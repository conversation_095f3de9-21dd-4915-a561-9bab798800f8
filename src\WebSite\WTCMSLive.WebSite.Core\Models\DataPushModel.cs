﻿using System.Xml.Serialization;

namespace WTCMSLive.WebSite.Models
{
    public class DataPushModel
    {
    }

    public class FtpConfig
    {
        [XmlElement(ElementName = "ServerIP")]
        public string ServerIP { get; set; }
        [XmlElement(ElementName = "ServerPort")]
        public string ServerPort { get; set; }
        [XmlElement(ElementName = "UserName")]
        public string UserName { get; set; }
        [XmlElement(ElementName = "Password")]
        public string Password { get; set; }
        [XmlElement(ElementName = "BaseServerDirectory")]
        public string BaseServerDirectory { get; set; }
    }


    public class GoldWind2023Config
    {
        public string OS { get; set; }
        public string Directory { get; set; }
        public string GW_Directory_CVM { get; set; }
        public string GW_Directory_Blade { get; set; }
        public string GW_Directory_Tower { get; set; }
        public string HardDiskName { get; set; }
        public int? LongWaveDef_CVM { get; set; }
        public int? LongWaveDef_BVM { get; set; }
        public int? LongWaveDef_TVM { get; set; }
        public string LastHardDiskSpace { get; set; }
    }


    public class PushDataMapConfig
    {
        public List<WindParkMapConfig> WindParkMap { get; set; }
        public List<WindTurbineMapConfig> WindTurbineMap { get; set; }
    }


    public class WindParkMapConfig
    {
        public string WindParkName { get; set; }
        public string WindParkID { get; set; }
        public string TargetWindParkID { get; set; }
        public string TargetWindTurbineID { get; set; }
    }

    public class WindTurbineMapConfig
    {
        public string WindTurbineID { get; set; }
        public string TargetWindTurbineID { get; set; }

        public string TargetWindTurbineName { get; set; }
    }
}