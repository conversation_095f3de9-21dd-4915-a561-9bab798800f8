﻿using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MySqlX.XDevAPI;
using System.Diagnostics;
using System.Web;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Models;
using System.Security.Claims;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Swashbuckle.AspNetCore.Annotations;
using WTCMSLive.WebSite.Core.Helpers;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HomeController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public HomeController(IConfiguration configuration)
        {
            _configuration = configuration;
        }


        #region 系统日志

        /// <summary>
        /// 系统日志类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetSystemLogType")]
        public IActionResult GetSystemLogType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumLogType value in Enum.GetValues(typeof(EnumLogType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }


        /// <summary>
        /// 获取系统日志信息
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="logType"></param>
        /// <returns></returns>
        /// 
        [HttpGet("systemLog")]
        public IActionResult ShowSystemLog(string beginTime, string endTime, int logType)
        {
            try
            {
                LogQueryCondition _logQC = new LogQueryCondition();
                _logQC.StartTime = Convert.ToDateTime(beginTime);
                _logQC.EndTime = Convert.ToDateTime(endTime);
                _logQC.LogDB = logType;
                List<SystemRunningLog> logList = LogManagement.GetSystemRunningLogList(_logQC);

                return Ok(logList);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[ShowSystemLog]系统日志", ex);
            }
            return Ok(new List<SystemRunningLog>());
        }

       
        #endregion 系统日志
 
      
        /// <summary>
        /// 登录接口，jwt token
        /// </summary>
        /// <param name="account"></param>
        /// <param name="password"></param>
        /// <param name="remember"></param>
        /// <returns></returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromForm] string account, [FromForm] string password)
        {
            try
            {
                if(string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password))
                {
                    return Ok(ApiResponse<LoginResponse>.Error("请输出正确的账号密码！"));
                }


                User user = UserManagement.Login(account, password);
                if (user == null)
                {
                    return Ok(ApiResponse<LoginResponse>.Error("登录失败，账号或密码错误！"));
                }

                // 获取用户的角色和模块信息
                var userRole = RoleModuleHelper.GetUserRoleAndModules(user.UserID);

                var jwtSettings = _configuration.GetSection("JwtSettings").Get<JwtSettings>();

                // 使用JwtHelper生成Token
                var tokenString = JwtHelper.GenerateToken(account, user.UserID.ToString(), userRole.RoleName, jwtSettings);

                var loginResponse = new LoginResponse
                {
                    Token = tokenString,
                    UserName = user.UserName,
                    UserId = user.UserID.ToString(),
                    Role = userRole.RoleName, // 使用实际的角色名称
                    Expiration = DateTime.UtcNow.AddMinutes(jwtSettings.ExpirationInMinutes),
                    UserRole = userRole, // 添加完整的角色信息
                    
                };

                return Ok(ApiResponse<LoginResponse>.Success(loginResponse, "登录成功！"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[Login]登录失败", ex);
                return Ok(ApiResponse<LoginResponse>.Error("登录错误，请检查系统日志！"));
            }
        }

        /// <summary>
        /// JWT Token续签接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("refreshToken")]
        [Authorize]
        public async Task<IActionResult> RefreshToken()
        {
            try
            {
                // 从Authorization头获取当前Token
                var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Ok(ApiResponse<RefreshTokenResponse>.Error("无效的Authorization头"));
                }

                var currentToken = authHeader.Substring("Bearer ".Length).Trim();
                var jwtSettings = _configuration.GetSection("JwtSettings").Get<JwtSettings>();

                // 验证当前Token
                var (isValid, principal, errorMessage) = JwtHelper.ValidateToken(currentToken, jwtSettings);
                if (!isValid)
                {
                    return Ok(ApiResponse<RefreshTokenResponse>.Error($"Token验证失败: {errorMessage}"));
                }

                // 提取用户信息
                var (account, userId, role) = JwtHelper.ExtractUserInfo(principal);
                if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(userId))
                {
                    return Ok(ApiResponse<RefreshTokenResponse>.Error("Token中缺少必要的用户信息"));
                }

                // 验证用户是否仍然有效（可选：检查用户是否被禁用等）
                // 这里可以添加额外的用户状态检查逻辑

                // 生成新的Token
                var newToken = JwtHelper.GenerateToken(account, userId, role, jwtSettings);
                var newExpiration = DateTime.UtcNow.AddMinutes(jwtSettings.ExpirationInMinutes);

                var refreshResponse = new RefreshTokenResponse
                {
                    Token = newToken,
                    Expiration = newExpiration,
                    UserId = userId,
                    Role = role
                };

                return Ok(ApiResponse<RefreshTokenResponse>.Success(refreshResponse, "Token续签成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[RefreshToken]Token续签失败", ex);
                return Ok(ApiResponse<RefreshTokenResponse>.Error("Token续签错误，请检查系统日志"));
            }
        }

        /// <summary>
        /// 获取版本号
        /// </summary>
        /// <returns></returns>
        ///
        [AllowAnonymous]
        [HttpGet("version")]
        public string GetVersion()
        {
            return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
        }

        /// <summary>
        /// 设备树获取
        /// </summary>
        /// <returns></returns>
        [HttpGet("tree")]
        public ActionResult GetTree()
        {
            TreeManager treeManager = new TreeManager();
            List<TreeModel> treeList = new List<TreeModel>();
            try
            {
                treeList = treeManager.GetTreeModel();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetTree]取得左侧树菜单失败", ex);
            }
            return Ok(treeList);
        }
        
    }
}
