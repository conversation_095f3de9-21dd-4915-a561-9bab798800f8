﻿using CMSFramework.BusinessEntity;
using CMSFramework.DAUEntities;

namespace WTCMSLive.WebSite.Models
{
    public class TemplateTurbineModel:WindTurbine
    {
        /// <summary>
        /// 主控
        /// </summary>
        public MCS Mcs { get; set; }

        public List<MCSChannelStateParam> MCSChannelStateParamList { get; set; }

        public List<MCSChannelValueParam> MCSChannelValueParamList { get; set; }

        /// <summary>
        /// 采集单元
        /// </summary>

        public List<WindDAU> WindDAUList { get; set; }

        /// <summary>
        /// 测量定义
        /// </summary>
        public List<MeasDefinition> MeasDefinitionList { get; set; }

        public List<MeasDefinition_Ex> MeasDefinition_ExList { get; set; }

        /// <summary>
        /// 报警定义
        /// </summary>
        public List<AlarmDefinition> AlarmDefinitions { get; set; }





        //添加modbusUnit表
        public List<ModbusUnit> modbusDefslist { get; set; }
        //服务表
        public List<SerialServer> serialServerslist { get; set; }
        public List<SerialPortParam> serialPorts { get; set; }
         //timcalibration表添加
        public List<TimCalibration> timUnitList { get; set; }
      
        public List<ModbusDef> modbusDefs { get; set; }


        // svmunit
        public List<SVMUnit> svmuList { get; set; }
        public List<SVMRegister> svmRegisterList { get; set; }

        public List<MeasLoc_SVM> svmMeasDataList { get; set; }
         
        public List<WaveDef_SVM> waveDef_SVMsList { get; set; }


        //油液
        public List<OilUnit> oilUnitList { get; set; }

        public List<OilAnalyzeConfig> oilAnalyzeConfigs { get; set; }

        // 超声螺栓预警力
        public List<UltrasonicChannelConfig> ultrasonicList { get; set; }

        // sftp和高级配置，用于网关控制,dau对象中没定义，单独定义
        public List<DAUSftpConfig>  dauSftpConfigs { get; set; }
        public List<DauExtendConfig> dauExtendConfigs { get; set; }
    }
}