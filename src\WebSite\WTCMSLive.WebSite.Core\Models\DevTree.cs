﻿using AppFramework.Utility;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using CMSFramework.DAUEntities;

namespace WTCMSLive.WebSite.Models
{
    public class DevTree
    {

        public static void DeleteTurbineByID(string ID)
        {
            WindTurbine windTurbine = DevTreeManagement.GetWindTurbine(ID);
            //Modified by wjy 2021/08/18
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                //删除mdfmodbus
                List<ModbusDef> defmodbuslist = ctx.ModbusDefs.Where(item => item.WindTurbineID == ID).ToList();
                if (defmodbuslist.Count > 0)
                {

                    ctx.ModbusDefs.RemoveRange(defmodbuslist);
                }

                var modbuswave = ctx.WDFModbusDefs.Where(item=>item.WindTurbineID == ID).ToList();
                if (modbuswave.Count > 0)
                {
                    ctx.WDFModbusDefs.RemoveRange(modbuswave);
                }

                // 删除measSolution
                List<MeasSolution> measSolution = ctx.MeasSolutions.Where(t => t.WindTurbineID == ID).ToList();
                if (measSolution.Count > 0)
                {
                    ctx.MeasSolutions.RemoveRange(measSolution);
                }

                ctx.SaveChanges();
            }

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var modbuschannel = ctx.ModbusChannelList.Where(t=>t.WindTurbineID == ID).ToList(); 
                if(modbuschannel.Count > 0)
                {
                    ctx.ModbusChannelList.RemoveRange(modbuschannel);
                    ctx.SaveChanges();
                }

                // 删除modbusunit
                List<ModbusUnit> modbusUnitlist = ctx.ModbusUnits.Where(i => i.WindTurbineID == ID).ToList();
                if (modbusUnitlist.Count > 0)
                {
                    ctx.ModbusUnits.RemoveRange(modbusUnitlist);
                    ctx.SaveChanges();
                }
                //删除timcalibration
                List<TimCalibration> tcList = ctx.TimCalibrations.Where(i => i.WindTurbineID == ID).ToList();
                if (tcList.Count > 0)
                {
                    ctx.TimCalibrations.RemoveRange(tcList);
                    ctx.SaveChanges();
                }
                // 删除serialserver
                List<SerialServer> serialServerList = ctx.SerialServers.Where(i => i.WindTurbineID == ID).ToList();
                if (serialServerList.Count > 0)
                {
                    ctx.SerialServers.RemoveRange(serialServerList);
                    ctx.SaveChanges();
                }

                // 删除serialPortParams
                List<SerialPortParam> serialPortParams = ctx.SerialPorts.Where(i=>i.WindTurbineID == ID).ToList();
                if(serialPortParams.Count > 0)
                {
                    ctx.SerialPorts.RemoveRange(serialPortParams);
                    ctx.SaveChanges();
                }

                List<DAUSftpConfig> sFTPConfigs = ctx.DauSftpConfigs.Where(i => i.WindTurbineID == ID).ToList();
                if(sFTPConfigs.Count > 0)
                {
                    ctx.DauSftpConfigs.RemoveRange(sFTPConfigs);
                    ctx.SaveChanges();
                }
                List<DauExtendConfig> dauExtendConfigs = ctx.DauExtendConfigs.Where(i => i.WindTurbineID == ID).ToList();
                if(dauExtendConfigs.Count > 0)
                {
                    ctx.DauExtendConfigs.RemoveRange(dauExtendConfigs);
                    ctx.SaveChanges();
                }

                //删除OilUnit
                List<OilUnit> oilUnitList = ctx.OilUnits.Where(i => i.WindTurbineID == ID).ToList();
                if (oilUnitList != null)
                {
                    ctx.OilUnits.RemoveRange(oilUnitList);
                    ctx.SaveChanges();
                }
                List<UltrasonicChannelConfig> deleteUltrasonicList = ctx.UltrasonicChannelConfigs.Where(i => i.WindTurbineID == ID).ToList();
                if (deleteUltrasonicList != null)
                {
                    ctx.UltrasonicChannelConfigs.RemoveRange(deleteUltrasonicList);
                    ctx.SaveChanges();
                }

            }
            //删除ultrasonicchannelconfig
            DauManagement.DeleteUltrasonicChannel(ID);
            List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(ID);
            if (measDefList.Count > 0)
            {
                measDefList.ForEach(item =>
                MeasDefinitionManagement.DeleteMeasdefinition(item.WindTurbineID, item.MeasDefinitionID));
            }
            //WindDAU dau = DauManagement.GetDAUById(ID);
            //// Add by ZhangMai 2012-08-14 添加删除机组时同时删除关联的DAU
            //// Modified by SunQI 2020-05-23 同时删除多个关联DAU --- 时间已经过去8年了。。。
            //if (dau != null)
            //{
            //    DauManagement.DeleteDAU(ID);
            //}
            List<WindDAU> daulist = DAUSManageModel.GetDAUListById(ID);
            daulist.ForEach(item =>
            {
                DAUSManageModel.DeleteDAUByTrubineIdAndDauId(item.WindTurbineID, item.DauID);
            });

            SVMUnit svm = SVMManagement.GetSVMById(ID);
            //删除关联主控
            MCS mscSystem = DAUMCS.GetMCSByTurbineId(ID);
            if (mscSystem != null)
            {
                DAUMCS.DeleteMCSSystem(ID);
            }
            if (svm != null)
            {
                SVMManagement.DeleteSVM(ID, svm.ComponentID);
            }
            //删除报警定义
            AlarmDefinitionManage.DeleteAllAlarmDefinition(ID);
            DevTreeManagement.DeleteWindTurbine(ID);
        }

        public BaseTableModel GetDevtreelist(bool edit, string windParkID)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindTurbine> windTurbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkID);
            List<MCS> mcsList = DAUMCS.GetMCSByWindParkId(windParkID);
            List<WindTurbineModel> TurbineModel = DevTreeManagement.GetWindTurbineModelList();
            tableModel.tableName = "devList";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < windTurbineList.Count; i++)
            {
                Rows cells = new Rows();
                WindTurbine devWindTurbine = windTurbineList[i];
                cells.cells = CreateWindTurbineListTableCell(devWindTurbine, edit, mcsList.Find(item => item.WindTurbineID == devWindTurbine.WindTurbineID), TurbineModel.Find(item => item.TurbineModel == devWindTurbine.WindTurbineModel));
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        private Cell[] CreateWindTurbineListTableCell(WindTurbine devWindTurbine, bool edit,MCS mcs,WindTurbineModel WindModel)
        {

            List<Cell> cellList = new List<Cell>();
            //机组id
            Cell cell01 = new Cell();
            cell01.displayValue = devWindTurbine.WindTurbineID;  
            //机组名称
            Cell cell02 = new Cell();
            cell02.displayValue = devWindTurbine.WindTurbineName;       
            //机组编号
            Cell cell03 = new Cell();
            cell03.displayValue = devWindTurbine.WindTurbineCode;
            //机组型号  
            Cell cell04 = new Cell();
            cell04.displayValue = devWindTurbine.WindTurbineModel;
            //投运日期     
            Cell cell05 = new Cell();
            cell05.displayValue = devWindTurbine.OperationalDate.ToString("yyyy-MM-dd");
            //额定功率    
            Cell cell06 = new Cell();
            cell06.displayValue = WindModel?.RatedPower.ToString();
            //机组主控 ip   
            Cell cell07 = new Cell();
            if(mcs!=null)
                cell07.displayValue = mcs.MCSIP;

            // 发电机转速、并网转速
            Cell cell11 = new Cell();
            cell11.displayValue = WindModel?.RatedGeneratorSpeed.ToString();
            Cell cell12 = new Cell();
            cell12.displayValue = WindModel?.GridConnectedGeneratorSpeed.ToString();
            //编辑

            Cell cell09 = new Cell();
            cell09.type = "btn";
            cell09.displayValue = "编辑";
            cell09.onclick = "editDevtree('" + devWindTurbine.WindTurbineID + "')";
            cell09.style = "btn btn-primary btn-sm btnEdit";
            cell09.icon = "glyphicon glyphicon-edit";
          
           //删除
           Cell cell10 = new Cell();
            cell10.type = "btn";
            cell10.displayValue = " 删除";
            cell10.onclick = "deleteDevtree('" + devWindTurbine.WindTurbineID + "',this)";
            cell10.style = "btn btn-danger btn-sm btnDelete";
            cell10.icon = "glyphicon glyphicon-remove";
            cellList.Add(cell01);
            cellList.Add(cell02);
            cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            cellList.Add(cell06);
            cellList.Add(cell07);
            cellList.Add(cell11);
            cellList.Add(cell12);
            cellList.Add(cell09);
            cellList.Add(cell10);
            return cellList.ToArray();
        }
        public List<WindTurbine> GetTurbineDropDown()
        {
            List<WindTurbine> list = new List<WindTurbine>();
            List<WindTurbine> wind = DevTreeManagement.GetTurbinesList();
            foreach (WindTurbine w in wind)
            {
                WindTurbine winds = new WindTurbine();
                winds.WindTurbineID = w.WindTurbineID;
                winds.WindTurbineName = w.WindTurbineName;
                list.Add(winds);
            }
            return list;
        }

        #region 转速测量位置
        public BaseTableModel GetRotSpdMeasLocTable(string turbineID)
        {
            List<MeasLoc_RotSpd> rotSpdList = DevTreeManagement.GetRotSpdMeasLocListByTurId(turbineID);
            WindDAU dau = DauManagement.GetDAUById(turbineID);
            BaseTableModel tableModel = new BaseTableModel();
            //tableModel.tableName = "RotSpdMeas";
            tableModel.tableName = "MeasLoc_RotSpd_Table";
            List<Rows> rowsList = new List<Rows>();
            for (int i = 0; i < rotSpdList.Count; i++)
            {
                Rows row = new Rows();
                row.cells = CreateRotSpdMeasLocTableCell(rotSpdList[i], dau);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }
        private Cell[] CreateRotSpdMeasLocTableCell(MeasLoc_RotSpd measLoc_RotSpd,WindDAU dau)
        {
            List<Cell> cellList = new List<Cell>();
            //转速测量位置名称 	
            Cell cell0 = new Cell();
            cell0.displayValue = measLoc_RotSpd.MeasLocName;
            //变速比
            Cell cell1 = new Cell();
            cell1.displayValue = measLoc_RotSpd.GearRatio.ToString();
            //采集单元 	
            Cell cell2 = new Cell();
            if (dau == null)
            { cell2.displayValue = "未绑定"; }
            else
            {
                cell2.displayValue = dau.DAUName;
            }
            //采集通道 	
            Cell cell3 = new Cell();
            if (dau == null || dau.RotSpeedChannelList==null || dau.RotSpeedChannelList.Count==0)
            { cell3.displayValue = "未绑定"; }
            else
            {
                cell3.displayValue = dau.RotSpeedChannelList[0].ChannelNumber.ToString();
            }
            //编码器线数
            Cell cell4 = new Cell();
            cell4.displayValue = measLoc_RotSpd.LineCounts.ToString();
            //编辑
            Cell cell6 = new Cell();
            if (cell0!=null)
            {
                cell6.type = "btn";
                cell6.displayValue = "编辑";
                cell6.onclick = "editRotSpdMeas('" + measLoc_RotSpd.MeasLocName + "')";
                cell6.style = "btn btn-primary btn-sm";
            }
            else { cell6.displayValue = "-"; }
            //机组id
            Cell cell7 = new Cell();
            cell7.displayValue = measLoc_RotSpd.WindTurbineID.ToString();
            //测量位置id
            Cell cell8 = new Cell();
            cell8.displayValue = measLoc_RotSpd.MeasLocationID.ToString();

            cellList.Add(cell0);
            cellList.Add(cell1);
            cellList.Add(cell2);
            cellList.Add(cell3);
            cellList.Add(cell4);
            cellList.Add(cell6);
            cellList.Add(cell7);
            cellList.Add(cell8);
            return cellList.ToArray();
        }
        #endregion 转速测量位置

        #region 工况测量位置

        /// <summary>
        /// 工况排序的列表模版
        /// </summary>
        /// <returns></returns>
        public static List<string> GetMeasLocProcessOrderSeqList()
        {
            List<string> list = new List<string>();
            foreach (KeyValuePair<int, string> data in EnumWorkCondParamTypeHelper.GetParamTypeList())
            {
                if (data.Key != 99)
                {
                    list.Add(data.Value);
                }
            }
            return list;
        }

        public BaseTableModel GetWorkCondMeasLocTable(string turbineID ,bool edit)
        {
            List<MeasLoc_Process> workCondMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
            List<MeasLoc_Process> workCondMeasLocListOrder = workCondMeasLocList;//new List<MeasLoc_Process>();
            WindDAU dau = DauManagement.GetDAUById(turbineID);
            List<MCSChannel> mcsChannelList = DAUMCS.GetMCSChannelList(turbineID);
            //foreach (string processName in GetMeasLocProcessOrderSeqList())
            //{
            //    MeasLoc_Process process = workCondMeasLocList.Find(item => item.MeasLocName == processName);
            //    if (process != null)
            //    {
            //        workCondMeasLocListOrder.Add(process);
            //    }
            //}
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "WorkCondMeasLoc";
            List<Rows> rowsList = new List<Rows>();
            for (int i = 0; i < workCondMeasLocListOrder.Count; i++)
            {
                Rows row = new Rows();
                row.cells = CreateWorkCondMeasLocTableCell(workCondMeasLocListOrder[i], dau, mcsChannelList, edit);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;


        }
        private Cell[] CreateWorkCondMeasLocTableCell(MeasLoc_Process measLoc_Process,WindDAU dau ,List<MCSChannel> mcsChannelList, bool edit)
        {
            DAUChannel_Process process =null;
            if (dau != null && dau.ProcessChannelList != null && dau.ProcessChannelList.Count > 0)
            {
                process = dau.ProcessChannelList.Find(item => item.MeasLoc_ProcessId == measLoc_Process.MeasLocationID);
            }
            List<Cell> cellList = new List<Cell>();
            //工况测量位置名称 	
            Cell cell0 = new Cell();
            cell0.displayValue = measLoc_Process.MeasLocName;
            cellList.Add(cell0);
            //工况参数 	
            Cell cell1 = new Cell();
            cell1.displayValue = AppFramework.Utility.EnumHelper.GetDescription(measLoc_Process.Param_Type_Code);
            cellList.Add(cell1);
            //数据来源 	
            Cell cell2 = new Cell();
            cell2.displayValue = EnumHelper.GetDescription(measLoc_Process.FieldBusType);
            cellList.Add(cell2);
            //通道编号
            Cell cell3 = new Cell();
            if (measLoc_Process.FieldBusType == EnumWorkConDataSource.WindDAU)
            {
                cell3.displayValue = process == null ? "未绑定" : process.ChannelNumber.ToString();
            }
            else
            {
                MCSChannel mcsChannel = mcsChannelList.Find(item => item.MeasLocProcessID == measLoc_Process.MeasLocationID);
                cell3.displayValue = mcsChannel == null ? "未绑定" : mcsChannel.ChannelNumber;
            }
            cellList.Add(cell3);
            //机组id
            Cell cell4 = new Cell();
            cell4.displayValue = measLoc_Process.WindTurbineID;
            cellList.Add(cell4);
            //编辑
            Cell cell5 = new Cell();
            cell5.type = "btn";
            cell5.displayValue = "编辑";
            cell5.onclick = "editWorkCondMeasLoc(this," + "'" + measLoc_Process.MeasLocationID + "'," + (cell3.displayValue == "未绑定" ? 1 : 0) + ")";
            cell5.style = "btn btn-primary btn-sm btnEdit";
            cellList.Add(cell5);
            //删除
            Cell cell6 = new Cell();
            cell6.type = "btn";
            cell6.displayValue = "删除";
            cell6.onclick = "deleteWorkCondMeasLoc('" + measLoc_Process.WindTurbineID + "','" + measLoc_Process.MeasLocationID + "',this," + (cell3.displayValue == "未绑定" ? 1 : 0) + ")";
            cell6.style = "btn btn-danger btn-sm btnDelete";
            cellList.Add(cell6);
            return cellList.ToArray();
        }
        #endregion 工况测量位置
    }
}