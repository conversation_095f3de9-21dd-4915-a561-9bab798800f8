namespace WTCMSLive.WebSite.Core.DTOs
{
    public class DAUDeleteDTO
    {
        public string WindTurbineID { get; set; }
        public string WindParkID { get; set; }
        public string DauID { get; set; }
    }

    /// <summary>
    /// 批量删除振动通道DTO
    /// </summary>
    public class BatchDeleteVibChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<VibChannelDeleteItem> Channels { get; set; }
    }

    /// <summary>
    /// 振动通道删除项
    /// </summary>
    public class VibChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// 批量删除电流电压通道DTO
    /// </summary>
    public class BatchDeleteProcessChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<ProcessChannelDeleteItem> Channels { get; set; }
    }

    /// <summary>
    /// 电流电压通道删除项
    /// </summary>
    public class ProcessChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// 批量添加振动通道DTO
    /// </summary>
    public class BatchAddVibChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<VibChannelAddItem> Channels { get; set; }
    }

    /// <summary>
    /// 振动通道添加项
    /// </summary>
    public class VibChannelAddItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocVibID { get; set; }
        public double Coeff_a { get; set; }
        public float MinBiasVolt { get; set; }
        public float MaxBiasVolt { get; set; }
        public double? PhysicalQuantityType { get; set; }
        public double? S2S_Coeff_a { get; set; }
        public double? S2S_Coeff_b { get; set; }
        public short? RegisterAddress { get; set; }
        public float? Coeff_L0 { get; set; }
    }

    /// <summary>
    /// 批量添加电流电压通道DTO
    /// </summary>
    public class BatchAddProcessChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<ProcessChannelAddItem> Channels { get; set; }
    }

    /// <summary>
    /// 电流电压通道添加项
    /// </summary>
    public class ProcessChannelAddItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLoc_ProcessId { get; set; }
        public double Coeff_a { get; set; }
        public double Coeff_b { get; set; }
    }

    /// <summary>
    /// 单个振动通道编辑DTO
    /// </summary>
    public class EditVibChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public int ChannelNumber { get; set; }
        public string MeasLocVibID { get; set; }
        public double Coeff_a { get; set; }
        public float MinBiasVolt { get; set; }
        public float MaxBiasVolt { get; set; }
        public double? PhysicalQuantityType { get; set; }
        public double? S2S_Coeff_a { get; set; }
        public double? S2S_Coeff_b { get; set; }
        public short? RegisterAddress { get; set; }
        public float? Coeff_L0 { get; set; }
    }

    /// <summary>
    /// 单个电流电压通道编辑DTO
    /// </summary>
    public class EditProcessChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public int ChannelNumber { get; set; }
        public string MeasLoc_ProcessId { get; set; }
        public double Coeff_a { get; set; }
        public double Coeff_b { get; set; }
    }

    /// <summary>
    /// 批量删除工况通道DTO
    /// </summary>
    public class BatchDeleteWorkConditionChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<WorkConditionChannelDeleteItem> Channels { get; set; }
    }

    /// <summary>
    /// 工况通道删除项
    /// </summary>
    public class WorkConditionChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// 批量添加工况通道DTO
    /// </summary>
    public class BatchAddWorkConditionChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<WorkConditionChannelAddItem> Channels { get; set; }
    }

    /// <summary>
    /// 工况通道添加项
    /// </summary>
    public class WorkConditionChannelAddItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLoc_ProcessId { get; set; }
        public float UpperLimitFreqency { get; set; }
        public int SampleLength { get; set; }
        public float PowerCoeff_a { get; set; }
        public float PowerCoeff_b { get; set; }
    }

    /// <summary>
    /// 单个工况通道编辑DTO
    /// </summary>
    public class EditWorkConditionChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public int ChannelNumber { get; set; }
        public string MeasLoc_ProcessId { get; set; }
        public float UpperLimitFreqency { get; set; }
        public int SampleLength { get; set; }
        public float PowerCoeff_a { get; set; }
        public float PowerCoeff_b { get; set; }
    }

    /// <summary>
    /// 批量删除转速通道DTO
    /// </summary>
    public class BatchDeleteRotSpeedChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<RotSpeedChannelDeleteItem> Channels { get; set; }
    }

    /// <summary>
    /// 转速通道删除项
    /// </summary>
    public class RotSpeedChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// 批量添加转速通道DTO
    /// </summary>
    public class BatchAddRotSpeedChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public List<RotSpeedChannelAddItem> Channels { get; set; }
    }

    /// <summary>
    /// 转速通道添加项
    /// </summary>
    public class RotSpeedChannelAddItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocRotSpdID { get; set; }
    }

    /// <summary>
    /// 单个转速通道编辑DTO
    /// </summary>
    public class EditRotSpeedChannelDTO
    {
        public string WindTurbineID { get; set; }
        public string DauID { get; set; }
        public int ChannelNumber { get; set; }
        public string MeasLocRotSpdID { get; set; }
    }
}