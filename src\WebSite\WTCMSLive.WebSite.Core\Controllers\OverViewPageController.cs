﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class OverViewController : ControllerBase
    {
        /// <summary>
        /// 获取设备树
        /// </summary>
        /// <returns></returns>
        [HttpGet("tree")]
        public ActionResult GetTree(string? parkID = null)
        {
            TreeManager treeManager = new();
            return Ok(treeManager.GetTreeModel(parkID));
        }


        /// <summary>
        /// 获取风场列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("parkList")]
        public ActionResult<List<WindPark>> GetParkList()
        {
            List<WindPark> parkList = BusinessModel.DevTreeManagement.GetWindParkList();

            // 排除模板风场
            parkList.RemoveAll(t => t.WindParkID.Equals("HN999"));
            return parkList;
        }

        /// <summary>
        /// 获取机组状态
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        /// 
        [HttpGet("TurbineStatusList")]
        private List<DataModel> GetTurbineStatusList(string windParkID,out List<FiterModel> fiterList)
        {
            List<DataModel> modelList = new List<DataModel>();
            fiterList = new List<FiterModel>();
            List<AlarmStatus_Turbine> statusList = new List<AlarmStatus_Turbine>();
            List<AlarmStatus_Turbine> statusOrderList = DevRTStateManagement.GetTurRTAlarmStatusList(windParkID);
            WindPark windpark = DevTreeManagement.GetWindPark(windParkID);
            List<WindTurbine> list = windpark.WindTurbineList;
            //添加机组状态列表排序规则（按机组编号排序）
            list.ForEach(item =>
            {
                var turbineState = statusOrderList.Find(s => s.WindTurbineID == item.WindTurbineID);
                if (turbineState != null)
                    statusList.Add(turbineState);
            });
            FiterModel fiterSuccess = new FiterModel();
            fiterSuccess.name = "正常";
            fiterSuccess.count = 0;
            fiterSuccess.id = "_Success";
            fiterSuccess.className = "btn btn-success";

            FiterModel systemError = new FiterModel();
            systemError.name = "系统异常";
            systemError.id = "_primary";
            systemError.className = "btn btn-primary";

            FiterModel fiterWaring = new FiterModel();
            fiterWaring.name = "注意";
            fiterWaring.count = 0;
            fiterWaring.id = "_warning";
            fiterWaring.className = "btn btn-warning";

            FiterModel fiterError = new FiterModel();
            fiterError.name = "危险";
            fiterError.count = 0;
            fiterError.id = "_danger";
            fiterError.className = "btn btn-danger";

            FiterModel fiterUnknow = new FiterModel();
            fiterUnknow.name = "未知";
            fiterUnknow.count = 0;
            fiterUnknow.id = "_default";
            fiterUnknow.className = "btn btn-unknow";

            FiterModel fiterDamage = new FiterModel
            {
                name = "损伤",
                count = 0,
                id = "_damage",
                className = "btn btn-info"
            };

            systemError.count = 0;
            for (int i = 0; i < statusList.Count; i++)
            {
                DataModel model = new DataModel();
                model.name = windpark.WindTurbineList.Find(item => item.WindTurbineID == statusList[i].DevSegmentID).WindTurbineName;
                model.id = statusList[i].DevSegmentID;
                model.level = ((int)statusList[i].AlarmDegree).ToString();
                switch ((int)statusList[i].AlarmDegree)
                {
                    case 2:
                        ++fiterUnknow.count;
                        break;
                    case 3:
                        ++fiterSuccess.count;
                        break;
                    case 4:
                        ++systemError.count;
                        break;
                    case 5:
                        ++fiterWaring.count;
                        break;
                    case 6:
                        ++fiterError.count;
                        break;
                    case 10:
                        ++fiterDamage.count;
                        break;
                    default:
                        break;
                }
                modelList.Add(model);
            }
            fiterList.Add(fiterUnknow);
            fiterList.Add(fiterSuccess);
            fiterList.Add(systemError);
            fiterList.Add(fiterWaring);
            fiterList.Add(fiterError);
            fiterList.Add(fiterDamage);
            //移除未出现的状态
            fiterList.RemoveAll(item => item.count == 0);
            return modelList;
        }



        /// <summary>
        /// 获取风场状态统计
        /// </summary>
        /// <returns>风场和机组状态统计信息</returns>
        [HttpGet("GetParkStatusCount")]
        public IActionResult GetParkStatusCount()
        {
            try
            {
                // 获取基础数据
                List<WindPark> dataList = DevTreeManagement.GetWindParkList();
                List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAList();

                // 创建返回对象
                var result = new ParkStatusCountDTO
                {
                    TotalParkCount = dataList.Count,
                    TotalTurbineStatus = new TurbineStatusSummaryDTO(),
                    ParkDetailList = new List<ParkDetailStatusDTO>(),
                    StatisticsTime = DateTime.Now
                };

                // 统计所有机组的总数和状态
                var allTurbines = new List<WindTurbine>();
                foreach (var park in dataList)
                {
                    var parkTurbines = DevTreeManagement.GetTurbinesListByWindParkId(park.WindParkID);
                    allTurbines.AddRange(parkTurbines);
                }

                // 计算总体机组状态统计
                result.TotalTurbineStatus = CalculateTurbineStatusSummary(allTurbines, statusRTList);

                // 计算各风场详细统计
                foreach (var park in dataList)
                {
                    var parkDetail = CalculateParkDetailStatus(park, statusRTList);
                    result.ParkDetailList.Add(parkDetail);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetParkStatusCount]获取风场状态统计失败", ex);
                return Ok(new ParkStatusCountDTO
                {
                    TotalParkCount = 0,
                    TotalTurbineStatus = new TurbineStatusSummaryDTO(),
                    ParkDetailList = new List<ParkDetailStatusDTO>(),
                    StatisticsTime = DateTime.Now
                });
            }
        }


        /// <summary>
        /// 获取指定风场的设备状态统计
        /// </summary>
        /// <param name="WindParkID">风场ID</param>
        /// <returns>风场设备状态统计信息</returns>
        [HttpGet("GetDevStatusCount")]
        public IActionResult GetDevStatusCount(string WindParkID)
        {
            try
            {
                if (string.IsNullOrEmpty(WindParkID))
                {
                    return BadRequest("风场ID不能为空");
                }

                // 获取基础数据
                List<WindTurbine> turbineList = DevTreeManagement.GetTurbinesListByWindParkId(WindParkID);
                List<AlarmStatus_Turbine> statusRTList = DevRTStateManagement.GetTurRTAlarmStatusList(WindParkID);
                List<AlarmStatus_Component> compList = DevRTStateManagement.GetTurComRTStateListWindPark(WindParkID);

                // 获取风场信息
                WindPark windPark = DevTreeManagement.GetWindPark(WindParkID);
                if (windPark == null)
                {
                    return NotFound($"未找到风场ID为 {WindParkID} 的风场");
                }

                // 创建返回对象
                var result = new DevStatusCountDTO
                {
                    WindParkID = WindParkID,
                    WindParkName = windPark.WindParkName,
                    TurbineStatusSummary = CalculateTurbineStatusSummary(turbineList, statusRTList),
                    TurbineDetailList = new List<TurbineWithComponentsDTO>(),
                    StatisticsTime = DateTime.Now
                };

                // 生成机组详细列表（包含部件信息）
                foreach (var turbine in turbineList)
                {
                    var turbineDetail = CreateTurbineWithComponents(turbine, statusRTList, compList);
                    result.TurbineDetailList.Add(turbineDetail);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetDevStatusCount]获取风场 {WindParkID} 设备状态统计失败", ex);
                return Ok(new DevStatusCountDTO
                {
                    WindParkID = WindParkID ?? "",
                    WindParkName = "获取失败",
                    TurbineStatusSummary = new TurbineStatusSummaryDTO(),
                    TurbineDetailList = new List<TurbineWithComponentsDTO>(),
                    StatisticsTime = DateTime.Now
                });
            }
        }


        /// <summary>
        /// 获取指定风场的设备状态统计
        /// </summary>
        /// <param name="WindParkID">风场ID</param>
        /// <returns>风场设备状态统计信息</returns>
        [HttpGet("GetTurbineStatusCount")]
        public IActionResult GetTurbineStatusCount(string WindParkID,string TurbineID)
        {
            try
            {
                if (string.IsNullOrEmpty(TurbineID))
                {
                    return BadRequest("机组ID不能为空");
                }

                List<WindTurbineComponent> windTurbineComponent = null;
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    windTurbineComponent = ctx.DevTurComponents.Where(t=>t.WindTurbineID == TurbineID).ToList();
                }

                List<AlarmStatus_Component> alarmStatusList = new List<AlarmStatus_Component>() ;
                using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
                {
                    alarmStatusList = ctx.AlarmStatus_Components.Where(p => p.WindTurbineID == TurbineID).ToList();
                    if (windTurbineComponent != null && windTurbineComponent.Count > 0)
                    {
                        alarmStatusList.ForEach(item =>
                        {
                            item.DevSegmentName = windTurbineComponent.Find(com => com.ComponentID == item.ComponentID).ComponentName;
                        });
                    }
                }

                return Ok(alarmStatusList);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetDevStatusCount]获取风场 {WindParkID} 设备状态统计失败", ex);
                return Ok(new List<AlarmStatus_Component>());
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 计算机组状态汇总
        /// </summary>
        /// <param name="turbines">机组列表</param>
        /// <param name="statusList">状态列表</param>
        /// <returns>机组状态汇总</returns>
        private TurbineStatusSummaryDTO CalculateTurbineStatusSummary(List<WindTurbine> turbines, List<AlarmStatus_Turbine> statusList)
        {
            var summary = new TurbineStatusSummaryDTO
            {
                TotalCount = turbines.Count
            };

            foreach (var turbine in turbines)
            {
                var status = statusList.FirstOrDefault(s => s.DevSegmentID == turbine.WindTurbineID);
                var statusValue = status?.AlarmDegree ?? EnumAlarmDegree.AlarmDeg_Normal; // 默认为3（正常）

                switch ((int)statusValue)
                {
                    case 3: // 正常
                        summary.NormalCount++;
                        break;
                    case 5: // 注意
                        summary.WarningCount++;
                        break;
                    case 6: // 危险
                        summary.DangerCount++;
                        break;
                    default: // 其他状态
                        summary.OtherCount++;
                        break;
                }
            }

            // 计算百分比
            if (summary.TotalCount > 0)
            {
                summary.NormalPercentage = Math.Round((double)summary.NormalCount / summary.TotalCount * 100, 2);
                summary.WarningPercentage = Math.Round((double)summary.WarningCount / summary.TotalCount * 100, 2);
                summary.DangerPercentage = Math.Round((double)summary.DangerCount / summary.TotalCount * 100, 2);
                summary.OtherPercentage = Math.Round((double)summary.OtherCount / summary.TotalCount * 100, 2);
            }

            return summary;
        }

        /// <summary>
        /// 计算风场详细状态
        /// </summary>
        /// <param name="park">风场信息</param>
        /// <param name="statusList">状态列表</param>
        /// <returns>风场详细状态</returns>
        private ParkDetailStatusDTO CalculateParkDetailStatus(WindPark park, List<AlarmStatus_Turbine> statusList)
        {
            var parkDetail = new ParkDetailStatusDTO
            {
                WindParkID = park.WindParkID,
                WindParkName = park.WindParkName,
                WindParkCode = park.WindParkCode,
                TurbineDetailList = new List<TurbineStatusDetailDTO>()
            };

            // 获取该风场下的机组列表
            var parkTurbines = DevTreeManagement.GetTurbinesListByWindParkId(park.WindParkID);

            // 计算该风场的机组状态汇总
            parkDetail.TurbineStatus = CalculateTurbineStatusSummary(parkTurbines, statusList);

            // 生成机组详细列表
            foreach (var turbine in parkTurbines)
            {
                var status = statusList.FirstOrDefault(s => s.DevSegmentID == turbine.WindTurbineID);
                var statusValue = (int)(status?.AlarmDegree ?? EnumAlarmDegree.AlarmDeg_Normal); // 默认为3（正常）

                var turbineDetail = new TurbineStatusDetailDTO
                {
                    WindTurbineID = turbine.WindTurbineID,
                    WindTurbineName = turbine.WindTurbineName,
                    Status = statusValue,
                    //StatusDescription = GetStatusDescription(statusValue),
                    StatusUpdateTime = status?.AlarmUpdateTime
                };

                parkDetail.TurbineDetailList.Add(turbineDetail);
            }

            // 按机组名称排序
            parkDetail.TurbineDetailList = parkDetail.TurbineDetailList
                .OrderBy(t => t.WindTurbineName)
                .ToList();

            return parkDetail;
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态描述</returns>
        private string GetStatusDescription(int status)
        {
            return status switch
            {
                2 => "未知",
                3 => "正常",
                4 => "系统异常",
                5 => "注意",
                6 => "危险",
                10 => "损伤",
                _ => "其他"
            };
        }

        /// <summary>
        /// 创建机组及其部件信息
        /// </summary>
        /// <param name="turbine">机组信息</param>
        /// <param name="turbineStatusList">机组状态列表</param>
        /// <param name="componentStatusList">部件状态列表</param>
        /// <returns>机组及其部件状态信息</returns>
        private TurbineWithComponentsDTO CreateTurbineWithComponents(
            WindTurbine turbine,
            List<AlarmStatus_Turbine> turbineStatusList,
            List<AlarmStatus_Component> componentStatusList)
        {
            // 获取机组状态
            var turbineStatus = turbineStatusList.FirstOrDefault(s => s.DevSegmentID == turbine.WindTurbineID);
            var turbineStatusValue = (int)(turbineStatus?.AlarmDegree ?? EnumAlarmDegree.AlarmDeg_Normal); // 默认为3（正常）

            var turbineWithComponents = new TurbineWithComponentsDTO
            {
                WindTurbineID = turbine.WindTurbineID,
                WindTurbineName = turbine.WindTurbineName,
                TurbineStatus = turbineStatusValue,
                //TurbineStatusDescription = GetStatusDescription(turbineStatusValue),
                TurbineStatusUpdateTime = turbineStatus?.AlarmUpdateTime,
                ComponentList = new List<ComponentStatusDTO>()
            };

            // 获取该机组下的所有部件状态
            var turbineComponents = componentStatusList
                .Where(c => c.WindTurbineID == turbine.WindTurbineID)
                .OrderBy(c => c.ComponentID)
                .ToList();

            foreach (var component in turbineComponents)
            {
                var componentStatus = new ComponentStatusDTO
                {
                    ComponentID = component.ComponentID,
                    ComponentName = component.DevSegmentName ?? component.ComponentID,
                    Status = (int)component.AlarmDegree,
                    //StatusDescription = GetStatusDescription((int)component.AlarmDegree),
                    StatusUpdateTime = component.AlarmUpdateTime,
                    WindTurbineID = component.WindTurbineID
                };

                turbineWithComponents.ComponentList.Add(componentStatus);
            }

            return turbineWithComponents;
        }

        #endregion

    }
}
