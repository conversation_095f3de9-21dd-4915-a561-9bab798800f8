﻿using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Models;
using AppFramework.IDUtility;
using CMSFramework.Logger;
using CMSFramework.EF;
using Microsoft.EntityFrameworkCore;
using WTCMSLive.WebSite.Models;
using System.Diagnostics;
using System.Runtime.InteropServices;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class GatewayController : ControllerBase
    {
        private readonly ILogger<GatewayController> _logger;

        public GatewayController(
            ILogger<GatewayController> logger)
        {
            _logger = logger;
        }

        #region 设备配置
        /// <summary>
        /// 获取Gateway机组设备信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDevice")]
        public IActionResult GetDevice()
        {
            try
            {
                List<WindPark> windParkList = DevTreeManagement.GetWindParkList();
                var windPark = windParkList.FirstOrDefault();

                if (windPark == null)
                {
                    return Ok(ApiResponse<GatewayDeviceDTO>.Success(null, "暂无设备信息"));
                }

                // 获取风场下的机组信息
                var windTurbine = windPark.WindTurbineList?.FirstOrDefault();

                // 获取主控系统信息
                MCS mcs = null;
                if (windTurbine != null)
                {
                    mcs = DAUMCS.GetMCSByTurbineId(windTurbine.WindTurbineID);
                }

                var result = new GatewayDeviceDTO
                {
                    WindParkID = windPark.WindParkID,
                    WindParkName = windPark.WindParkName,
                    WindParkCode = windPark.WindParkCode,
                    OperationalDate = windPark.OperationalDate.ToString("yyyy-MM-dd"),
                    ContactMan = windPark.ContactMan,
                    ContactTel = windPark.ContactTel,
                    Address = windPark.Address,
                    PostCode = windPark.PostCode,
                    Description = windPark.Description,
                    Country = windPark.Country,
                    Area = windPark.Area,
                    Location = windPark.location,
                    WindTurbineID = windTurbine?.WindTurbineID,
                    WindTurbineName = windTurbine?.WindTurbineName,
                    WindTurbineCode = windTurbine?.WindTurbineCode,
                    WindTurbineModel = windTurbine?.WindTurbineModel,
                    MinWorkingRotSpeed = windTurbine?.MinWorkingRotSpeed,
                    McsIP = mcs?.MCSIP
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[GetDevice]获取Gateway设备信息失败", ex);
                return Ok(new GatewayDeviceDTO());
            }
        }

        /// <summary>
        /// 添加Gateway机组设备
        /// </summary>
        /// <param name="dto">机组设备信息</param>
        /// <returns></returns>
        [HttpPost("AddDevice")]
        public IActionResult AddDevice([FromBody] GatewayDeviceDTO dto)
        {
            try
            {
                if (dto == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkName) || string.IsNullOrEmpty(dto.WindTurbineName))
                {
                    return Ok(ApiResponse<string>.Error("风场名称和机组名称不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkCode) || string.IsNullOrEmpty(dto.WindTurbineCode))
                {
                    return Ok(ApiResponse<string>.Error("风场编码和机组编码不能为空"));
                }

                if (string.IsNullOrEmpty(dto.ComponentIds))
                {
                    return Ok(ApiResponse<string>.Error("机组部件信息不能为空"));
                }

                // 检查是否已存在风场和机组
                List<WindPark> existingWindParks = DevTreeManagement.GetWindParkList();
                var existingPark = existingWindParks.FirstOrDefault(p => p.WindParkName == dto.WindParkName);

                if (existingPark != null)
                {
                    return Ok(ApiResponse<string>.Error("风场名称已存在"));
                }

                var existingParkByCode = existingWindParks.FirstOrDefault(p => p.WindParkCode == dto.WindParkCode.PadLeft(3, '0'));
                if (existingParkByCode != null)
                {
                    return Ok(ApiResponse<string>.Error("风场编码已存在"));
                }

                // 构建风场信息
                WindPark windPark = new WindPark();
                windPark.WindParkName = dto.WindParkName;
                windPark.WindParkCode = int.Parse(dto.WindParkCode).ToString("000");
                windPark.OperationalDate = DateTime.Parse(dto.OperationalDate ?? DateTime.Now.ToString("yyyy-MM-dd"));
                windPark.ContactMan = dto.ContactMan ?? "";
                windPark.ContactTel = dto.ContactTel ?? "";
                windPark.Address = dto.Address ?? "";
                windPark.PostCode = dto.PostCode ?? "";
                windPark.WindParkID = IDProvide.GetWindParkId(dto.WindParkGroupName ?? "其他", windPark.WindParkCode);
                windPark.Description = dto.Description ?? "";
                windPark.Country = dto.Country ?? "";
                windPark.Area = dto.Area ?? "";
                windPark.location = dto.Location ?? "";

                // 添加风场
                DevTreeManagement.AddWindPark(windPark);

                // 构建机组信息
                WindTurbine windTurbine = new WindTurbine();
                windTurbine.WindTurbineID = IDProvide.GetTurbineId(windPark.WindParkID, dto.WindTurbineCode);
                windTurbine.WindParkID = windPark.WindParkID;
                windTurbine.WindTurbineName = dto.WindTurbineName;
                windTurbine.WindTurbineCode = dto.WindTurbineCode;
                windTurbine.WindTurbineModel = dto.WindTurbineModel ?? "";
                windTurbine.OperationalDate = DateTime.Parse(dto.OperationalDate ?? DateTime.Now.ToString("yyyy-MM-dd"));
                windTurbine.MinWorkingRotSpeed = dto.MinWorkingRotSpeed ?? 0;
                windTurbine.Location = dto.Location ?? "";

                // 构建部件列表
                List<WindTurbineComponent> componentList = new List<WindTurbineComponent>();
                string[] componentIds = dto.ComponentIds.Split(',');

                foreach (string componentId in componentIds)
                {
                    if (!string.IsNullOrEmpty(componentId.Trim()))
                    {
                        WindTurbineComponent component = new WindTurbineComponent();
                        component.ComponentID = IDProvide.GetCompotentID(windTurbine.WindTurbineID, componentId.Trim());
                        component.WindTurbineID = windTurbine.WindTurbineID;
                        component.ComponentName = componentId.Trim();
                        componentList.Add(component);
                    }
                }

                windTurbine.TurComponentList = componentList;

                // 构建默认转速测量位置
                MeasLoc_RotSpd rotSpd = new MeasLoc_RotSpd();
                rotSpd.MeasLocName = "发电机转速";
                rotSpd.LineCounts = 2;
                rotSpd.GearRatio = 1f;
                rotSpd.MeasLocationID = IDProvide.GetRotSpdLocID(windTurbine.WindTurbineID, "发电机");
                rotSpd.WindTurbineID = windTurbine.WindTurbineID;
                windTurbine.RotSpdMeasLoc = rotSpd;

                // 添加机组
                DevTreeManagement.AddWindTurbine_Manager(windTurbine);

                // 添加主控系统
                if (!string.IsNullOrEmpty(dto.McsIP))
                {
                    MCS mcs = new MCS();
                    mcs.WindTurbineID = windTurbine.WindTurbineID;
                    mcs.MCSIP = dto.McsIP;
                    mcs.FieldBusType = "0";
                    mcs.MCSPort = 502;
                    DAUMCS.AddMCS(mcs);
                }

                // 记录日志
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = windTurbine.WindTurbineName;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = string.Format("Gateway添加_设备树({0})", windTurbine.WindTurbineID);
                LogManagement.UserlogWrite(logEntity);

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[AddDevice]Gateway添加机组失败", ex);
                return Ok(ApiResponse<string>.Error($"添加机组失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 修改Gateway机组设备信息
        /// </summary>
        /// <param name="dto">机组设备信息</param>
        /// <returns></returns>
        [HttpPost("EditDevice")]
        public IActionResult EditDevice([FromBody] GatewayDeviceDTO dto)
        {
            try
            {
                if (dto == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (string.IsNullOrEmpty(dto.WindParkID) || string.IsNullOrEmpty(dto.WindTurbineID))
                {
                    return Ok(ApiResponse<string>.Error("风场ID和机组ID不能为空"));
                }

                // 获取现有风场信息
                WindPark existingWindPark = DevTreeManagement.GetWindPark(dto.WindParkID);
                if (existingWindPark == null)
                {
                    return Ok(ApiResponse<string>.Error("风场不存在"));
                }

                // 获取现有机组信息
                WindTurbine existingWindTurbine = DevTreeManagement.GetWindTurbine(dto.WindTurbineID);
                if (existingWindTurbine == null)
                {
                    return Ok(ApiResponse<string>.Error("机组不存在"));
                }

                // 更新风场信息
                if (!string.IsNullOrEmpty(dto.WindParkName))
                {
                    existingWindPark.WindParkName = dto.WindParkName;
                }
                if (!string.IsNullOrEmpty(dto.OperationalDate))
                {
                    existingWindPark.OperationalDate = DateTime.Parse(dto.OperationalDate);
                }
                if (dto.ContactMan != null)
                {
                    existingWindPark.ContactMan = dto.ContactMan;
                }
                if (dto.ContactTel != null)
                {
                    existingWindPark.ContactTel = dto.ContactTel;
                }
                if (dto.Address != null)
                {
                    existingWindPark.Address = dto.Address;
                }
                if (dto.PostCode != null)
                {
                    existingWindPark.PostCode = dto.PostCode;
                }
                if (dto.Description != null)
                {
                    existingWindPark.Description = dto.Description;
                }
                if (dto.Country != null)
                {
                    existingWindPark.Country = dto.Country;
                }
                if (dto.Area != null)
                {
                    existingWindPark.Area = dto.Area;
                }
                if (dto.Location != null)
                {
                    existingWindPark.location = dto.Location;
                }

                // 更新机组信息
                if (!string.IsNullOrEmpty(dto.WindTurbineName))
                {
                    existingWindTurbine.WindTurbineName = dto.WindTurbineName;
                }
                if (!string.IsNullOrEmpty(dto.WindTurbineModel))
                {
                    existingWindTurbine.WindTurbineModel = dto.WindTurbineModel;
                }
                if (!string.IsNullOrEmpty(dto.OperationalDate))
                {
                    existingWindTurbine.OperationalDate = DateTime.Parse(dto.OperationalDate);
                }
                if (dto.MinWorkingRotSpeed.HasValue)
                {
                    existingWindTurbine.MinWorkingRotSpeed = dto.MinWorkingRotSpeed.Value;
                }
                if (dto.Location != null)
                {
                    existingWindTurbine.Location = dto.Location;
                }

                // 保存风场信息
                DevTreeManagement.EditWindPark(existingWindPark);

                // 保存机组信息
                DevTreeManagement.EditWindTurbine(existingWindTurbine);

                // 更新主控系统IP
                if (!string.IsNullOrEmpty(dto.McsIP))
                {
                    MCS existingMcs = DAUMCS.GetMCSByTurbineId(dto.WindTurbineID);
                    if (existingMcs != null)
                    {
                        existingMcs.MCSIP = dto.McsIP;
                        DAUMCS.EditMCS(existingMcs);
                    }
                    else
                    {
                        MCS newMcs = new MCS();
                        newMcs.WindTurbineID = dto.WindTurbineID;
                        newMcs.MCSIP = dto.McsIP;
                        newMcs.FieldBusType = "0";
                        newMcs.MCSPort = 502;
                        DAUMCS.AddMCS(newMcs);
                    }
                }

                // 记录日志
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = existingWindTurbine.WindTurbineName;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = string.Format("Gateway修改_设备树({0})", existingWindTurbine.WindTurbineID);
                LogManagement.UserlogWrite(logEntity);

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                Logger.LogErrorMessage("[EditDevice]Gateway修改机组失败", ex);
                return Ok(ApiResponse<string>.Error($"修改机组失败: {ex.Message}"));
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 基本信息查询
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDeviceInfo")]
        public async Task<ActionResult<GateWayInformation>> GetDeviceInfo()
        {
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var deviceInfo = await ctx.GateWayInformations.FirstOrDefaultAsync();
            if (deviceInfo == null)
            {
                return new GateWayInformation();
            }
            return deviceInfo;

        }

        /// <summary>
        /// 基本信息编辑
        /// </summary>
        /// <param name="deviceInfo"></param>
        /// <returns></returns>
        [HttpPost("UpdateDeviceInfo")]
        public async Task<IActionResult> UpdateDeviceInfo([FromBody] GateWayInformation deviceInfo)
        {
            if (string.IsNullOrEmpty(deviceInfo.DeviceName))
            {
                return BadRequest("Device name is required");
            }
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayInformations.FirstOrDefaultAsync();
            if (existingDevice == null)
            {
                ctx.GateWayInformations.Add(deviceInfo);
            }
            else
            {
                existingDevice.DeviceName = deviceInfo.DeviceName;
                existingDevice.IP = deviceInfo.IP;
                existingDevice.Mask = deviceInfo.Mask;
                existingDevice.GateWay = deviceInfo.GateWay;
                existingDevice.SN = deviceInfo.SN;
                existingDevice.ProductDate = deviceInfo.ProductDate;
                existingDevice.UDPPort = deviceInfo.UDPPort;
                existingDevice.MAC = deviceInfo.MAC;
                existingDevice.TimeSyncService = deviceInfo.TimeSyncService;
            }

            try
            {
                await ctx.SaveChangesAsync();
                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DeviceInfoExists(deviceInfo.DeviceName))
                {
                    return NotFound();
                }
                throw;
            }
        }

        private bool DeviceInfoExists(string deviceName)
        {
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            return ctx.GateWayInformations.Any(e => e.DeviceName == deviceName);
        }
        #endregion

        #region 推送配置

        /// <summary>
        /// mqtt配置查询
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMqttConfig")]
        public async Task<ActionResult<MqttConfig>> GetMqttConfig()
        {
            using var _context = new ConfigContext(ConfigInfo.DBConnName);
            var config = await _context.MqttConfigs.FirstOrDefaultAsync();
            if (config == null)
            {
                return new MqttConfig();
            }
            return config;
        }
        /// <summary>
        /// mqtt配置编辑
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPost("UpdateMqttConfig")]
        public async Task<IActionResult> UpdateMqttConfig([FromBody] MqttConfig config)
        {
            using var _context = new ConfigContext(ConfigInfo.DBConnName);
            var existingConfig = await _context.MqttConfigs.FirstOrDefaultAsync();
            if (existingConfig == null)
            {
                _context.MqttConfigs.Add(config);
            }
            else
            {
                _context.Entry(existingConfig).CurrentValues.SetValues(config);
            }

            await _context.SaveChangesAsync();
            return Ok(ApiResponse<string>.Success("OK"));
        }


        /// <summary>
        /// sftp配置查询
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetSFTPConfig")]
        public async Task<ActionResult<SFTPConfig>> GetSFTPConfig()
        {
            using var _context = new ConfigContext(ConfigInfo.DBConnName);
            var config = await _context.SFTPConfigs.FirstOrDefaultAsync();
            if (config == null)
            {
                return new SFTPConfig();
            }
            return config;
        }

        /// <summary>
        /// sftp配置编辑
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPost("UpdateSFTPConfig")]
        public async Task<IActionResult> UpdateSFTPConfig([FromBody] SFTPConfig config)
        {
            using var _context = new ConfigContext(ConfigInfo.DBConnName);
            var existingConfig = await _context.SFTPConfigs.FirstOrDefaultAsync();
            if (existingConfig == null)
            {
                _context.SFTPConfigs.Add(config);
            }
            else
            {
                _context.Entry(existingConfig).CurrentValues.SetValues(config);
            }

            await _context.SaveChangesAsync();
            return Ok(ApiResponse<string>.Success("OK"));
        }
        #endregion

        #region 透传配置

        /// <summary>
        /// 获取透传配置
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetPassThrough")]
        public IActionResult GetPassThrough()
        {
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var datalist = ctx.GateWayPassThroughs.ToList();
            return Ok(datalist);
        }

        /// <summary>
        /// 添加透传配置
        /// </summary>
        /// <param name="gateWayPassThrough"></param>
        /// <returns></returns>
        [HttpPost("AddPassThrough")]
        public async Task<IActionResult> AddPassThrough([FromBody] GateWayPassThrough gateWayPassThrough)
        {
            
            if (string.IsNullOrEmpty(gateWayPassThrough.ChannelName))
            {
                return BadRequest("ChannelName is required");
            }
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayPassThroughs.FirstOrDefaultAsync(t=>t.ChannelName == gateWayPassThrough.ChannelName);
            if (existingDevice == null)
            {
                ctx.GateWayPassThroughs.Add(gateWayPassThrough);
                await ctx.SaveChangesAsync();
                return Ok(ApiResponse<string>.Success("OK"));
            }
            else
            {
                return Ok(ApiResponse<string>.Error("通道名称已存在"));
            }
        }


        [HttpPost("EditPassThrough")]
        public async Task<IActionResult> EditPassThrough([FromBody] GateWayPassThrough gateWayPassThrough)
        {

            if (string.IsNullOrEmpty(gateWayPassThrough.ChannelName))
            {
                return BadRequest("ChannelName is required");
            }
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayPassThroughs.FirstOrDefaultAsync(t => t.ChannelName == gateWayPassThrough.ChannelName);
            if (existingDevice == null)
            {
                return Ok(ApiResponse<string>.Error("未找到对应的记录"));
                
            }
            else
            {
                existingDevice.DataBit = gateWayPassThrough.DataBit;
                existingDevice.StopBit = gateWayPassThrough.StopBit;
                existingDevice.BandRate = gateWayPassThrough.BandRate;
                existingDevice.MappingTcpPort = gateWayPassThrough.MappingTcpPort;
                existingDevice.Parity = gateWayPassThrough.Parity;


                await ctx.SaveChangesAsync();
                return Ok(ApiResponse<string>.Success("OK"));
            }
        }

        /// <summary>
        /// 删除透传
        /// </summary>
        /// <param name="ChannelName"></param>
        /// <returns></returns>
        [HttpGet("DeletePassThrough")]
        public async Task<IActionResult> DeletePassThrough(string ChannelName)
        {

            if (string.IsNullOrEmpty(ChannelName))
            {
                return BadRequest("ChannelName is required");
            }
            using var ctx = new CMSFramework.EF.ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayPassThroughs.FirstOrDefaultAsync(t => t.ChannelName == ChannelName);
            if (existingDevice == null)
            {
                return Ok(ApiResponse<string>.Error("未找到对应的记录"));
            }
            else
            {
                ctx.Remove(existingDevice);
                await ctx.SaveChangesAsync();
                return Ok(ApiResponse<string>.Success("OK"));
            }
        }

        /// <summary>
        /// 透传切换
        /// </summary>
        /// <param name="ChannelName"></param>
        /// <param name="StartMapping"></param>
        /// <returns></returns>

        [HttpGet("SwitchPassThrough")]
        public async Task<IActionResult> SwitchPassThrough(string ChannelName,bool StartMapping)
        {

            if (string.IsNullOrEmpty(ChannelName))
            {
                return BadRequest("ChannelName is required");
            }
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayPassThroughs.FirstOrDefaultAsync(t => t.ChannelName == ChannelName);
            if (existingDevice == null)
            {
                return Ok(ApiResponse<string>.Error("未找到对应的记录"));
            }
            else
            {
                existingDevice.IsStartMapping = StartMapping;
                await ctx.SaveChangesAsync();
                return Ok(ApiResponse<string>.Success("OK"));
            }
        }
        #endregion

        #region 网关配置
        /// <summary>
        /// 重启网关
        /// </summary>
        /// <returns></returns>
        [HttpGet("GatewayReset")]
        public IActionResult GatewayReset()
        {
            _ = Task.Run(async () =>
            {
                await Task.Delay(2_000);          // 给前端留一点返回时间
                try
                {
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        // 优先 systemd
                        //Process.Start("sudo", "systemctl reboot");
                        var processStartInfo = new ProcessStartInfo
                        {
                            FileName = "/bin/bash",
                            Arguments = "-c \"sudo reboot\"",
                            RedirectStandardOutput = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        };

                        using var process = Process.Start(processStartInfo);
                        process?.WaitForExit();

                    }
                    else
                    {
                        _logger.LogWarning("不支持当前操作系统重启命令");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "重启失败");
                }
            });

            return Ok(ApiResponse<string>.Success("Reboot command has been sent"));
        }

        /// <summary>
        /// 对时服务
        /// </summary>
        /// <param name="timeSyncService"></param>
        /// <returns></returns>
        [HttpGet("TimeSync")]
        public async Task<IActionResult> TimeSyncAsync(string timeSyncService)
        {
            if (string.IsNullOrWhiteSpace(timeSyncService))
                return Ok(ApiResponse<string>.Error("服务器地址不能为空"));
            using var ctx = new ConfigContext(ConfigInfo.DBConnName);
            var existingDevice = await ctx.GateWayInformations.FirstOrDefaultAsync();
            if (existingDevice != null)
            {
                existingDevice.TimeSyncService = timeSyncService;
            }
            try
            {
                await ctx.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
            }

            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = $"ntpdate {timeSyncService}",
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processStartInfo);
                process?.WaitForExit();

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "对时失败");
                return Ok(ApiResponse<string>.Error($"对时失败：{ex.Message}"));
            }
        }


        #endregion
    }
}