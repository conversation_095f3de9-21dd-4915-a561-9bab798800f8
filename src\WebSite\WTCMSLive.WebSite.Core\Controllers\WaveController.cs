﻿using System.Net;
using System.Net.Http.Headers;
using CMSFramework.BusinessEntity;
using CMSFramework.EF;
using MySql.Data.MySqlClient;
using WTCMSLive.WebSite.Models;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.IO.Compression;


namespace WTCMSLive.WebSite.Controllers
{
    public partial class WaveController : Controller
    {
        #region 波形监测
        public string GetLastWaveTime(string turbineID, string mLocationID)
        {
            //EnumDataSource.RealTimeDB
            DateTime? dt = null;
            VibWaveFormData waveFormData = null;
            if (mLocationID == "2" || mLocationID == "3")
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.RealTimeDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.VibWaveForms.Where(wf => wf.WindTurbineID == turbineID && wf.SignalType == mLocationID).FirstOrDefault();
                    if (waveFormData != null)
                    {
                        return waveFormData.AcquisitionTime.ToString();
                    }
                }
            }
            else
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.RealTimeDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.VibWaveForms.Where(wf => wf.MeasLocationID == mLocationID && wf.WindTurbineID == turbineID).OrderByDescending(item => item.AcquisitionTime).FirstOrDefault();
                    if (waveFormData != null)
                    {
                        return waveFormData.AcquisitionTime.ToString();
                    }
                }
            }
            return "";
        }

        /// <summary>
        /// 获取最新的波形数据
        /// </summary>
        /// <param name="waveType"></param>
        /// <param name="turbineID"></param>
        /// <param name="mLocationID"></param>
        /// <returns></returns>
        public string GetWaveDataByType(int waveType, string turbineID, string mLocationID, double ContentWidth)
        {
            List<MeasLoc_Vib> measLocList = MeasLocationDA.GetVibMeasLocationByTurId(turbineID);
            List<string> _location = new List<string>();
            // 如果选中的测量位置为电压（2）或者电流(3)
            if (mLocationID == "2" || mLocationID == "3")
            {
                List<MeasLoc_Vib> list = measLocList.FindAll(
                    item => item.Orientation == mLocationID).OrderBy(item => item.Orientation).ToList();
                foreach (var item in list)
                {
                    _location.Add(item.MeasLocationID);
                }
            }
            else
            {
                _location = mLocationID.Split(',').ToList();
            }
            string[] location = _location.ToArray();

            List<eChartsData> echartsDataList = new List<eChartsData>();
            // 
            eChartsData echartTimeData = CreateChartData();
            echartsDataList.Add(echartTimeData);

            eChartsData echartsFFT = CreateChartData();
            echartsDataList.Add(echartsFFT);

            // 遍历测量位置，每个测量位置获取波形，加入chartData
            foreach (string loc in location)
            {
                var measLoc = measLocList.Find(item => item.MeasLocationID == loc);

                if (measLoc != null)
                {
                    //数据数组
                    //CMSFramework.Logger.Logger.LogInfoMessage("获取数据begin：" + DateTime.Now);
                    VibWaveFormData waveFormData = GetRealWaveFromData(turbineID, measLoc.MeasLocationID, EnumWaveFormType.WDF_Time);
                    //CMSFramework.Logger.Logger.LogInfoMessage("获取数据end：" + DateTime.Now);
                    if (waveFormData == null)
                        continue;

                    // 获取时域数据
                    //CMSFramework.Logger.Logger.LogInfoMessage("获取数据文件begin：" + DateTime.Now);
                    double[] data = GetWaveDataDouble(waveFormData);
                    //  CMSFramework.Logger.Logger.LogInfoMessage("获取数据文件end：" + DateTime.Now);
                    double t = 1.0 / waveFormData.SampleRate;

                    int dotTotal = (int)ContentWidth;
                    // 时域图设置
                    SetEchartsView(echartTimeData, measLoc, waveFormData, data, "时域分析", t, dotTotal);

                    ////频谱分析获取
                    //double[] arrFFT = AppFramework.Analysis.FrepSpectrum.AmplitudeSpectrum(data, waveFormData.SampleRate);
                    //// 频率分辨率
                    //double FeqRes = 1 / (waveFormData.WaveLength / waveFormData.SampleRate);

                    //// 频谱图设置
                    //SetEchartsView(echartsFFT, measLoc, waveFormData, arrFFT, "频谱分析", FeqRes, dotTotal);

                    //获取包络谱波形
                    Dictionary<string, double[]> arrBl = WaveEnvelope.GetEnvelopeWave(data, waveFormData.SampleRate);
                    SetEnvelopeEcharts(echartsFFT, measLoc, waveFormData, arrBl, "包络谱");

                }
            }
            string JsonResult = Newtonsoft.Json.JsonConvert.SerializeObject(echartsDataList);
            // CMSFramework.Logger.Logger.LogInfoMessage("JsonResult：" + JsonResult.Length);
            return JsonResult;
        }

        /// <summary>
        /// 设置Echart数据
        /// </summary>
        /// <param name="echartTimeData"></param>
        /// <param name="measLoc"></param>
        /// <param name="waveFormData"></param>
        /// <param name="data"></param>
        /// <param name="title"></param>
        /// <param name="xtickVal"></param>
        private static void SetEchartsView(eChartsData echartTimeData, MeasLoc_Vib measLoc, VibWaveFormData waveFormData, double[] data, string title, double xtickVal, int dotTotal)
        {
            echartTimeData.legendData.Add(measLoc.MeasLocName);
            echartTimeData.primarykey = waveFormData.AcquisitionTime.ToString();
            echartTimeData.title = title + "(" + waveFormData.AcquisitionTime.ToString() + ")";
            echartTimeData.UnitName = UnitConfig.GetUnitNameBySignalType(waveFormData.SignalType);
            seriesData serData = new seriesData()
            {
                name = measLoc.MeasLocName,
                type = "line",
                symbol = "none",
                //sampling = "max",
                data = new List<List<double>>()
            };

            int dotNum = (int)(data.Length / dotTotal);

            if (dotNum > 10)
            {
                //dotTotal = dotTotal * 10;
                dotNum = (int)(data.Length / dotTotal);

                double[] dataNew = new double[dotTotal];

                for (int inx = 0; inx < dotTotal; inx++)
                {
                    double max = double.MinValue;
                    double min = double.MaxValue;
                    int maxIndex = 0;
                    int minIndex = 0;
                    for (int i = 0; i < dotNum && i < data.Length; i++)
                    {
                        if (max < data[inx * dotNum + i])
                        {
                            maxIndex = inx * dotNum + i;
                            max = data[maxIndex];
                        }

                        if (min > data[inx * dotNum + i])
                        {
                            minIndex = inx * dotNum + i;
                            min = data[minIndex];
                        }
                    }
                    if (title == "时域分析")
                    {
                        //单位不转化、

                        //if (waveFormData.SignalType == "2")
                        //{
                        //    //数据库中的mv转成v
                        //    //xtickVal = xtickVal / 1000;
                        //    min = min / 1000;
                        //    max = max / 1000;
                        //}
                    }
                    if (maxIndex > minIndex)
                    {
                        AddDot(xtickVal, serData, min, minIndex);
                        AddDot(xtickVal, serData, max, maxIndex);
                    }
                    else
                    {
                        AddDot(xtickVal, serData, max, maxIndex);
                        AddDot(xtickVal, serData, min, minIndex);
                    }
                }
            }
            else
            {
                for (int i = 0; i < data.Length; i++)
                {
                    List<double> valueArr = new List<double>();
                    if (title == "时域分析")
                    {

                        //不对单位进行转换
                        //如果是电压，需要把mv换成v
                        //if (waveFormData.SignalType == "2")
                        //{
                        //    //数据库中的mv转成v
                        //    data[i] = data[i] / 1000;
                        //}
                    }
                    valueArr.Add(double.Parse((i * xtickVal).ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));
                    valueArr.Add(double.Parse(data[i].ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));
                    serData.data.Add(valueArr);
                }
            }
            echartTimeData.series.Add(serData);
        }
        /// <summary>
        /// 设置包络波形Echarts数据
        /// </summary>
        /// <param name="echartTimeData"></param>
        /// <param name="measLoc"></param>
        /// <param name="waveFormData"></param>
        /// <param name="data"></param>
        /// <param name="title"></param>
        private static void SetEnvelopeEcharts(eChartsData echartTimeData, MeasLoc_Vib measLoc, VibWaveFormData waveFormData, Dictionary<string, double[]> data, string title)
        {
            echartTimeData.legendData.Add(measLoc.MeasLocName);
            echartTimeData.primarykey = waveFormData.AcquisitionTime.ToString();
            echartTimeData.title = title + "(" + waveFormData.AcquisitionTime.ToString() + ")";
            echartTimeData.UnitName = UnitConfig.GetUnitNameBySignalType(waveFormData.SignalType);
            seriesData serData = new seriesData()
            {
                name = measLoc.MeasLocName,
                type = "line",
                symbol = "none",
                //sampling = "max",
                data = new List<List<double>>()
            };

            double[] XAxis = data["x"];
            double[] YAxis = data["y"];

            for (int i = 0; i < XAxis.Length; i++)
            {
                List<double> valueArr = new List<double>();
                valueArr.Add(double.Parse(XAxis[i].ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));
                valueArr.Add(double.Parse(YAxis[i].ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));
                serData.data.Add(valueArr);
            }
            echartTimeData.series.Add(serData);
        }
        /// <summary>
        /// 获取波形更新时间
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="mLocationID"></param>
        /// <returns></returns>
        private static void AddDot(double xtickVal, seriesData serData, double min, int minIndex)
        {
            List<double> valueArr = new List<double>();

            valueArr.Add(double.Parse((minIndex * xtickVal).ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));

            valueArr.Add(double.Parse(min.ToString(System.Configuration.ConfigurationManager.AppSettings["FloatFormat"])));

            serData.data.Add(valueArr);
        }
        #endregion

        #region 波形分析
        /// <summary>
        /// 波形分析
        /// </summary>
        /// <param name="waveType">时域  或  频谱</param>
        /// <param name="selectedWaveList">选中的波形列表</param>
        /// <returns></returns>
        public string GetAnalyseWaveDataByType(int waveType, string selectedWaveList)
        {
            List<eChartsData> echartsDataList = new List<eChartsData>();
            // 时域
            eChartsData echartTimeData = CreateChartData();
            echartsDataList.Add(echartTimeData);
            //频谱
            eChartsData echartsFFT = CreateChartData();
            echartsDataList.Add(echartsFFT);
            try
            {
                string[] WavetTypeArr = selectedWaveList.Split(new char[] { '#' }, StringSplitOptions.RemoveEmptyEntries);
                // 遍历波形
                foreach (string strWaveType in WavetTypeArr)
                {
                    JsonWaveInfo waveInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<JsonWaveInfo>(strWaveType);
                    List<MeasLoc_Vib> measLocList = null;
                    if (measLocList == null)
                    {
                        measLocList = MeasLocationDA.GetVibMeasLocationByTurId(waveInfo.WindTurbineID);
                    }
                    if (waveInfo.MeasLocName == "电机转速")
                    {
                        echartTimeData.legendData = new List<string>();
                        echartTimeData.series = new List<seriesData>();
                        echartTimeData.title = "转速波形";
                        echartTimeData.UnitName = UnitConfig.GetUnitNameByWaveType("转速");
                        seriesData serData = new seriesData()
                        {
                            type = "line",
                            symbol = "none",
                            sampling = "average"
                        };
                        serData.data = new List<List<double>>();
                        //获取转速波形数据
                        RotSpeedWaveData waveFormData = GetRotSpdWaveFromData(waveInfo.WindTurbineID, waveInfo.MeasLocationID, waveInfo.AcquisitionTime);
                        SetRotWaveData(waveFormData, serData);
                        serData.name = waveInfo.MeasLocName + waveFormData.AcquisitionTime.ToString();
                        echartTimeData.legendData.Add(serData.name);
                        echartTimeData.series.Add(serData);
                        //echartsDataList.Add(echartTimeData);
                    } else if (waveInfo.MeasLocName == "工况") {
                        echartTimeData.legendData = new List<string>();
                        echartTimeData.series = new List<seriesData>();
                        echartTimeData.title = "工况波形";
                        //工况波形数据
                        WorkConditionWaveFormData waveFormData = GetWkWaveFromData(waveInfo.WindTurbineID, waveInfo.MeasLocationID, waveInfo.AcquisitionTime);

                        if (waveFormData == null)
                            continue;
                        double[] data = GetWkWaveBytes(waveFormData);
                        double t = 1.0 / waveFormData.SampleRate;
                        seriesData serData = new seriesData()
                        {
                            type = "line",
                            symbol = "none",
                        };
                        serData.data = new List<List<double>>();


                        for (int i = 0; i < data.Length; i++)
                        {
                            List<double> valueArr = new List<double>();
                            valueArr.Add(i);
                            valueArr.Add(data[i]);
                            serData.data.Add(valueArr);
                        }

                        echartTimeData.series.Add(serData);
                        //echartsDataList.Add(echartTimeData);
                    }
                    else
                    {
                        var measLoc = measLocList.Find(item => item.MeasLocationID == waveInfo.MeasLocationID);
                        if (measLoc != null)
                        {
                            //数据数组
                            VibWaveFormData waveFormData = GetDayDbWaveFromData(waveInfo.WindTurbineID, waveInfo.MeasLocationID, waveInfo.AcquisitionTime);
                            if (waveFormData == null)
                                continue;
                            double[] data = GetWaveDataDouble(waveFormData);
                            // 获取时域数据
                            double t = 1.0 / waveFormData.SampleRate;
                            SetEchartsView(echartTimeData, measLoc, waveFormData, data, "时域分析", t, waveFormData.WaveLength);
                            //温度波形无频谱分析数据
                            //if (measLoc.Orientation != "4")
                            //{
                            //    //频谱分析获取
                            //    double[] arrFFT = AppFramework.Analysis.FrepSpectrum.AmplitudeSpectrum(data, waveFormData.SampleRate);
                            //    // 频率分辨率
                            //    double FeqRes = 1 / (waveFormData.WaveLength / waveFormData.SampleRate);
                            //    // 频谱图设置
                            //    SetEchartsView(echartsFFT, measLoc, waveFormData, arrFFT, "频谱分析", FeqRes, waveFormData.WaveLength);

                            //    //温度波形也没有包络波形?
                            //    //包络谱获取

                            //    //Dictionary<string, double[]> arrBl = WaveEnvelope.GetEnvelopeWave(data, waveFormData.SampleRate);
                            //    //SetEnvelopeEcharts(echartsFFT, measLoc, waveFormData, arrBl, "包络谱");

                            //}
                        }
                    }
                    /*
                    if (waveInfo.MeasLocName == "转速")
                    {
                        eChartsData rotspdEcharts = new eChartsData();
                        rotspdEcharts.legendData = new List<string>();
                        rotspdEcharts.series = new List<seriesData>();
                        rotspdEcharts.title = "转速波形";
                        seriesData serData = new seriesData()
                        {
                            type = "line",
                            symbol = "none",
                            sampling = "average"
                        };
                        serData.data = new List<List<double>>();
                        //获取转速波形数据
                        RotSpeedWaveData waveFormData = GetRotSpdWaveFromData(waveInfo.WindTurbineID, waveInfo.MeasLocationID, waveInfo.AcquisitionTime);
                        SetRotWaveData(waveFormData, serData);
                        serData.name = waveInfo.MeasLocName + waveFormData.AcquisitionTime.ToString();
                        rotspdEcharts.legendData.Add(serData.name);
                        rotspdEcharts.series.Add(serData);
                        echartsList.Add(rotspdEcharts);
                    }
                    else
                    {
                        // 测量位置删除？
                        var measLoc = measLocList.Find(loc => loc.MeasLocationID == waveInfo.MeasLocationID);
                        if (measLoc == null)
                            continue;
                        // 获取波形实体
                        VibWaveFormData waveFormData = GetDayDbWaveFromData(waveInfo.WindTurbineID, waveInfo.MeasLocationID, waveInfo.AcquisitionTime);
                        double[] arr = GetWaveDataDouble(waveFormData);
                        // 时域
                        if (waveType == 0)
                        {
                            double t = 1.0 / waveFormData.SampleRate;
                            eChartsData echartTime = new eChartsData();
                            echartTime.legendData = new List<string>();
                            echartTime.series = new List<seriesData>();
                            SetEchartsView(echartTime, measLoc, waveFormData, arr, "时域分析", t, waveFormData.WaveLength);
                            echartsList.Add(echartTime);
                        }
                        // 频谱
                        else if (waveType == 1)
                        {
                            if (measLoc != null && measLoc.Orientation != "4")
                            {
                                //频谱分析获取
                                double[] arrFFT = AppFramework.Analysis.FrepSpectrum.AmplitudeSpectrum(arr, waveFormData.SampleRate);
                                // 频率分辨率
                                double FeqRes = 1 / (waveFormData.WaveLength / waveFormData.SampleRate);
                                eChartsData echartFFT = new eChartsData();
                                echartFFT.legendData = new List<string>();
                                echartFFT.series = new List<seriesData>();
                                // 频谱图设置
                                SetEchartsView(echartFFT, measLoc, waveFormData, arrFFT, "频谱分析", FeqRes, arrFFT.Length);
                                echartsList.Add(echartFFT);
                            }
                        }
                    }*/
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.GetExceptionMessage(ex);
            }
            string JsonResult = Newtonsoft.Json.JsonConvert.SerializeObject(echartsDataList);
            return JsonResult;
        }
        #endregion


        #region 波形下载
        public ActionResult WaveDownLoad()
        {
            return View();
        }

        /// <summary>
        /// 按照时间段批量下载
        /// </summary>
        /// <param name="fileExtend"></param>
        /// <param name="WindTurbineId"></param>
        /// <param name="beginDate"></param>
        /// <param name="endDate"></param>
        /// <param name="waveType"></param>
        /// <returns></returns>
        public FileResult GetLoadWaveZipData(string fileExtend, string WindTurbineId, string beginDate, string endDate, string waveType)
        {
            HttpResponseMessage messag = new HttpResponseMessage();
            List<WaveDataFileInfo> list = new List<WaveDataFileInfo>();
            try
            {
                DateTime beginTime = DateTime.Parse(beginDate);
                DateTime endTime = DateTime.Parse(endDate).AddDays(1);
                //获取该段时间内的工况数据
                List<WorkingConditionData> businessWKConDataDatas = WTCMSLive.BusinessModel.TrendEVDataManage.GetWorkingConditionByTurId(WindTurbineId, beginTime, endTime);
                // 转速
                List<RotSpeedWaveData> RotSpdFormData = GetDayDbRotSpdWaveFromDataList(WindTurbineId, beginTime, endTime);
                // 时域
                List<VibWaveFormData> waveFormData = GetDayDbWaveFromDataList(WindTurbineId, beginTime, endTime);
                // 过滤未选择的波形
                waveFormData = FilterWaveType(waveType, waveFormData);

                List<DAUChannel_Process> dauProcess = WTCMSLive.BusinessModel.DauManagement.GetDAUChannelProcessListByDAUId(WindTurbineId);
                List<MeasLoc_Vib> vibList = WTCMSLive.BusinessModel.DevTreeManagement.GetVibMeasLocationByTurId(WindTurbineId);
                WindPark wp = WTCMSLive.BusinessModel.DevTreeManagement.GetWindParkByTurID(WindTurbineId);
                foreach (var waveForm in waveFormData)
                {
                    var file = CreateTimeWaveFile(businessWKConDataDatas, dauProcess, wp.WindParkName,
                        vibList.Find(m => m.MeasLocationID == waveForm.MeasLocationID).MeasLocName, waveForm);
                    if (file != null)
                    {
                        list.Add(file);
                    }
                }

                // 转速波形
                if (waveType == "转速" || waveType == "全部")
                {
                    foreach (var rotWave in RotSpdFormData)
                    {
                        var file = CreateRotWaveFile(wp, rotWave);
                        if (file != null)
                        {
                            list.Add(file);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetLoadWaveData]", ex);
            }

            string sourceFile = SaveDataFile(fileExtend, list);
            string fileDownName = DateTime.Now.ToString("yyyyHHmmMMhhss") + ".zip";
            return File(sourceFile, "text/plain", fileDownName);

        }

        /// <summary>
        /// 转速波形文件
        /// </summary>
        /// <param name="wp"></param>
        /// <param name="rotWave"></param>
        private WaveDataFileInfo CreateRotWaveFile(WindPark wp, RotSpeedWaveData rotWave)
        {
            string acqTime = rotWave.AcquisitionTime.ToString("yyyyMMddHHmmss");
            WaveDataFileInfo waveFile = new WaveDataFileInfo()
            {
                fileName = wp.WindParkName + "_" + wp.WindTurbineList.Find(item => item.WindTurbineID == rotWave.WindTurbineID).WindTurbineName + "_转速_" + acqTime
            };

            rotWave.WaveData = this.GetRotSpdWaveBytes(rotWave);

            float[] fltData = CMSFramework.Utility.RotSpdWaveHelper.ConvertToRotSpdRPM(rotWave.WaveData, 1);
            if (fltData != null)
            {
                waveFile.fileData = string.Join("\r\n", fltData);
                return waveFile;
            }
            return null;
        }

        /// <summary>
        /// 创建时域文件
        /// </summary>
        /// <param name="businessWKConDataDatas"></param>
        /// <param name="dauProcess"></param>
        /// <param name="vibList"></param>
        /// <param name="wp"></param>
        /// <param name="waveForm"></param>
        /// <returns></returns>
        private WaveDataFileInfo CreateTimeWaveFile(List<WorkingConditionData> businessWKConDataDatas, List<DAUChannel_Process> dauProcess, string trainName, string measLocName, VibWaveFormData waveForm)
        {
            string acqTime = waveForm.AcquisitionTime.ToString("yyyyMMddHHmmss");
            WaveDataFileInfo wavetFile = new WaveDataFileInfo()
            {
                fileName = trainName + "_" + measLocName + "_" + acqTime
            };
            // 获取波形数据
            double[] fltData = GetWaveDataDouble(waveForm);
            if (fltData != null)
            {
                //添加数据头
                //温度值 1 温度值 2 温度值 3
                //采样频率，采样时长，采样开始时间
                wavetFile.fileData = GetWorkConditionInfo(dauProcess, businessWKConDataDatas.FindAll(item => item.AcquisitionTime == waveForm.AcquisitionTime)) + "\r\n";
                if (waveForm.WaveformType == CMSFramework.BusinessEntity.EnumWaveFormType.WDF_Time)
                    {
                    wavetFile.fileData += Math.Round(waveForm.SampleRate / 2.16F) + "," + Math.Round((waveForm.WaveLength / waveForm.SampleRate) * 2.56F) + "," + waveForm.AcquisitionTime + "\r\n";
                }
                else
                {
                    wavetFile.fileData += waveForm.SampleRate + "," + (waveForm.WaveLength / waveForm.SampleRate) + "," + waveForm.AcquisitionTime + "\r\n";
                }

                wavetFile.fileData += string.Join("\r\n", fltData);

                return wavetFile;
            }
            return null;
        }

        private static List<VibWaveFormData> FilterWaveType(string waveType, List<VibWaveFormData> waveFormData)
        {
            if (waveType == "转速")
            {
                waveFormData.Clear();
            }
            if (waveType != "全部")
            {
                int EnumType = 0;
                if (waveType == "加速度")
                {
                    EnumType = 0;
                }
                else if (waveType == "电流")
                {
                    EnumType = 3;
                }
                else if (waveType == "电压")
                {
                    EnumType = 2;
                }
                else if (waveType == "温度")
                {
                    EnumType = 4;
                }
                waveFormData = waveFormData.Where(item => item.SignalType == EnumType.ToString()).ToList();
            }
            return waveFormData;
        }

        /// <summary>
        /// 保存数据文件
        /// </summary>
        /// <param name="fileExtend"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        private static string SaveDataFile(string fileExtend, List<WaveDataFileInfo> list)
        {
            string fileName = "数据文件.zip";

            // 获取保存路径
            string dicFilePath = Path.Combine(Directory.GetCurrentDirectory(), "Temp");
            string dataFileFullName = Path.Combine(dicFilePath, fileName);

            // 确保目录存在
            if (!Directory.Exists(dicFilePath))
            {
                Directory.CreateDirectory(dicFilePath);
            }
            else
            {
                // 如果文件已存在，删除旧文件
                if (System.IO.File.Exists(dataFileFullName))
                {
                    System.IO.File.Delete(dataFileFullName);
                }
            }

            // 创建ZIP文件
            using (FileStream fsOut = System.IO.File.Create(dataFileFullName))
            using (ZipArchive zipArchive = new ZipArchive(fsOut, ZipArchiveMode.Create, true))
            {
                foreach (var item in list)
                {
                    byte[] bytes = Encoding.UTF8.GetBytes(item.fileData);

                    // 添加文件到ZIP
                    var zipEntry = zipArchive.CreateEntry(item.fileName + fileExtend, CompressionLevel.Fastest);
                    using (Stream entryStream = zipEntry.Open())
                    {
                        entryStream.Write(bytes, 0, bytes.Length);
                    }
                }
            }

            return dataFileFullName;
        }



        /// <summary>
        /// 单条下载
        /// </summary>
        /// <param name="selectedWaveList"></param>
        /// <returns></returns>
        public string GetLoadWaveData(string selectedWaveList)
        {
            List<WaveDataFileInfo> list = new List<WaveDataFileInfo>();
            try
            {
                string[] WavetTypeArr = selectedWaveList.Split(new char[] { '#' }, StringSplitOptions.RemoveEmptyEntries);
                string WindTurbineId = "";
                if (WavetTypeArr.Length > 0)
                {
                    JsonWaveInfo waveForm = Newtonsoft.Json.JsonConvert.DeserializeObject<JsonWaveInfo>(WavetTypeArr[0]);
                    WindTurbineId = waveForm.WindTurbineID;
                }
                // 机组为空？
                if (string.IsNullOrEmpty(WindTurbineId))
                {
                    return Newtonsoft.Json.JsonConvert.SerializeObject(list);
                }

                List<DAUChannel_Process> dauProcess = WTCMSLive.BusinessModel.DauManagement.GetDAUChannelProcessListByDAUId(WindTurbineId).OrderBy(item => item.ChannelNumber).ToList();
                List<JsonWaveInfo> waveInfoList = new List<JsonWaveInfo>();

                foreach (string strWave in WavetTypeArr)
                {
                    JsonWaveInfo waveForm = Newtonsoft.Json.JsonConvert.DeserializeObject<JsonWaveInfo>(strWave);

                    waveInfoList.Add(waveForm);
                }

                DateTime beginTime = waveInfoList.OrderBy(item => item.AcquisitionTime).FirstOrDefault().AcquisitionTime;
                DateTime endTime = waveInfoList.OrderByDescending(item => item.AcquisitionTime).FirstOrDefault().AcquisitionTime;

                //获取该段时间内的工况数据
                List<WorkingConditionData> businessWKConDataDatas = WTCMSLive.BusinessModel.TrendEVDataManage.GetWorkingConditionByTurId(WindTurbineId, beginTime, endTime);

                foreach (var waveForm in waveInfoList)
                {
                    if (waveForm.MeasLocName == "转速")
                    {
                        string acqTime = waveForm.AcquisitionTime.ToString("yyyyMMddHHmmss");
                        WaveDataFileInfo waveFile = new WaveDataFileInfo()
                        {
                            fileName = waveForm.TrainName + "_" + waveForm.MeasLocName + "_" + acqTime
                        };

                        RotSpeedWaveData waveFormData = GetRotSpdWaveFromData(waveForm.WindTurbineID, waveForm.MeasLocationID, waveForm.AcquisitionTime);
                        float[] fltData = CMSFramework.Utility.RotSpdWaveHelper.ConvertToRotSpdRPM(GetRotSpdWaveBytes(waveFormData), 1);

                        if (fltData != null)
                        {
                            waveFile.fileData = string.Join("\r\n", fltData);
                            list.Add(waveFile);
                        }
                    }
                    else
                    {
                        VibWaveFormData waveFormData = GetDayDbWaveFromData(waveForm.WindTurbineID, waveForm.MeasLocationID, waveForm.AcquisitionTime);

                        var file = CreateTimeWaveFile(businessWKConDataDatas, dauProcess, waveForm.TrainName, waveForm.MeasLocName, waveFormData);
                        if (file != null)
                        {
                            list.Add(file);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetLoadWaveData]-参数：" + selectedWaveList, ex);
            }
            string JsonResult = Newtonsoft.Json.JsonConvert.SerializeObject(list);
            return JsonResult;// Json(list, JsonRequestBehavior.AllowGet);
        }

        private HttpResponseMessage WriteResponseMessage(Stream stream, string fileName)
        {
            try
            {
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = fileName
                };
                return response;
            }
            catch
            {
                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }
        }
        private string GetWorkConditionInfo(List<DAUChannel_Process> dauProcess, List<WorkingConditionData> businessWKConDataDatas)
        {
            List<double?> workConditionList = new List<double?>();
            dauProcess.ForEach(item =>
            {
                WorkingConditionData workcon = businessWKConDataDatas.Find(work => work.MeasLocationID == item.MeasLoc_ProcessId);
                if (workcon != null)
                {
                    workConditionList.Add(workcon.Param_Value);
                }
                else
                {
                    workConditionList.Add(null);
                }
            });
            return string.Join(",", workConditionList);
        }
        #endregion

        #region 波形列表获取

        ///// <summary>
        ///// 前台返回波形列表信息
        ///// </summary>
        ///// <param name="_beginTime"></param>
        ///// <param name="_endTime"></param>
        ///// <param name="WindTurbineId"></param>
        ///// <param name="measLocId"></param>
        ///// <returns></returns>
        //public JsonResult GetWaveDataByTime(DateTime _beginTime, DateTime _endTime, string WindTurbineId, string measLocId)
        //{
        //    CMSFramework.Logger.Logger.LogDebugMessage("T1:" + DateTime.Now);
        //    List<WaveType> list = new List<WaveType>();
        //    _endTime = _endTime.AddDays(1);
        //    List<RotSpeedWaveData> RotSpdFormData = GetDayDbRotSpdWaveFromDataList(WindTurbineId, _beginTime, _endTime);
        //    List<VibWaveFormData> waveFormData = GetDayDbWaveFromDataList(WindTurbineId, _beginTime, _endTime);
        //    List<WRD.AMS.BusinessEntities.DevTrainLine> trainLineList = null;
        //    List<WRD.AMS.BusinessEntities.DevTrainNumber> trainNumberList = null;
        //    using (WRD.AMS.EF.AMSContext ctx = new WRD.AMS.EF.AMSContext(ConfigInfo.DBConnName))
        //    {
        //        trainLineList = ctx.TrainLines.ToList();
        //        trainNumberList = ctx.TrainLineNumbers.ToList();
        //    }
        //    List<WindPark> windParkList = null;
        //    List<WindTurbine> turbineList = null;
        //    List<MeasLoc_Vib> measLocVib = MeasLocationDA.GetAllMeasLocations();
        //    using (CMSFramework.EF.DevContext ctx = new DevContext(ConfigInfo.DBConnName))
        //    {
        //        windParkList = ctx.DevWindParks.ToList();
        //        turbineList = ctx.DevWindTurbines.ToList();
        //    }
        //    JsonWaveInfo jsonValue = null;
        //    if (!string.IsNullOrEmpty(measLocId))
        //    {
        //        waveFormData = waveFormData.FindAll(item => item.MeasLocationID == measLocId);
        //    }
        //    else
        //    {
        //        waveFormData = waveFormData.FindAll(item => item.WindTurbineID == WindTurbineId);
        //    }
        //    waveFormData = waveFormData.OrderBy(item => item.WindTurbineID).ThenBy(item => item.ComponentName).ThenBy(item => item.MeasLocationID).ThenBy(item => item.AcquisitionTime).ToList();
        //    CMSFramework.Logger.Logger.LogDebugMessage("T2:" + DateTime.Now);
        //    waveFormData.ForEach(item =>
        //    {
        //        //把波形实体转换成网页实体
        //        MeasLoc_Vib measLoc = measLocVib.Find(meas => meas.MeasLocationID == item.MeasLocationID);
        //        if (measLoc != null)
        //        {
        //            WindTurbine turbine = turbineList.Find(tur => tur.WindTurbineID == item.WindTurbineID);
        //            WindPark park = windParkList.Find(tur => tur.WindParkID == turbine.WindParkID);
        //            WaveType wavetype = new WaveType();
        //            wavetype.TrainName = GetTrainName(park, trainLineList, trainNumberList);
        //            wavetype.StrAcquisitionTime = item.AcquisitionTime.ToString();
        //            wavetype.WaveformTypeName = AppFramework.Utility.EnumHelper.GetDescription((WRD.AMS.BusinessEntities.EnumSensorType)int.Parse(item.SignalType)).Replace("传感器", ""); // item.WaveformType
        //            wavetype.MeasLocName = measLoc.MeasLocName + "(通道" + item.DAUChannelID + ")";
        //            wavetype.WaveLength = item.WaveLength;

        //            wavetype.WindTurbineID = item.WindTurbineID;
        //            wavetype.AcquisitionTime = item.AcquisitionTime;
        //            wavetype.MeasLocationID = item.MeasLocationID;
        //            if (item.WaveformType == CMSFramework.BusinessEntity.EnumWaveFormType.WDF_Time)
        //            {
        //                wavetype.StringSampleRate = Math.Round(item.SampleRate / 2.56F).ToString();
        //            }
        //            else
        //            {
        //                wavetype.StringSampleRate = item.SampleRate.ToString();
        //            }
        //            jsonValue = new JsonWaveInfo()
        //            {
        //                AcquisitionTime = item.AcquisitionTime,
        //                MeasLocName = wavetype.MeasLocName,
        //                TrainName = wavetype.TrainName,
        //                MeasLocationID = measLoc.MeasLocationID,
        //                WindTurbineID = item.WindTurbineID
        //            };
        //            wavetype.GUID = Newtonsoft.Json.JsonConvert.SerializeObject(jsonValue);
        //            list.Add(wavetype);
        //            if (RotSpdFormData != null && RotSpdFormData.Count > 0)
        //            {
        //                RotSpeedWaveData RotSpdWave = RotSpdFormData.Find(rot => rot.WindTurbineID == item.WindTurbineID && rot.AcquisitionTime == item.AcquisitionTime);
        //                if (RotSpdWave != null)
        //                {
        //                    WaveType wavetypeRotSpd = new WaveType();
        //                    wavetypeRotSpd.TrainName = wavetype.TrainName;
        //                    wavetypeRotSpd.StrAcquisitionTime = item.AcquisitionTime.ToString();
        //                    wavetypeRotSpd.WaveformTypeName = "转速";
        //                    wavetypeRotSpd.MeasLocName = "电机转速";// measLoc.MeasLocName;
        //                    wavetypeRotSpd.WaveLength = RotSpdWave.WaveLength.Value;
        //                    wavetypeRotSpd.StringSampleRate = "";
        //                    wavetypeRotSpd.WindTurbineID = item.WindTurbineID;
        //                    wavetypeRotSpd.AcquisitionTime = item.AcquisitionTime;
        //                    wavetypeRotSpd.MeasLocationID = RotSpdWave.MeasLocationID;
        //                    jsonValue = new JsonWaveInfo()
        //                    {
        //                        AcquisitionTime = item.AcquisitionTime,
        //                        MeasLocationID = RotSpdWave.MeasLocationID,
        //                        WindTurbineID = item.WindTurbineID,
        //                        MeasLocName = "转速",
        //                        TrainName = wavetype.TrainName
        //                    };
        //                    string GUIDCode = Newtonsoft.Json.JsonConvert.SerializeObject(jsonValue);
        //                    wavetypeRotSpd.GUID = GUIDCode;
        //                    list.Add(wavetypeRotSpd);
        //                    RotSpdFormData.Remove(RotSpdWave);
        //                }
        //            }
        //        }
        //    });
        //    CMSFramework.Logger.Logger.LogDebugMessage("T3:" + DateTime.Now);
        //    List<WaveType> _list = new List<WaveType>();
        //    int maxCount = 1000;
        //    if (list.Count > maxCount)
        //    {
        //        list = list.OrderByDescending(item => item.AcquisitionTime).ToList();
        //        for (int i = 0; i < maxCount; i++)
        //        {
        //            _list.Add(list[i]);
        //        }
        //    }
        //    else
        //    {
        //        _list = list;
        //    }
        //    string result = Newtonsoft.Json.JsonConvert.SerializeObject(_list);
        //    CMSFramework.Logger.Logger.LogDebugMessage("_list-result:" + result.Length);

        //    //result = ZipUtil.Compress(result);
        //    //CMSFramework.Logger.Logger.LogDebugMessage("T5:" + DateTime.Now);
        //    return Json(_list, JsonRequestBehavior.AllowGet);
        //}

        //public JsonResult GetPageWaveDataByTime(DateTime _beginTime, DateTime _endTime, string WindTurbineId, string waveType, int pageIndex, int pageSize, string SortColumn, string SortType,string componentName, string mesaLocID)
        //{
        //    records record = new records();
        //    List<WaveType> list = new List<WaveType>();
        //    _endTime = _endTime.AddDays(1);
        //    //List<RotSpeedWaveData> RotSpdFormData = GetDayDbRotSpdWaveFromDataList(WindTurbineId, _beginTime, _endTime);
        //    List<VibWaveFormData_DB> waveFormData = null;// GetDayDbWaveFromDataList(WindTurbineId, _beginTime, _endTime);
        //    int DataLength = 0;

        //    // S1. 获取数据（筛选）
        //    // 1、waveType = '全部' 
        //    //      1.1 按照时间、机组获取数据（WFData)
        //    //      1.2 按照时间、机组获取数据 RotSpeedData）
        //    //      1.3 合并数据

        //    // 2、waveType = '转速'
        //    //       按照时间、机组获取数据 RotSpeedData）
        //     //< option value = "-1" > 全部 </ option >
 
        //     //< option value = "3" > 电流 </ option >
  
        //     //< option value = "2" > 电压 </ option >
   
        //     //< option value = "0" > 加速度 </ option >
    
        //     //< option value = "4" > 温度 </ option >
     
        //     //< option value = "1" > 转速 </ option >

        //    // 3、其他
        //    //      1.1 按照时间、机组、波形类型获取数据（WFData)

        //    // S2. 获取数据（排序）

        //    // S3. 获取Page列表

        //    using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HisDB, ConfigInfo.DBConnName))
        //    {
        //        List<MySqlParameter> sqlParametersList = new List<MySqlParameter>() {
        //        new MySqlParameter { ParameterName = "WindTurbineID", Value = WindTurbineId },
        //                                                                 new MySqlParameter { ParameterName = "beginTime", Value = _beginTime },
        //                                                                 new MySqlParameter { ParameterName = "endTime", Value = _endTime }
        //        };
        //        string strSql = "", strCountSql = "";
        //        //string strSqlhour = "", strCountSqlhour = "";
        //        //string strSqlhis = "", strCountSqlhis = "";
        //        string dataColumn = " AcquisitionTime,SignalType,DAUChannelID,WaveLength,SampleRate,WindTurbineID,MeasLocationID,ComponentName,LocationSection ";
        //        string rotSpeedDataColunm = " AcquisitionTime,1 SignalType,0 DAUChannelID,WaveLength,0 SampleRate,WindTurbineID,MeasLocationID,'电机' ComponentName,'电机转速' LocationSection ";
        //        if (waveType == "-1")
        //        {
        //            /*
        //            strSql = string.Format(@"select * from (
        //                                                    select {0} from wfdataday
        //                                                    union
        //                                                    select {1} from rotspeedwavedataday
        //                                                    ) t1 
        //                                                    where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime ", dataColumn, rotSpeedDataColunm);
        //            strCountSql = string.Format(@" select count(0) from  (
        //                                                    select {0} from wfdataday
        //                                                    union
        //                                                    select {1} from rotspeedwavedataday
        //                                                    ) t1  where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime ", dataColumn, rotSpeedDataColunm);
        //             * */
        //            CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-准备请求数据:" + DateTime.Now);
        //            //UNION 查询连表数据太慢，暂时不启用
        //            List<RotSpeedWaveData> RotSpdFormData = ctx.RotSpdWaves.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //         wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
        //            //List<VibWaveFormData> waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //            //wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();

        //            //根据测量位置筛选
        //            List<VibWaveFormData> waveFormDataVib = new List<VibWaveFormData>();
        //            if (componentName == ""&& mesaLocID == "")
        //            {
        //                waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();

        //                //查找小时库
        //                using(WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
        //                    if(wavehour != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
        //                    }
        //                }

        //                //查找整天库
        //                using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
        //                    if (waveday != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
        //                    }
        //                }
        //            }else if(componentName != "" && mesaLocID == "")
        //            {
        //                waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();

        //                //查找小时库
        //                using (WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();
        //                    if (wavehour != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
        //                    }
        //                }

        //                //查找整天库
        //                using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();
        //                    if (waveday != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
        //                    }
        //                }
        //            }
        //            else
        //            {
        //                waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();

        //                //查找小时库
        //                using (WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();
        //                    if (wavehour != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
        //                    }
        //                }

        //                //查找整天库
        //                using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
        //                {
        //                    List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
        //                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();
        //                    if (waveday != null)
        //                    {
        //                        waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
        //                    }
        //                }
        //            }


        //            CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-请求数据完成:" + DateTime.Now);
        //            waveFormData = new List<VibWaveFormData_DB>();
        //            //振动数据转换
        //            ConvertVibList(waveFormDataVib, waveFormData);
        //            //转速数据转换
        //            ConvertRotSpdList(RotSpdFormData, waveFormData);
        //            CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-合并数据完成:" + DateTime.Now);
        //            if (string.IsNullOrEmpty(SortColumn))
        //            {
        //                //如果没有排序，就默认按时间降序排列
        //                waveFormData = waveFormData.OrderByDescending(item => item.AcquisitionTime).ToList();
        //            }
        //            else if (SortColumn == "StrAcquisitionTime")
        //            {
        //                if (SortType.ToUpper() == "DESC")
        //                {
        //                    waveFormData = waveFormData.OrderByDescending(item => item.AcquisitionTime).ToList();
        //                }
        //                else
        //                {
        //                    waveFormData = waveFormData.OrderBy(item => item.AcquisitionTime).ToList();
        //                }
        //            }
        //            else if (SortColumn == "WaveformTypeName")
        //            {
        //                if (SortType.ToUpper() == "DESC")
        //                {
        //                    waveFormData = waveFormData.OrderByDescending(item => item.SignalType).ToList();
        //                }
        //                else
        //                {
        //                    waveFormData = waveFormData.OrderBy(item => item.SignalType).ToList();
        //                }
        //            }
        //            else if (SortColumn == "MeasLocName")
        //            {
        //                if (SortType.ToUpper() == "DESC")
        //                {
        //                    waveFormData = waveFormData.OrderByDescending(item => item.ComponentName).ThenByDescending(item => item.LocationSection).ToList();
        //                }
        //                else
        //                {
        //                    waveFormData = waveFormData.OrderBy(item => item.ComponentName).ThenBy(item => item.LocationSection).ToList();
        //                }
        //            }
        //            CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-排序数据完成:" + DateTime.Now);
        //            DataLength = waveFormData.Count;
        //            int start = pageIndex * pageSize + 1, end = 0;
        //            //list分页
        //            end = pageIndex * pageSize + pageSize;
        //            if (end > DataLength)
        //            {
        //                end = DataLength;
        //            }
        //            List<VibWaveFormData_DB> TempFormData = new List<VibWaveFormData_DB>();
        //            for (int i = start - 1; i < end; i++)
        //            {
        //                TempFormData.Add(waveFormData[i]);
        //            }
        //            waveFormData = TempFormData;
        //            CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-分页数据完成:" + DateTime.Now);
        //        }
        //        else if (waveType == "1")
        //        {
        //            //获取转速数据
        //            strCountSql = " select count(0) from rotspeedwavedataday union (select * from rotspeedwavedatahour union select * from rotspeedwavedatahis) where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime ";
        //            strSql = " select " + rotSpeedDataColunm + " from rotspeedwavedataday  union (select * from rotspeedwavedatahour union select * from rotspeedwavedatahis) where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime ";
        //        }
        //        else
        //        {
        //            //获取振动数据，电流，电压，加速度，温度
        //            strCountSql = " select count(0) from wfdataday where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime and SignalType=@SignalType ";
        //            strSql = " select " + dataColumn + " from wfdataday where WindTurbineID=@WindTurbineID and  AcquisitionTime >= @beginTime and  AcquisitionTime < @endTime and SignalType=@SignalType ";
        //            sqlParametersList.Add(new MySqlParameter { ParameterName = "SignalType", Value = waveType });
        //        }
        //        if (waveType != "-1")
        //        {
        //            System.Data.Entity.Infrastructure.DbRawSqlQuery<int> resultCount = ctx.Database.SqlQuery<int>(strCountSql, sqlParametersList.ToArray());
        //            DataLength = resultCount.FirstOrDefault();
        //            sqlParametersList.Add(new MySqlParameter { ParameterName = "pagebegin", Value = pageIndex * pageSize });
        //            sqlParametersList.Add(new MySqlParameter { ParameterName = "pageSize", Value = pageSize });
        //            strSql += " ORDER BY ";
        //            if (string.IsNullOrEmpty(SortColumn))
        //            {
        //                //如果没有排序，就默认按时间降序排列
        //                strSql += " AcquisitionTime DESC ";
        //            }
        //            else if (SortColumn == "StrAcquisitionTime")
        //            {
        //                strSql += " AcquisitionTime " + SortType;
        //            }
        //            else if (SortColumn == "WaveformTypeName")
        //            {
        //                strSql += " SignalType " + SortType;
        //            }
        //            else if (SortColumn == "MeasLocName")
        //            {
        //                strSql += " ComponentName " + SortType + ",LocationSection " + SortType;
        //            }
        //            strSql += " LIMIT @pagebegin,@pageSize ";
        //            waveFormData = ctx.Database.SqlQuery<VibWaveFormData_DB>(strSql, sqlParametersList.ToArray()).ToList<VibWaveFormData_DB>();
        //        }
        //    }
        //    List<WRD.AMS.BusinessEntities.DevTrainLine> trainLineList = null;
        //    List<WRD.AMS.BusinessEntities.DevTrainNumber> trainNumberList = null;
        //    //using (WRD.AMS.EF.AMSContext ctx = new WRD.AMS.EF.AMSContext(ConfigInfo.DBConnName))
        //    //{
        //    //    trainLineList = ctx.TrainLines.ToList();
        //    //    trainNumberList = ctx.TrainLineNumbers.ToList();
        //    //}
        //    List<WindPark> windParkList = null;
        //    List<WindTurbine> turbineList = null;
        //    List<MeasLoc_Vib> measLocVib = MeasLocationDA.GetAllMeasLocations();
        //    using (CMSFramework.EF.DevContext ctx = new DevContext(ConfigInfo.DBConnName))
        //    {
        //        windParkList = ctx.DevWindParks.ToList();
        //        turbineList = ctx.DevWindTurbines.ToList();
        //    }
        //    JsonWaveInfo jsonValue = null;
        //    // waveFormData = waveFormData.OrderBy(item => item.WindTurbineID).ThenBy(item => item.ComponentName).ThenBy(item => item.MeasLocationID).ThenBy(item => item.AcquisitionTime).ToList();
        //    CMSFramework.Logger.Logger.LogDebugMessage("T2:" + DateTime.Now);
        //    waveFormData.ForEach(item =>
        //    {
        //        //把波形实体转换成网页实体
        //        WindTurbine turbine = turbineList.Find(tur => tur.WindTurbineID == item.WindTurbineID);
        //        WindPark park = windParkList.Find(tur => tur.WindParkID == turbine.WindParkID);
        //        WaveType wavetype = new WaveType();
        //        //wavetype.TrainName = GetTrainName(park, trainLineList, trainNumberList);
        //        wavetype.TrainName ="";
        //        wavetype.StrAcquisitionTime = item.AcquisitionTime.ToString();
        //        if (item.SignalType == "1")
        //        {
        //            wavetype.WaveformTypeName = "转速";
        //            wavetype.MeasLocName = "电机转速";
        //            wavetype.StringSampleRate = "";
        //        }
        //        else
        //        {
        //            MeasLoc_Vib measLoc = measLocVib.Find(meas => meas.MeasLocationID == item.MeasLocationID);
        //            //wavetype.WaveformTypeName = AppFramework.Utility.EnumHelper.GetDescription((WRD.AMS.BusinessEntities.EnumSensorType)int.Parse(item.SignalType)).Replace("传感器", ""); // item.WaveformType
        //            wavetype.WaveformTypeName = item.SignalType.Replace("传感器", "");
        //            wavetype.MeasLocName = measLoc.MeasLocName + "(通道" + item.DAUChannelID + ")";
        //            wavetype.StringSampleRate = item.SampleRate.ToString();
        //        }
        //        wavetype.WaveLength = item.WaveLength;
        //        wavetype.WindTurbineID = item.WindTurbineID;
        //        wavetype.AcquisitionTime = item.AcquisitionTime;
        //        wavetype.MeasLocationID = item.MeasLocationID;
        //        jsonValue = new JsonWaveInfo()
        //        {
        //            AcquisitionTime = item.AcquisitionTime,
        //            MeasLocName = wavetype.MeasLocName,
        //            TrainName = wavetype.TrainName,
        //            MeasLocationID = item.MeasLocationID,
        //            WindTurbineID = item.WindTurbineID
        //        };
        //        wavetype.GUID = Newtonsoft.Json.JsonConvert.SerializeObject(jsonValue);
        //        list.Add(wavetype);
        //        /*  if (RotSpdFormData != null && RotSpdFormData.Count > 0)
        //          {
        //              RotSpeedWaveData RotSpdWave = RotSpdFormData.Find(rot => rot.WindTurbineID == item.WindTurbineID && rot.AcquisitionTime == item.AcquisitionTime);
        //              if (RotSpdWave != null)
        //              {
        //                  WaveType wavetypeRotSpd = new WaveType();
        //                  wavetypeRotSpd.TrainName = wavetype.TrainName;
        //                  wavetypeRotSpd.StrAcquisitionTime = item.AcquisitionTime.ToString();
        //                  wavetypeRotSpd.WaveformTypeName = "转速";
        //                  wavetypeRotSpd.MeasLocName = "电机转速";// measLoc.MeasLocName;
        //                  wavetypeRotSpd.WaveLength = RotSpdWave.WaveLength.Value;
        //                  wavetypeRotSpd.StringSampleRate = "";
        //                  wavetypeRotSpd.WindTurbineID = item.WindTurbineID;
        //                  wavetypeRotSpd.AcquisitionTime = item.AcquisitionTime;
        //                  wavetypeRotSpd.MeasLocationID = RotSpdWave.MeasLocationID;
        //                  jsonValue = new JsonWaveInfo()
        //                  {
        //                      AcquisitionTime = item.AcquisitionTime,
        //                      MeasLocationID = RotSpdWave.MeasLocationID,
        //                      WindTurbineID = item.WindTurbineID,
        //                      MeasLocName = "转速",
        //                      TrainName = wavetype.TrainName
        //                  };
        //                  string GUIDCode = Newtonsoft.Json.JsonConvert.SerializeObject(jsonValue);
        //                  wavetypeRotSpd.GUID = GUIDCode;
        //                  list.Add(wavetypeRotSpd);
        //                  RotSpdFormData.Remove(RotSpdWave);
        //              }
        //          }
        //          */
        //    });
        //    record.recordsTotal = DataLength;
        //    record.recordsFiltered = DataLength;
        //    record.data = list;
        //    return Json(record, JsonRequestBehavior.AllowGet);
        //}

        public JsonResult GetPageWaveDataByTimeV2(DateTime _beginTime, DateTime _endTime, string WindTurbineId, string waveType, int pageIndex, int pageSize, string SortColumn, string SortType, string componentName, string mesaLocID)
        {
            records record = new records();

            _endTime = _endTime.AddDays(1);
            List<VibWaveFormData_DB> waveFormData = new List<VibWaveFormData_DB>();
            int DataLength = 0;

            //默认排列方式为时间降序排列ToUpper() == "DESC"
            if (string.IsNullOrEmpty(SortColumn) && string.IsNullOrEmpty(SortType))
            {
                SortColumn = "StrAcquisitionTime"; SortType = "DESC";
            }

            // 查询对应的波形列表
            switch (waveType)
            {
                case "-1": // 振动，不包含转速和工况
                    waveFormData = GetVibFromData(_beginTime, _endTime, WindTurbineId, pageIndex, pageSize, componentName,mesaLocID,SortColumn,SortType);
                    //ConvertVibList(GetVibWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.HisDB), waveFormData);
                    //ConvertVibList(GetVibWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.DayDB), waveFormData);
                    //ConvertVibList(GetVibWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.HourDB), waveFormData);
                    break;
                case "1":  // 转速
                    ConvertRotSpdList(GetRotSpdWave(_beginTime, _endTime, WindTurbineId, EnumDataSource.HisDB), waveFormData);
                    ConvertRotSpdList(GetRotSpdWave(_beginTime, _endTime, WindTurbineId, EnumDataSource.DayDB), waveFormData);
                    ConvertRotSpdList(GetRotSpdWave(_beginTime, _endTime, WindTurbineId, EnumDataSource.HourDB), waveFormData);
                    //对waveformdata排序
                    waveFormData = SetSortWaveList(waveFormData,SortColumn,SortType);
                    break;
                case "5": // 工况
                    // waveFormData = GetWKFromData(_beginTime, _endTime, WindTurbineId, pageIndex, pageSize, componentName, mesaLocID);
                    ConvertWKList(GetWKWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.HisDB),waveFormData);
                    ConvertWKList(GetWKWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.DayDB), waveFormData);
                    ConvertWKList(GetWKWaveData(_beginTime, _endTime, WindTurbineId, EnumDataSource.HourDB), waveFormData);
                    waveFormData = SetSortWaveList(waveFormData, SortColumn, SortType);
                    break;
                default:
                    break;
            }

            List<MeasLoc_Vib> measLocVib = MeasLocationDA.GetAllMeasLocations();

            List<MeasLoc_Process> process = MeasLocationDA.GetWfMeasLocationByTurbineID();

            JsonWaveInfo jsonValue = null;

            List<WaveType> list = new List<WaveType>();
            waveFormData.ForEach(item =>
            {
                //把波形实体转换成网页实体
                WaveType wavetype = new WaveType();
                wavetype.TrainName = "";
                wavetype.StrAcquisitionTime = item.AcquisitionTime.ToString();

                switch (item.SignalType)
                {
                    case "1":
                        wavetype.WaveformTypeName = "转速";
                        wavetype.MeasLocName = "电机转速";
                        wavetype.StringSampleRate = "";
                        break;
                    case "-1":
                        MeasLoc_Process measlocprocess = process.Find(p => p.MeasLocationID == item.MeasLocationID);
                        wavetype.MeasLocName = "工况"; 
                        if (measlocprocess != null)
                        {
                            wavetype.WaveformTypeName = measlocprocess.MeasLocName;
                        }else
                        {
                            if (item.MeasLocationID.Contains("__01Temp"))
                            {
                                wavetype.WaveformTypeName = "叶片1温度";
                            }else if (item.MeasLocationID.Contains("__02Temp"))
                            {
                                wavetype.WaveformTypeName = "叶片2温度";
                            }else if (item.MeasLocationID.Contains("__03Temp"))
                            {
                                wavetype.WaveformTypeName = "叶片3温度";
                            }else
                            {
                                wavetype.WaveformTypeName = item.MeasLocationID;
                            }
                        }
                        break;
                    default:
                        MeasLoc_Vib measLoc = measLocVib.Find(meas => meas.MeasLocationID == item.MeasLocationID);
                        //wavetype.WaveformTypeName = AppFramework.Utility.EnumHelper.GetDescription((WRD.AMS.BusinessEntities.EnumSensorType)int.Parse(item.SignalType)).Replace("传感器", ""); // item.WaveformType
                        // 由枚举改为字符串显示
                        //wavetype.WaveformTypeName = AppFramework.Utility.EnumHelper.GetDescription((WaveManager.MyEnumWaveFormType)int.Parse(item.SignalType)).Replace("传感器", ""); // item.WaveformType
                        wavetype.WaveformTypeName = item.SignalType; // item.WaveformType
                        //wavetype.WaveformTypeName = item.SignalType.Replace("传感器", "");
                        if (measLoc != null)
                        {
                            wavetype.MeasLocName = measLoc.MeasLocName + "(通道" + item.DAUChannelID + ")";
                        }else
                        {
                            wavetype.MeasLocName = "未知测量位置" + "(通道" + item.DAUChannelID + ")";
                        }
                        wavetype.StringSampleRate = item.SampleRate.ToString();
                        break;
                }

                wavetype.WaveLength = item.WaveLength;
                wavetype.WindTurbineID = item.WindTurbineID;
                wavetype.AcquisitionTime = item.AcquisitionTime;
                wavetype.MeasLocationID = item.MeasLocationID;
                jsonValue = new JsonWaveInfo()
                {
                    AcquisitionTime = item.AcquisitionTime,
                    MeasLocName = wavetype.MeasLocName,
                    TrainName = wavetype.TrainName,
                    MeasLocationID = item.MeasLocationID,
                    WindTurbineID = item.WindTurbineID
                };
                wavetype.GUID = Newtonsoft.Json.JsonConvert.SerializeObject(jsonValue);
                list.Add(wavetype);
            });

            List<WaveType> TempFormData = new List<WaveType>();
            // 分页处理
            DataLength = list.Count;
            int start = pageIndex * pageSize + 1, end = 0;
            //list分页
            end = pageIndex * pageSize + pageSize;
            if (end > DataLength)
            {
                end = DataLength;
            }
            for (int i = start - 1; i < end; i++)
            {
                TempFormData.Add(list[i]);
            }
            list = TempFormData;


            record.recordsTotal = DataLength;
            record.recordsFiltered = DataLength;
            record.data = list;
            return Json(record);
        }

        private List<VibWaveFormData_DB> SetSortWaveList(List<VibWaveFormData_DB> waveFormData,string SortColumn,string SortType)
        {
            if(SortColumn == "StrAcquisitionTime")
            {
                if (SortType.ToUpper() == "DESC")
                {
                    waveFormData = waveFormData.OrderByDescending(item => item.AcquisitionTime).ToList();
                }
                else
                {
                    waveFormData = waveFormData.OrderBy(item => item.AcquisitionTime).ToList();
                }
            }
            return waveFormData;
        }

        /// <summary>
        /// 工况数据对象转换
        /// </summary>
        /// <param name="list"></param>
        /// <param name="waveFormData"></param>
        private void ConvertWKList(List<WorkConditionWaveFormData> list, List<VibWaveFormData_DB> waveFormData)
        {
            list.ForEach(item =>
            {
                waveFormData.Add(new VibWaveFormData_DB()
                {
                    AcquisitionTime = item.AcquisitionTime,
                    SignalType = "-1",
                    WaveLength = item.WaveLength,
                    WindTurbineID = item.WindTurbineID,
                    MeasLocationID = item.MeasLocationID,
                    ComponentName = item.ComponentName,
                    LocationSection = item.LocationSection
                });
            });
        }

        private List<VibWaveFormData_DB> GetWKFromData(DateTime beginTime, DateTime endTime, string windTurbineId, int pageIndex, int pageSize, string componentName, string mesaLocID)
        {
            throw new NotImplementedException();
        }

        private static List<RotSpeedWaveData> GetRotSpdWave(DateTime beginTime, DateTime endTime, string windTurbineId,EnumDataSource dbType)
        {
            List<RotSpeedWaveData> wfList;
            using (WFDataContext ctx = WFDataContext.Factory(dbType, ConfigInfo.DBConnName))
            {
                wfList = ctx.RotSpdWaves.Where(obj => obj.WindTurbineID == windTurbineId && obj.AcquisitionTime > beginTime && obj.AcquisitionTime < endTime).ToList();
            }

            return wfList;
        }

        private static List<VibWaveFormData> GetVibWaveData(DateTime beginTime, DateTime endTime, string windTurbineId, EnumDataSource dbType)
        {
            List<VibWaveFormData> wfList;
            using (WFDataContext ctx = WFDataContext.Factory(dbType, ConfigInfo.DBConnName))
            {
                wfList = ctx.VibWaveForms.Where(obj => obj.WindTurbineID == windTurbineId && obj.AcquisitionTime > beginTime && obj.AcquisitionTime < endTime).ToList();
            }

            return wfList;
        }

        private static List<WorkConditionWaveFormData> GetWKWaveData(DateTime beginTime, DateTime endTime, string windTurbineId, EnumDataSource dbType)
        {
            List<WorkConditionWaveFormData> wfList;
            using (WFDataContext ctx = WFDataContext.Factory(dbType, ConfigInfo.DBConnName))
            {
                wfList = ctx.WorkConditionWaves.Where(obj => obj.WindTurbineID == windTurbineId && obj.AcquisitionTime > beginTime && obj.AcquisitionTime < endTime).ToList();
            }

            return wfList;
        }

        /// <summary>
        /// 获取振动波形数据
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="windTurbineId"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="componentName"></param>
        /// <param name="mesaLocID"></param>
        /// <returns></returns>
        private List<VibWaveFormData_DB> GetVibFromData(DateTime _beginTime, DateTime _endTime, string WindTurbineId, int pageIndex, int pageSize, string componentName, string mesaLocID, string SortColumn, string SortType)
        {
            List<VibWaveFormData_DB> waveFormData = new List<VibWaveFormData_DB>();
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HisDB, ConfigInfo.DBConnName))
            {
                List<MySqlParameter> sqlParametersList = new List<MySqlParameter>() {
                new MySqlParameter { ParameterName = "WindTurbineID", Value = WindTurbineId },
                                                                         new MySqlParameter { ParameterName = "beginTime", Value = _beginTime },
                                                                         new MySqlParameter { ParameterName = "endTime", Value = _endTime }
                };
                string strSql = "", strCountSql = "";
                //string strSqlhour = "", strCountSqlhour = "";
                //string strSqlhis = "", strCountSqlhis = "";
                string dataColumn = " AcquisitionTime,SignalType,DAUChannelID,WaveLength,SampleRate,WindTurbineID,MeasLocationID,ComponentName,LocationSection ";
                string rotSpeedDataColunm = " AcquisitionTime,1 SignalType,0 DAUChannelID,WaveLength,0 SampleRate,WindTurbineID,MeasLocationID,'电机' ComponentName,'电机转速' LocationSection ";

                CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-准备请求数据:" + DateTime.Now);
                //UNION 查询连表数据太慢，暂时不启用
             //   List<RotSpeedWaveData> RotSpdFormData = ctx.RotSpdWaves.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
             //wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
                //List<VibWaveFormData> waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                //wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();

                //根据测量位置筛选
                List<VibWaveFormData> waveFormDataVib = new List<VibWaveFormData>();
                if (componentName == "" && mesaLocID == "")
                {
                    waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();

                    //查找小时库
                    using (WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
                        if (wavehour != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
                        }
                    }

                    //查找整天库
                    using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
                        if (waveday != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
                        }
                    }
                }
                else if (componentName != "" && mesaLocID == "")
                {
                    waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();

                    //查找小时库
                    using (WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();
                        if (wavehour != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
                        }
                    }

                    //查找整天库
                    using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.ComponentName == componentName).ToList();
                        if (waveday != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
                        }
                    }
                }
                else
                {
                    waveFormDataVib = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();

                    //查找小时库
                    using (WFDataContext ctxhour = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> wavehour = ctxhour.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();
                        if (wavehour != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(wavehour).ToList();
                        }
                    }

                    //查找整天库
                    using (WFDataContext ctxday = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
                    {
                        List<VibWaveFormData> waveday = ctxday.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == WindTurbineId &&
                        wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime && wf.MeasLocationID == mesaLocID).ToList();
                        if (waveday != null)
                        {
                            waveFormDataVib = waveFormDataVib.Union(waveday).ToList();
                        }
                    }
                }


                CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-请求数据完成:" + DateTime.Now);
                waveFormData = new List<VibWaveFormData_DB>();
                //振动数据转换
                ConvertVibList(waveFormDataVib, waveFormData);
                //转速数据转换
                //ConvertRotSpdList(RotSpdFormData, waveFormData);
                CMSFramework.Logger.Logger.LogDebugMessage("全部-T1-合并数据完成:" + DateTime.Now);
                if (string.IsNullOrEmpty(SortColumn))
                {
                    //如果没有排序，就默认按时间降序排列
                    waveFormData = waveFormData.OrderByDescending(item => item.AcquisitionTime).ToList();
                }
                else if (SortColumn == "StrAcquisitionTime")
                {
                    if (SortType.ToUpper() == "DESC")
                    {
                        waveFormData = waveFormData.OrderByDescending(item => item.AcquisitionTime).ToList();
                    }
                    else
                    {
                        waveFormData = waveFormData.OrderBy(item => item.AcquisitionTime).ToList();
                    }
                }
                else if (SortColumn == "WaveformTypeName")
                {
                    if (SortType.ToUpper() == "DESC")
                    {
                        waveFormData = waveFormData.OrderByDescending(item => item.SignalType).ToList();
                    }
                    else
                    {
                        waveFormData = waveFormData.OrderBy(item => item.SignalType).ToList();
                    }
                }
                else if (SortColumn == "MeasLocName")
                {
                    if (SortType.ToUpper() == "DESC")
                    {
                        waveFormData = waveFormData.OrderByDescending(item => item.ComponentName).ThenByDescending(item => item.LocationSection).ToList();
                    }
                    else
                    {
                        waveFormData = waveFormData.OrderBy(item => item.ComponentName).ThenBy(item => item.LocationSection).ToList();
                    }
                }
            }
            return waveFormData;
        }

        private void ConvertVibList(List<VibWaveFormData> waveFormDataVib, List<VibWaveFormData_DB> List)
        {
            waveFormDataVib.ForEach(item =>
            {
                List.Add(new VibWaveFormData_DB()
                {
                    AcquisitionTime = item.AcquisitionTime,
                    SignalType = item.SignalType,
                    DAUChannelID = item.DAUChannelID,
                    WaveLength = item.WaveLength,
                    SampleRate = item.SampleRate,
                    WindTurbineID = item.WindTurbineID,
                    MeasLocationID = item.MeasLocationID,
                    ComponentName = item.ComponentName,
                    LocationSection = item.LocationSection
                });
            });
        }

        private void ConvertRotSpdList(List<RotSpeedWaveData> RotSpdFormData, List<VibWaveFormData_DB> List)
        {
            RotSpdFormData.ForEach(item =>
            {
                List.Add(new VibWaveFormData_DB()
                {
                    AcquisitionTime = item.AcquisitionTime,
                    SignalType = "1",
                    WaveLength = item.WaveLength.Value,
                    WindTurbineID = item.WindTurbineID,
                    MeasLocationID = item.MeasLocationID,
                    ComponentName = "转速",
                    LocationSection = "转速"
                });
            });
        }
        #endregion
    }

    public class records
    {
        public string draw { get; set; }
        public int recordsTotal { get; set; }
        public int recordsFiltered { get; set; }
        public List<WaveType> data { get; set; }
    }

    public class VibWaveFormData_DB
    {
        //AcquisitionTime,SignalType,DAUChannelID,WaveLength,SampleRate,WindTurbineID,MeasLocationID
        public DateTime AcquisitionTime { get; set; }
        public string MeasLocationID { get; set; }
        public double SampleRate { get; set; }
        public int WaveLength { get; set; }
        public string WindTurbineID { get; set; }
        public int DAUChannelID { get; set; }
        public string SignalType { get; set; }
        public string ComponentName { get; set; }
        public string LocationSection { get; set; }
    }
}
