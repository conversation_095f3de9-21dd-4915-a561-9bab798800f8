﻿using CMSFramework.TypeDef;
using WindCMS.GWServer.Agent;
using WindCMS.GWServer.Entity;
using WTCMSLive.BusinessModel;

namespace WindCMS.GWServer.ListenerServer;

public class ListenerWriteHandlerProxy : IListenerWriteHandler
{
    private TcpListenerAgent Agent { get; }
    
    /// <summary>
    /// 监听写处理对象
    /// </summary>
    private ListenerWriteHandler ListenerWriteHandler { get; }
    
    public ListenerWriteHandlerProxy(TcpListenerAgent tcpListenerAgent)
    {
        Agent = tcpListenerAgent;
        ListenerWriteHandler = new ListenerWriteHandler(tcpListenerAgent);
    }
    
    /// <summary>
    /// 内部类，用于管理 HasInteraction 状态
    /// </summary>
    private class InteractionScope : IDisposable
    {
        private readonly TcpListenerAgent _agent;
 
        public InteractionScope(TcpListenerAgent agent)
        {
            _agent = agent;
            _agent.HasInteraction = true;
            _agent.ResultExecution = true;
            
            // 与读取数据线程刷新频率同步（添加两个周期延迟确保能够成功通知到）
            Thread.Sleep(200);
        }
 
        public void Dispose()
        {
            _agent.HasInteraction = false;
            _agent.LastCommunicationTime = DateTime.Now;

            if (!_agent.ResultExecution)
            {
                // 执行失败后认为断开连接
                _agent.ConnectReady = false;
                // 设置DAU状态
                var windDau = _agent.DauWorkContext.Dau;
                if (windDau != null)
                {
                    windDau.DauOnOffStatus = EnumDauOnOffStatus.Off;
                }
                // 出现通讯异常后关闭客户端
                TcpListenerServer.CloseTcpClient(_agent);
            }
        }
    }
 
    // 封装公共方法，减少重复代码
    private T ExecuteWithInteraction<T>(Func<T> action)
    {
        using (new InteractionScope(Agent))
        {
            return action();
        }
    }
 
    private void ExecuteWithInteraction(Action action)
    {
        using (new InteractionScope(Agent))
        {
            action();
        }
    }

    public bool StartDaq()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.StartDaq());
    }

    public bool StopDaq()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.StopDaq());
    }

    public bool SetConfigToolNetworkParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SetConfigToolNetworkParameter());
    }

    public bool GetConfigToolNetworkParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetConfigToolNetworkParameter());
    }

    public bool SetDauBaseParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SetDauBaseParameter());
    }

    public bool GetDauBaseParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetDauBaseParameter());
    }

    public bool SetDauCollectionStrategyRequest()
    {
        return ExecuteWithInteraction(() =>
            ListenerWriteHandler.SetDauCollectionStrategyRequest());
    }

    public bool GetDauCollectionStrategy()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetDauCollectionStrategy());
    }

    public bool SetDauPushParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SetDauPushParameter());
    }

    public bool GetDauPushParameter()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetDauPushParameter());
    }

    public bool SetDauEquipmentWarranty()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SetDauEquipmentWarranty());
    }

    public bool GetDauEquipmentWarranty()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetDauEquipmentWarranty());
    }

    public bool StartGetRecorded()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.StartGetRecorded());
    }

    public bool SendMDF(byte[] wtLiveDbByteArray)
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SendMDF(wtLiveDbByteArray));
    }

    public bool SetDauContext(byte[] wtLiveDbByteArray, int dauContextConfigRequestArrayLength = 1010)
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.SetDauContext(wtLiveDbByteArray, dauContextConfigRequestArrayLength));
    }

    public byte[] GetDauContext()
    {
        return ExecuteWithInteraction(() => ListenerWriteHandler.GetDauContext());
    }
}