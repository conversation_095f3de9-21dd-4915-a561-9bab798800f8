﻿using System.Collections.Concurrent;

namespace WindCMS.GWServer.Entity;

/// <summary>
/// 定长队列
/// </summary>
/// <typeparam name="T"></typeparam>
public class FixedSizeConcurrentQueue<T>
{
    private readonly ConcurrentQueue<T> _queue = new();
    private readonly int _maxSize;
 
    public FixedSizeConcurrentQueue(int maxSize)
    {
        if (maxSize <= 0)
            throw new ArgumentException("Max size must be greater than zero.", nameof(maxSize));
        
        _maxSize = maxSize;
    }
 
    public void Enqueue(T item)
    {
        // 如果队列已满，先出队一个元素
        if (_queue.Count >= _maxSize)
        {
            _queue.TryDequeue(out _);
        }
        
        _queue.Enqueue(item);
    }
 
    public bool TryDequeue(out T result)
    {
        return _queue.TryDequeue(out result);
    }
 
    public bool TryPeek(out T result)
    {
        return _queue.TryPeek(out result);
    }
 
    public int Count => _queue.Count;
}