﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: Guo<PERSON>aile
    // Create: 2013-09-12
    /// <summary>
    /// 数据实体转换， 从业务层实体转换为DA层实体
    /// </summary>
    public static class ConvertEntityBusinessToDAData
    {

        #region 测量事件，波形

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件, RT
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static WFDataRTMeasEvent ConvertMeasEventWFRT(WTCMSLive.BusinessEntity.MeasEvent_Wave _ev)
        {
            return ConvertMeasEventWF(new WFDataRTMeasEvent(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件, Hour
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static WFDataMeasEventHour ConvertMeasEventWFHour(WTCMSLive.BusinessEntity.MeasEvent_Wave _ev)
        {
            return ConvertMeasEventWF(new WFDataMeasEventHour(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件, Day
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static WFDataMeasEventDay ConvertMeasEventWFDay(WTCMSLive.BusinessEntity.MeasEvent_Wave _ev)
        {
            return ConvertMeasEventWF(new WFDataMeasEventDay(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件, His
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static WFDataMeasEventHi ConvertMeasEventWFHis(WTCMSLive.BusinessEntity.MeasEvent_Wave _ev)
        {
            return ConvertMeasEventWF(new WFDataMeasEventHi(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件, Alarm
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static WFDataMeasEventAlarm ConvertMeasEventWFAlarm(WTCMSLive.BusinessEntity.MeasEvent_Wave _ev)
        {
            return ConvertMeasEventWF(new WFDataMeasEventAlarm(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertMeasEventWF<T>(
            T _entity,
            WTCMSLive.BusinessEntity.MeasEvent_Wave _ev) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);
            type.GetProperty("WindTurbineID").SetValue(_entity,string.Copy(_ev.WindTurbineID), null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_ev.MeasDefinitionID), null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _ev.AcquisitionTime, null);
            type.GetProperty("WkConDataNum").SetValue(_entity,Convert.ToInt16(_ev.WkConDataNum), null);
            type.GetProperty("RotSpdWaveFromNum").SetValue(_entity, Convert.ToInt16(_ev.RotSpdWaveFromNum), null);
            type.GetProperty("WaveFormNum").SetValue(_entity, Convert.ToInt16(_ev.WaveFormNum), null);
            type.GetProperty("SVMWaveFormNum").SetValue(_entity, Convert.ToInt16(_ev.SVMWaveFormNum), null);
            type.GetProperty("WkConLevelCode").SetValue(_entity, Convert.ToInt16(_ev.OutPowerBandCode), null);
            type.GetProperty("AlarmDegree").SetValue(_entity, Convert.ToInt16(_ev.AlarmDegree), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_ev.Data_Qual_Type, null);
            return _entity;
        }

        #endregion 测量事件，波形

        #region 测量事件，特征值

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换特征值测量事件, RT
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static EVDataRTMeasEvent ConvertMeasEventEVRT(WTCMSLive.BusinessEntity.MeasEvent_EigenValue _ev)
        {
            return ConvertMeasEventEV(new EVDataRTMeasEvent(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换特征值测量事件, Trend
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static EVDataTrendMeasEvent ConvertMeasEventEVTrend(WTCMSLive.BusinessEntity.MeasEvent_EigenValue _ev)
        {
            return ConvertMeasEventEV(new EVDataTrendMeasEvent(), _ev);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换特征值测量事件
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertMeasEventEV<T>(
            T _entity,
            WTCMSLive.BusinessEntity.MeasEvent_EigenValue _ev) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("AcquisitionTime").SetValue(_entity, _ev.AcquisitionTime, null);
            type.GetProperty("AlarmDegree").SetValue(_entity, Convert.ToInt16(_ev.AlarmDegree), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_ev.Data_Qual_Type, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_ev.MeasDefinitionID), null);
            type.GetProperty("WkConDataNum").SetValue(_entity, Convert.ToInt16(_ev.WkConDataNum), null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _ev.WindTurbineID, null);
            type.GetProperty("WkConLevelCode").SetValue(_entity, Convert.ToInt16(_ev.OutPowerBandCode), null);
            type.GetProperty("EigenValueNum").SetValue(_entity,Convert.ToInt16(_ev.EigenValueNum), null);
            type.GetProperty("EigenValueNumSVM").SetValue(_entity, Convert.ToInt16(_ev.SVMEigenValueNum), null);

            return _entity;
        }

        #endregion 测量事件，特征值


        #region 工况数据转换
        /// <summary>
        /// 转换转速单值，实时
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedValueRT ConvertRotSpeedValueRT(WTCMSLive.BusinessEntity.RotSpeedValue _data)
        {
            return ConvertRotSpeedValue<RotSpeedValueRT>(new RotSpeedValueRT(), _data);
        }
        /// <summary>
        /// 转速单值，小时
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedValueHour ConvertRotSpeedValueHour(WTCMSLive.BusinessEntity.RotSpeedValue _data)
        {
            return ConvertRotSpeedValue<RotSpeedValueHour>(new RotSpeedValueHour(), _data);
        }
        /// <summary>
        /// 转速单值，天
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedValueDay ConvertRotSpeedValueDay(WTCMSLive.BusinessEntity.RotSpeedValue _data)
        {
            return ConvertRotSpeedValue<RotSpeedValueDay>(new RotSpeedValueDay(), _data);
        }

        /// <summary>
        /// 转换转速单值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertRotSpeedValue<T>(
    T _entity,
    WTCMSLive.BusinessEntity.RotSpeedValue _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }
            Type type = typeof(T);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("MaximumValue").SetValue(_entity,Convert.ToDecimal(_data.MaxRotSpeed), null);
            type.GetProperty("MinimumValue").SetValue(_entity, Convert.ToDecimal(_data.MinRotSpeed), null);
            type.GetProperty("AvgValue").SetValue(_entity, Convert.ToDecimal(_data.AveRotSpeed), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);

            return _entity;
        }


        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据, 实时
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WorkConditionEVDataRT ConvertWorkConDataRT(WTCMSLive.BusinessEntity.WorkingConditionData _data)
        {
            return ConvertWorkConData<WorkConditionEVDataRT>(new WorkConditionEVDataRT(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据, 小时
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WorkConditionEVDataHour ConvertWorkConDataHour(WTCMSLive.BusinessEntity.WorkingConditionData _data)
        {
            return ConvertWorkConData<WorkConditionEVDataHour>(new WorkConditionEVDataHour(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据, 天
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WorkConditionEVDataDay ConvertWorkConDataDay(WTCMSLive.BusinessEntity.WorkingConditionData _data)
        {
            return ConvertWorkConData<WorkConditionEVDataDay>(new WorkConditionEVDataDay(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据, 趋势
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WorkConditionTrend ConvertWorkConDataTrend(WTCMSLive.BusinessEntity.WorkingConditionData _data)
        {
            return ConvertWorkConData<WorkConditionTrend>(new WorkConditionTrend(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertWorkConData<T>(
            T _entity,
            WTCMSLive.BusinessEntity.WorkingConditionData _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("MaximumValue").SetValue(_entity, Convert.ToDecimal(_data.MaxValue), null);
            type.GetProperty("MinimumValue").SetValue(_entity, Convert.ToDecimal(_data.MinValue), null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("ParamTypeCode").SetValue(_entity, Convert.ToInt16(_data.Param_Type_Code), null);
            type.GetProperty("AverageValue").SetValue(_entity, Convert.ToDecimal(_data.Param_Value), null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);

            return _entity;
        }

        #endregion 工况数据转换

        #region 转速波形数据转换

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换, RT
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveDataRT ConvertRotSpdWFRT(WTCMSLive.BusinessEntity.RotSpeedWaveData _data)
        {
            return ConvertRotSpdWF(new RotSpeedWaveDataRT(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换, Hour
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveDataHour ConvertRotSpdWFHour(WTCMSLive.BusinessEntity.RotSpeedWaveData _data)
        {
            return ConvertRotSpdWF(new RotSpeedWaveDataHour(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换, Day
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveDataDay ConvertRotSpdWFDay(WTCMSLive.BusinessEntity.RotSpeedWaveData _data)
        {
            return ConvertRotSpdWF(new RotSpeedWaveDataDay(), _data);
        }
        
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换, Day
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveDataHi ConvertRotSpdWFHis(WTCMSLive.BusinessEntity.RotSpeedWaveData _data)
        {
            return ConvertRotSpdWF(new RotSpeedWaveDataHi(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换, Alarm
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveDataAlarm ConvertRotSpdWFAlarm(WTCMSLive.BusinessEntity.RotSpeedWaveData _data)
        {
            return ConvertRotSpdWF(new RotSpeedWaveDataAlarm(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertRotSpdWF<T>(
            T _entity,
            WTCMSLive.BusinessEntity.RotSpeedWaveData _data) where T : new()
        {
             //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("WaveLength").SetValue(_entity, _data.WaveLength, null);
            type.GetProperty("WaveDataPath").SetValue(_entity, Encoding.Default.GetBytes(_data.waveDataPath), null);
            type.GetProperty("GearRatio").SetValue(_entity, (decimal)_data.GearRatio, null);
            type.GetProperty("LineCounts").SetValue(_entity,_data.LineCounts,null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }
            return _entity;
            
        }

        public static T ConvertRotSpdRTWF<T>(
            T _entity,
            RotSpeedWaveDataRT _data) where T : new()
        {
            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.DataQualType, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("WaveDataPath").SetValue(_entity, _data.WaveDataPath, null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("WaveLength").SetValue(_entity, _data.WaveLength, null);
            type.GetProperty("LineCounts").SetValue(_entity, _data.LineCounts, null);
            type.GetProperty("GearRatio").SetValue(_entity, (decimal)_data.GearRatio, null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }
            return _entity;

        }
        #endregion 转速波形数据转换

        #region 振动波形数据转换

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据, RT
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WFDataRT ConvertWFDataRT(WTCMSLive.BusinessEntity.WaveFormData _data)
        {
            return ConvertWFData(new WFDataRT(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据, Hour
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WFDataHour ConvertWFDataHour(WTCMSLive.BusinessEntity.WaveFormData _data)
        {
            return ConvertWFData(new WFDataHour(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据, Day
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WFDataDay ConvertWFDataDay(WTCMSLive.BusinessEntity.WaveFormData _data)
        {
            return ConvertWFData(new WFDataDay(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据, His
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WFDataHi ConvertWFDataHis(WTCMSLive.BusinessEntity.WaveFormData _data)
        {
            return ConvertWFData(new WFDataHi(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据, Alarm
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WFDataAlarm ConvertWFDataAlarm(WTCMSLive.BusinessEntity.WaveFormData _data)
        {
            return ConvertWFData(new WFDataAlarm(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static T ConvertWFDataRT<T>(
            T _entity,
            WTCMSLive.Entity.Models.WFDataRT _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, _data.MeasDefinitionID, null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("WaveDefinitionID").SetValue(_entity, _data.WaveDefinitionID, null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("DAUChannelID").SetValue(_entity, Convert.ToInt32(_data.DAUChannelID), null);
            type.GetProperty("SampleRate").SetValue(_entity, _data.SampleRate, null);
            type.GetProperty("WindParkID").SetValue(_entity, _data.WindParkID, null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("ComponentName").SetValue(_entity, _data.ComponentName, null);
            type.GetProperty("LocationOrientation").SetValue(_entity, _data.LocationOrientation, null);
            type.GetProperty("LocationSection").SetValue(_entity, _data.LocationSection, null);
            type.GetProperty("SignalType").SetValue(_entity, _data.SignalType, null);
            type.GetProperty("WaveType").SetValue(_entity, Convert.ToInt16(_data.WaveType), null);
            type.GetProperty("WaveDefParam").SetValue(_entity, _data.WaveDefParam, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.DataQualType, null);
            type.GetProperty("ConvertCoefficient").SetValue(_entity, Convert.ToDecimal(_data.ConvertCoefficient), null);
            type.GetProperty("WaveLength").SetValue(_entity, Convert.ToInt32(_data.WaveLength), null);
            type.GetProperty("WaveDataPath").SetValue(_entity, _data.WaveDataPath, null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }

           return _entity;
        }

        /// <summary>
        /// 转换晃度波形数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static T ConvertSVMWFDataRT<T>(
            T _entity,
            WTCMSLive.Entity.Models.SVMWFDataRT _data) where T : new()
        {
            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, _data.MeasDefinitionID, null);
            type.GetProperty("WaveDefinitionID").SetValue(_entity, _data.WaveDefinitionID, null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("ParamType").SetValue(_entity, _data.ParamType, null);
            type.GetProperty("SVMRegister").SetValue(_entity, _data.SVMRegister, null);
            type.GetProperty("WindParkID").SetValue(_entity, _data.WindParkID, null);
            type.GetProperty("SampleRate").SetValue(_entity, _data.SampleRate, null);
            type.GetProperty("WaveLength").SetValue(_entity, Convert.ToInt32(_data.WaveLength), null);
            type.GetProperty("ConvertCoefficient").SetValue(_entity, _data.ConvertCoefficient, null);
            type.GetProperty("WaveDataPath").SetValue(_entity, _data.WaveDataPath, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.DataQualType, null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }
            return _entity;
        }
        /// <summary>
        /// 转换振动波形数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertWFData<T>(
    T _entity,
    WTCMSLive.BusinessEntity.WaveFormData _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("ComponentName").SetValue(_entity, _data.ComponentName, null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("DAUChannelID").SetValue(_entity,Convert.ToInt32(_data.DAUChannelID), null);
            type.GetProperty("LocationOrientation").SetValue(_entity, _data.LocationOrientation, null);
            type.GetProperty("LocationSection").SetValue(_entity, _data.LocationSection, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("SampleRate").SetValue(_entity,Convert.ToDecimal(_data.SampleRate), null);
            type.GetProperty("SignalType").SetValue(_entity, _data.SignalType, null);
            type.GetProperty("WaveDefinitionID").SetValue(_entity, Convert.ToInt32(_data.WaveDefinitionID), null);
            type.GetProperty("WaveDataPath").SetValue(_entity, Encoding.Default.GetBytes(_data.WaveDataPath), null);
            type.GetProperty("ConvertCoefficient").SetValue(_entity, Convert.ToDecimal(_data.ConvertCoefficient), null);
            type.GetProperty("WaveLength").SetValue(_entity, Convert.ToInt32(_data.WaveLength), null);
            type.GetProperty("WaveType").SetValue(_entity, (short)_data.MeasDefType, null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);             
            type.GetProperty("WaveDefParam").SetValue(_entity,WaveDefParamConvert.EncodWaveParam(_data), null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("WindParkID").SetValue(_entity, _data.WindParkID, null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }
            return _entity;
        }

        #endregion 振动波形数据转换

        #region 特征值
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据, RT
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static EVDataRT ConvertEVDataRT(WTCMSLive.BusinessEntity.EigenValueData _data)
        {
            return ConvertEVData<EVDataRT>(new EVDataRT(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据, Trend
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static EVDataTrend ConvertEVDataTrend(WTCMSLive.BusinessEntity.EigenValueData _data)
        {
            return ConvertEVData<EVDataTrend>(new EVDataTrend(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertEVData<T>(
            T _entity,
            WTCMSLive.BusinessEntity.EigenValueData _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("AlarmDegree").SetValue(_entity, Convert.ToInt16(_data.AlarmDegree), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("EigenValue").SetValue(_entity, (decimal)_data.Eigen_Value, null);
            type.GetProperty("EigenValueID").SetValue(_entity, _data.EigenValueID, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("WaveDefinitionID").SetValue(_entity, Convert.ToInt32(_data.WaveDefinitionID), null);
            type.GetProperty("WkConLevelCode").SetValue(_entity, Convert.ToInt16(_data.OutPowerBandCode), null);
            type.GetProperty("EigenValueCode").SetValue(_entity, _data.EigenValueCode, null);
            type.GetProperty("SamplingTime").SetValue(_entity, _data.SamplingTime, null);

            return _entity;
        }
        #endregion 特征值

        #region 特征值，SVM

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// SVM特征值数据, RT
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static SVMEVDataRT ConvertSVMEVDataRT(WTCMSLive.BusinessEntity.SVM.SVMEigenValueData _data)
        {
            return ConvertSVMEVData<SVMEVDataRT>(new SVMEVDataRT(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// SVM特征值数据, Trend
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static SVMEVDataTrend ConvertSVMEVDataTrend(WTCMSLive.BusinessEntity.SVM.SVMEigenValueData _data)
        {
            return ConvertSVMEVData<SVMEVDataTrend>(new SVMEVDataTrend(), _data);
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private static T ConvertSVMEVData<T>(
            T _entity,
            WTCMSLive.BusinessEntity.SVM.SVMEigenValueData _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);

            type.GetProperty("WaveDefinitionID").SetValue(_entity,Convert.ToInt32(_data.WaveDefinitionID), null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("AlarmDegree").SetValue(_entity, Convert.ToInt16(_data.AlarmDegree), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("EigenValue").SetValue(_entity,Convert.ToDecimal(_data.EigenValue), null);
            type.GetProperty("EigenValueID").SetValue(_entity,_data.EigenValueID, null);
            type.GetProperty("EigenValueType").SetValue(_entity, (short)_data.EigenValueType, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, Convert.ToInt32(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity, _data.MeasLocationID, null);
            type.GetProperty("WindTurbineID").SetValue(_entity, _data.WindTurbineID, null);
            type.GetProperty("WkConLevelCode").SetValue(_entity, Convert.ToInt16(_data.LevelCode), null);
            type.GetProperty("SVMRegister").SetValue(_entity, Convert.ToInt16(_data.SVMRegister), null);

            return _entity;
        }

        #endregion 特征值，SVM


        #region 晃度波形数据转换
        public static SVMWFDataRT ConvertSVMWFDataRT(WTCMSLive.BusinessEntity.SVM.SVMWaveFormData _data)
        {
            return ConvertSVMWFData(new SVMWFDataRT(), _data);
        }

        public static SVMWFDataHi ConvertSVMWFDataHIS(WTCMSLive.BusinessEntity.SVM.SVMWaveFormData _data)
        {
            return ConvertSVMWFData(new SVMWFDataHi(), _data);
        }

        private static T ConvertSVMWFData<T>(
    T _entity,
    WTCMSLive.BusinessEntity.SVM.SVMWaveFormData _data) where T : new()
        {

            //初始化 如果为null
            if (_entity == null)
            {
                _entity = new T();
            }

            Type type = typeof(T);
            type.GetProperty("WindTurbineID").SetValue(_entity,_data.WindTurbineID, null);
            type.GetProperty("MeasDefinitionID").SetValue(_entity, int.Parse(_data.MeasDefinitionID), null);
            type.GetProperty("MeasLocationID").SetValue(_entity,_data.MeasLocationID, null);
            type.GetProperty("WaveDefinitionID").SetValue(_entity, int.Parse(_data.WaveDefinitionID), null);
            type.GetProperty("AcquisitionTime").SetValue(_entity, _data.AcquisitionTime, null);
            type.GetProperty("ParamType").SetValue(_entity,(short)_data.ParamType, null);
            type.GetProperty("SVMRegister").SetValue(_entity, _data.SVMRegister, null);
            type.GetProperty("WindParkID").SetValue(_entity, _data.WindParkID, null);
            type.GetProperty("SampleRate").SetValue(_entity,Convert.ToDecimal(_data.SampleRate), null);
            type.GetProperty("DataQualType").SetValue(_entity, (short)_data.Data_Qual_Type, null);
            type.GetProperty("ConvertCoefficient").SetValue(_entity, Convert.ToDecimal(_data.ConvertCoefficient), null);
            type.GetProperty("WaveLength").SetValue(_entity, Convert.ToInt32(_data.WaveLength), null);
            type.GetProperty("WaveDataPath").SetValue(_entity, Encoding.Default.GetBytes(_data.WavePath), null);
            if (_data.WaveData != null)
            {
                type.GetField("WaveData").SetValue(_entity, _data.WaveData);
            }
            return _entity;
        }
        #endregion

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 直流分量数据转换
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SensorDCData ConvertSensorDCData(
            WTCMSLive.BusinessEntity.SensorDCData _data)
        {
            WTCMSLive.Entity.Models.SensorDCData data = new Entity.Models.SensorDCData();

            data.ChannelNumber = _data.ChannelNumber;
            data.DCAcquisitionTime = _data.DCAcquisitionTime;
            data.DCDataValue = (decimal)_data.DCDataValue;
            data.WindTurbineID = _data.Ds_asset_id;

            return data;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 缓存数据更新记录表
        /// </summary>
        /// <param name="_record"></param>
        /// <returns></returns>
        public static BufferDataUpdateRecordHi ConvertBufferDataUpDateRecord(
            WTCMSLive.BusinessEntity.RTWaveFormDataTableUpdateRecord _record)
        {
            BufferDataUpdateRecordHi record = new BufferDataUpdateRecordHi();
            record.WindTurbineID =_record.WindTurbineID;
            record.AlarmState = _record.AlamState;
            record.MeasDefinitionID = Convert.ToInt32(_record.MeasDefinitionID);
            record.WindTurbineID = _record.WindTurbineID;
            record.WkConLevelCode = (short)_record.WorkCondDegree;
            if (_record.UpdateTimeAlarm != DateTime.MinValue)
            {
                record.UpdateTimeAlarm = _record.UpdateTimeAlarm;
            }
            if (_record.UpdateTimeDay != DateTime.MinValue)
            {
                record.UpdateTimeDay = _record.UpdateTimeDay;
            }
            if (_record.UpdateTimeEig != DateTime.MinValue)
            {
                record.UpdateTimeEigenValue = _record.UpdateTimeEig;
            }
            if (_record.UpdateTimeHis != DateTime.MinValue)
            {
                record.UpdateTimeHis = _record.UpdateTimeHis;
            }
            if (_record.UpdateTimeHour != DateTime.MinValue)
            {
                record.UpdateTimeHour = _record.UpdateTimeHour;
            }

            return record;
        }

        /// <summary>
        /// 波形数据挑选记录转换
        /// </summary>
        /// <param name="_record"></param>
        /// <returns></returns>
        public static WaveDataSelectedRecord ConvertWaveDataSelectRecord(
            WTCMSLive.BusinessEntity.WaveDataSelectedRecord _record)
        {
            WaveDataSelectedRecord rec = new WaveDataSelectedRecord();
            rec.WindTurbineID =_record.WindTurbineID;
            rec.MeasDefinitionID = int.Parse(_record.MeasDefinitionID);
            rec.RangeID = int.Parse(_record.RangeID);
            rec.SelectTime = _record.SelectTime;
            return rec;
        }

        /// <summary>
        /// 转换波形数据传输任务
        /// </summary>
        /// <param name="_assignment"></param>
        /// <returns></returns>
        public static SynchroDataTask ConvertSynchroDataTask(
            WTCMSLive.BusinessEntity.SynchroDataTask _assignment)
        {
            SynchroDataTask task = new SynchroDataTask();
            task.WindTurbineID = _assignment.WindTurbineID;
            task.ServerAddress = _assignment.ServerAddress;
            task.MeasDefinitionID = int.Parse(_assignment.MeasDefinitionID);
            task.AcquisitionTime = _assignment.AcquisitionTime;
            task.SynchroDataFilePath = _assignment.SynchroDataFilePath;
            task.PriorityLevel = (short)_assignment.PriorityLevel;
            task.NetType = (short)_assignment.NetType;
            return task;
        }

    }
}
