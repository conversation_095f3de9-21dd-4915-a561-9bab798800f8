﻿using System.Net.Sockets;
using CMSFramework.DAUFacadeBase;
using WindCMS.GWServer.Entity;
using WindCMS.GWServer.ListenerServer;

namespace WindCMS.GWServer.Agent;

/// <summary>
/// Tcp 监听实体
/// </summary>
public class TcpListenerAgent
{
    /// <summary>
    /// 连接就绪（需要等待获取上报数据后方认为就绪）
    /// </summary>
    public bool ConnectReady { get; set; }  = false;
    
    /// <summary>
    /// 是否网关设备
    /// </summary>
    public bool IsGatewayDevice { get; set; }

    /// <summary>
    /// 是否存在交互
    /// </summary>
    public bool HasInteraction { get; set; } = false;

    /// <summary>
    /// 执行结果
    /// </summary>
    public bool ResultExecution { get; set; } = true;

    /// <summary>
    /// 最后通讯时间
    /// </summary>
    public DateTime LastCommunicationTime = DateTime.Now;
    
    /// <summary>
    /// 客户端
    /// </summary>
    public TcpClient TcpClient { get; init; } = null!;
    
    /// <summary>
    /// DAU 上下文信息
    /// </summary>
    public DauWorkContext DauWorkContext { get; set; } = null!;
    
    /// <summary>
    /// DAU 扩展信息
    /// </summary>
    public DauConfigExtension DauConfigExtension { get; } = new();

    /// <summary>
    /// 上下文 Key 值
    /// </summary>
    public string TcpListenerContextKey { get; set; } = null!;

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationTokenSource CancellationTokenSource { get; init; } = null!;

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken? CancellationToken => CancellationTokenSource?.Token ?? null;


    /// <summary>
    /// 读取数据线程
    /// </summary>
    public Task ReadDataTask { get; set; } = null!;

    /// <summary>
    /// 写数据处理
    /// </summary>
    public IListenerWriteHandler ListenerWriteHandler { get; set; } = null!;

    /// <summary>
    /// 读数据处理
    /// </summary>
    public ListenerReadHandler ListenerReadHandler { get; set; } = null!;

    /// <summary>
    /// 读取流数据
    /// </summary>
    public byte[] ReadDataBuffer { get; set; } = new byte[7196];
}