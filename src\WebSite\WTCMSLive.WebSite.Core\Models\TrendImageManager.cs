﻿using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using CMSFramework.DataStorageLogic;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 趋势图，相关性分析图
    /// </summary>
    public class TrendImageManager
    {
       // private CMSFramework.EigenValue.Storage.EVDataProvider evDataProvider = new CMSFramework.EigenValue.Storage.EVDataProvider();

        private CMSFramework.EigenValue.Storage.EVDataProvider evProvider = new CMSFramework.EigenValue.Storage.EVDataProvider(ConfigInfo.DBConnNameTrend, ConfigInfo.DBConnName);

        private IReadMeasDataLogic readProvider = StorageFactoryLogic.GetReadDataLogic();

        /// <summary>
        /// 获取指定机组测量位置下特定时间和工况条件下的特征值数据
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="measLocId">测量位置ID</param>
        /// <param name="ParamType">工况参数</param>
        /// <param name="MinAverageValue">工况最小值</param>
        /// <param name="MaxAverageValue">工况最大值</param>
        /// <returns></returns>
        public List<EigenValueData_Vib> GetEigenDataByParame(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, EnumWorkCondition_ParamType ParamType, string MinAverageValue, string MaxAverageValue,int measType = 0)
        {
            List<EigenValueData_Vib> List = new List<EigenValueData_Vib>();
            List<WorkingConditionData> WorkList = null;
            //List<EigenValueData_Vib> eigenValueList = TrendEVDataManage.GetVibEigenValueTrendByMeasLocId(turbineID, measLocId, beginTime, endTime);
            //获取趋势特征值，按分表方式获取
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            List<EigenValueData_Vib> eigenValueList = readLogic.GetEigenValueData(turbineID, measLocId, beginTime, endTime);
            if (ParamType == EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION)
            {
                //无工况
                List = eigenValueList;
            }
            else
            {
                WorkList = readLogic.GetWorkCondData(turbineID, beginTime, endTime);
                // WorkList = readProvider.GetWorkCondData(turbineID, beginTime, endTime);
                WorkList = WorkList.FindAll(item => item.Param_Type_Code == ParamType && item.Param_Value >= double.Parse(MinAverageValue) && item.Param_Value <= double.Parse(MaxAverageValue));
                foreach (WorkingConditionData data in WorkList)
                {
                    var list = eigenValueList.Where(i => i.MeasDefinitionID == data.MeasDefinitionID && i.MeasLocationID == measLocId && i.AcquisitionTime == data.AcquisitionTime);
                    if (list != null)
                    {
                        list.ToList().ForEach(item =>
                        {
                            List.Add(new EigenValueData_Vib()
                            {
                                WindTurbineID = turbineID,
                                MeasLocationID = measLocId,
                                EigenValueID = item.EigenValueID,
                                EigenValueCode = item.EigenValueCode
                            });
                        });
                    }
                }
            }
            //List.ForEach(item =>
            //{
            //    item.EigenValueCode = item.EigenValueID.Replace(measLocId + "&&", "");
            //});
            List = List.GroupBy(a => a.EigenValueCode).Select(g => g.First()).ToList();
            if(measType == 1)
            {
                return List;
            }
            return WTCMSLive.BusinessModel.EigenValueManage.SortCMSEigenValue(List);
        }


        public List<EigenValueData_SVM> GetSVMEigenDataByParame(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, EnumWorkCondition_ParamType ParamType, string MinAverageValue, string MaxAverageValue)
        {
            List<EigenValueData_SVM> List = new List<EigenValueData_SVM>();
            List<WorkingConditionData> WorkList = null;
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();

            List<EigenValueData_SVM> eigenValueList = readLogic.GetEigenValueData_SVM(turbineID, beginTime, endTime);
            eigenValueList = eigenValueList.Where(i => i.MeasLocationID == measLocId).ToList();
            if (ParamType == EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION)
            {
                //无工况
                //WorkList = TrendEVDataManage.GetWorkingConditionByIDAndEigenIdNoWorkCondition(turbineID, beginTime, endTime);
                eigenValueList.ForEach(item =>
                {
                    List.Add(new EigenValueData_SVM()
                    {
                        WindTurbineID = turbineID,
                        MeasLocationID = measLocId,
                        EigenValueID = item.EigenValueID,
                        Eigen_Value = item.Eigen_Value,
                    });
                });
            }
            else
            {
                WorkList = readLogic.GetWorkCondData(turbineID, beginTime, endTime);
                WorkList = WorkList.FindAll(item => item.Param_Type_Code == ParamType
                    && item.Param_Value >= double.Parse(MinAverageValue) && item.Param_Value <= double.Parse(MaxAverageValue));
                foreach (WorkingConditionData data in WorkList)
                {
                    var list = eigenValueList.Where(i => i.MeasDefinitionID == data.MeasDefinitionID && i.MeasLocationID == measLocId && i.AcquisitionTime == data.AcquisitionTime);
                    if (list != null)
                    {
                        list.ToList().ForEach(item =>
                        {
                            List.Add(new EigenValueData_SVM()
                            {
                                WindTurbineID = turbineID,
                                MeasLocationID = measLocId,
                                EigenValueID=item.EigenValueID
                            });
                        });
                    }
                }
            }
            //SVM中，把特征值ID转存储成CODE
            List.ForEach(item =>
            {
                item.EigenValueID = item.EigenValueID.Replace(measLocId + "&&", "");
            });
            List = List.GroupBy(a => a.EigenValueID).Select(g => g.First()).ToList();
            return List;
            //return WTCMSLive.BusinessModel.EigenValueManage.SortEigenValue(List); 
        }


        public List<AnalysisData> GetEVDataTrendByTime(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, string measType, string EigenValueCode, EnumWorkCondition_ParamType ParamType, string MinAverageValue, string MaxAverageValue)
        {
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            List<AlarmDefinition> turbineAlarmList = new List<AlarmDefinition>();
            //测量位置
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            List<WorkingConditionData> WorkList = readLogic.GetWorkCondData(turbineID, beginTime, endTime);
            if (ParamType != EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION)
            {
                WorkList = WorkList.FindAll(item => item.Param_Type_Code == ParamType
                    && item.Param_Value >= double.Parse(MinAverageValue) && item.Param_Value <= double.Parse(MaxAverageValue));
            }
            var workMeasList = WorkList.GroupBy(a => new { a.MeasDefinitionID, a.AcquisitionTime }).Select(g => g.First()).ToList();
           
            List<EigenValueData_Vib> EigenVibList = readLogic.GetEigenValueData(turbineID, measLocId, beginTime, endTime);
            EigenVibList = EigenVibList.FindAll(item => item.EigenValueID == EigenValueCode);
            foreach (var data in workMeasList)
            {
                //结束时间需要获取在当前功率下的运行时间，替代结束时间，不然会把其他功率下的数据显示出来
                DateTime endTimeTemp = WorkList.Where(item => item.MeasDefinitionID == data.MeasDefinitionID && item.AcquisitionTime == data.AcquisitionTime).OrderByDescending(item => item.AcquisitionTime).First().AcquisitionTime;
                DateTime beginTimeTemp = WorkList.Where(item => item.MeasDefinitionID == data.MeasDefinitionID && item.AcquisitionTime == data.AcquisitionTime).OrderBy(item => item.AcquisitionTime).First().AcquisitionTime;
                endTimeTemp = endTimeTemp < endTime ? endTimeTemp : endTime;
                beginTimeTemp = beginTimeTemp > beginTime ? beginTimeTemp : beginTime;
                EigenVibList.FindAll(item=>item.MeasDefinitionID==data.MeasDefinitionID && item.AcquisitionTime>= beginTimeTemp && item.AcquisitionTime <= endTimeTemp).ForEach(i =>
                {
                    eigenValueList.Add(i);
                });
            }
            if (eigenValueList.Count == 0)
                return new List<AnalysisData>();
            //特征值分类字典
            Dictionary<string, List<EigenValueData>> eigenValueDic = new Dictionary<string, List<EigenValueData>>();
            //特征值索引
            List<string> dicIndex = new List<string>();
            List<string> dicNameIndex = new List<string>();
            //测量位置
            //特征值
            //数据分类,根据特征值ID，对取的所有数据进行分组
            //数据排序
            eigenValueList = eigenValueList.OrderBy(item => item.AcquisitionTime).ToList();
            for (int i = 0; i < eigenValueList.Count; i++)
            {
                if (eigenValueDic.ContainsKey(eigenValueList[i].EigenValueID))
                {
                    eigenValueDic[eigenValueList[i].EigenValueID].Add(eigenValueList[i]);
                }
                else
                {
                    List<EigenValueData> list = new List<EigenValueData>();
                    dicIndex.Add(eigenValueList[i].EigenValueID);//特征值ID索引
                    // 单位
                    string eigenUnit = "";
                    string TempEigenValueId = eigenValueList[i].EigenValueID;
                    if (TempEigenValueId.IndexOf("VRMS") > -1)
                    {
                        eigenUnit = " mm/s";
                    }
                    else if (TempEigenValueId.IndexOf("RMS") > -1 || TempEigenValueId.IndexOf("PK") > -1 || TempEigenValueId.IndexOf("PPK") > -1)
                    {
                        eigenUnit = " m/s^2";
                    }
                    // 过程量特征值暂不显示单位，单位无法确定
                    if (measType == "2")
                    {
                        eigenUnit = "";
                    }
                    //获取振动下特征值名称
                    dicNameIndex.Add(WTCMSLive.BusinessModel.EigenValueManage.GetFreBandByCode(eigenValueList[i].EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1]) + eigenUnit);
                    list.Add(eigenValueList[i]);
                    eigenValueDic.Add(eigenValueList[i].EigenValueID, list);//特征值ID和值
                }
            }
            List<AnalysisData> analysisChartList = new List<AnalysisData>();
            for (int j = 0; j < dicIndex.Count; j++)
            {
                AnalysisData analysisData = new AnalysisData();
                //取得第一个数据
                EigenValueData eigenValueData = eigenValueDic[dicIndex[j]][0];
                analysisData = CreateAnalysisChartByEigenValue(turbineID, dicNameIndex[j], dicNameIndex[j], eigenValueDic[dicIndex[j]]);
                analysisChartList.Add(analysisData);
            }
            return analysisChartList;
        }

        public List<AnalysisData> GetEVDataTrendByTime(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, string measType, string EigenValueId)
        {
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            List<AlarmDefinition> turbineAlarmList = new List<AlarmDefinition>();

            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            eigenValueList = readLogic.GetEigenValueData(turbineID, measLocId, beginTime, endTime);

            eigenValueList = eigenValueList.FindAll(item=>item.EigenValueID== EigenValueId);
            if (eigenValueList.Count == 0)
                return new List<AnalysisData>();
            // eigenValueList = TrendEVDataManage.GetEigenValueTrendByID(Convert.ToInt32(turbineID), beginTime, endTime);
            //特征值分类字典
            Dictionary<string, List<EigenValueData>> eigenValueDic = new Dictionary<string, List<EigenValueData>>();
            //特征值索引
            List<string> dicIndex = new List<string>();
            List<string> dicNameIndex = new List<string>();
            //测量位置
            //特征值
            //数据排序
            eigenValueList = eigenValueList.OrderBy(item => item.AcquisitionTime).ToList();

            for (int i = 0; i < eigenValueList.Count; i++)
            {
                if (eigenValueDic.ContainsKey(eigenValueList[i].EigenValueID))
                {
                    eigenValueDic[eigenValueList[i].EigenValueID].Add(eigenValueList[i]);
                }
                else
                {
                    List<EigenValueData> list = new List<EigenValueData>();
                    dicIndex.Add(eigenValueList[i].EigenValueID);//特征值ID索引
                    //获取振动下特征值名称
                    string eigenUnit = "";
                    string TempEigenValueId = eigenValueList[i].EigenValueID;

                    
                    if (TempEigenValueId.IndexOf("VRMS") > -1)
                    {
                        eigenUnit = " mm/s";
                    }
                    else if (TempEigenValueId.IndexOf("RMS") > -1 || TempEigenValueId.IndexOf("PK") > -1 || TempEigenValueId.IndexOf("PPK") > -1)
                    {
                        eigenUnit = " m/s^2";
                    }
                    // 过程量特征值暂不显示单位，单位无法确定
                    if (measType == "2")
                    {
                        eigenUnit = "";
                    }

                    dicNameIndex.Add(WTCMSLive.BusinessModel.EigenValueManage.GetFreBandByCode(eigenValueList[i].EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1]) + eigenUnit);
                    list.Add(eigenValueList[i]);
                    eigenValueDic.Add(eigenValueList[i].EigenValueID, list);//特征值ID和值
                }
            }
            //  WTCMSLive.ExceptionHandle.ExceptionHandler.LogDebugMessage("趋势分析：获取（end特征值分组） :" + DateTime.Now);
            List<AnalysisData> analysisChartList = new List<AnalysisData>();
            //  WTCMSLive.ExceptionHandle.ExceptionHandler.LogDebugMessage("趋势分析：获取（begin特征值分组数据） :" + DateTime.Now);
            for (int j = 0; j < dicIndex.Count; j++)
            {
                AnalysisData analysisData = new AnalysisData();
                //取得第一个数据
                EigenValueData eigenValueData = eigenValueDic[dicIndex[j]][0];
                analysisData = CreateAnalysisChartByEigenValue(turbineID, dicNameIndex[j], dicNameIndex[j], eigenValueDic[dicIndex[j]]);
                analysisChartList.Add(analysisData);
            }
            //  WTCMSLive.ExceptionHandle.ExceptionHandler.LogDebugMessage("趋势分析：获取（end特征值分组数据） :" + DateTime.Now);
            return analysisChartList;
        }

        private AnalysisData CreateAnalysisChartByEigenValue(string turbineID, string titleName, string subTitleName, List<EigenValueData> _EVDataList)
        {
            //获取VDI3834报警定义设置
            List<AlarmDefinition> turbineAlarmList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
            AnalysisData analysisChart = new AnalysisData();
            analysisChart.titleName = titleName;
            analysisChart.subText = subTitleName;
            List<double> eigenValueList = new List<double>();//有效值
            List<double> waringValueData = new List<double>();//警告 
            List<double> errorValueData = new List<double>();//危险 
            List<string> workConditionValueData = new List<string>();//工况
            List<string> timeValueData = new List<string>();//时间
            double error = 0;
            double waring = 0;
            for (int i = 0; i < _EVDataList.Count; i++)
            {
                if (_EVDataList[i] != null)
                {
                    eigenValueList.Add(Math.Round(_EVDataList[i].Eigen_Value, 5));
                    timeValueData.Add(_EVDataList[i].AcquisitionTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    //workConditionValueData.Add(_EVDataList[i].OutPowerBandCode);
                    if (error == 0 || waring == 0)
                    {
                        //获取VDI3834报警定义设置
                        AlarmDefinition AlarmDef = turbineAlarmList.Find(item => item.EigenValueID == _EVDataList[i].EigenValueID);
                        if (AlarmDef == null)
                            continue;
                        AlarmDefThreshold alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning);
                        if (alarmhold != null)
                        {
                            waring = (double)alarmhold.ThresholdValue;
                        }
                        alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm);
                        if (alarmhold != null)
                        {
                            error = (double)alarmhold.ThresholdValue;
                        }
                    }
                }
            }
            if (error > 0 || waring > 0)
            {
                for (int j = 0; j < eigenValueList.Count; j++)
                {
                    errorValueData.Add(error);
                    waringValueData.Add(waring);
                }
            }
            analysisChart.eigenValueData = eigenValueList.ToArray();
            analysisChart.workConditionValueData = workConditionValueData.ToArray();
            analysisChart.timeValueData = timeValueData.ToArray();
            analysisChart.waringValueData = waringValueData.ToArray();
            analysisChart.errorValueData = errorValueData.ToArray();
            return analysisChart;
        }
        /// <summary>
        /// 晃度仪趋势图
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public List<AnalysisData> GetSVMEVDataTrendByTime(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, string measType, string EigenValueId, EnumWorkCondition_ParamType ParamType, string MinAverageValue, string MaxAverageValue)
        {
            List<EigenValueData_SVM> eigenValueList = new List<EigenValueData_SVM>();
            List<AlarmDefinition> turbineAlarmListSVM = new List<AlarmDefinition>();
            List<MeasLoc_SVM> measLocList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
            //测量位置
            MeasLoc_SVM myMeasLoc = measLocList.Find(i => i.MeasLocationID == measLocId);
            //取得机组所有特征值
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            List<WorkingConditionData> WorkList = readLogic.GetWorkCondData(turbineID, beginTime, endTime);
            //List<WorkingConditionData> WorkList = readProvider.GetWorkCondData(turbineID, beginTime, endTime);;
            if (ParamType != EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION)
            {
                WorkList = WorkList.FindAll(item => item.Param_Type_Code == ParamType
                    && item.Param_Value >= double.Parse(MinAverageValue) && item.Param_Value <= double.Parse(MaxAverageValue));
            }
            var workMeasList = WorkList.GroupBy(a => new { a.MeasDefinitionID, a.AcquisitionTime }).Select(g => g.First()).ToList();

            var eigenList = readLogic.GetEigenValueData_SVM_ByEVID(turbineID, EigenValueId, beginTime, endTime);
            foreach (var data in workMeasList)
            {
                //结束时间需要获取在当前功率下的运行时间，替代结束时间，不然会把其他功率下的数据显示出来
                DateTime endTimeTemp = WorkList.Where(item => item.MeasDefinitionID == data.MeasDefinitionID && item.AcquisitionTime == data.AcquisitionTime).OrderByDescending(item => item.AcquisitionTime).First().AcquisitionTime;
                DateTime beginTimeTemp = WorkList.Where(item => item.MeasDefinitionID == data.MeasDefinitionID && item.AcquisitionTime == data.AcquisitionTime).OrderBy(item => item.AcquisitionTime).First().AcquisitionTime;
                endTimeTemp = endTimeTemp < endTime ? endTimeTemp : endTime;
                beginTimeTemp = beginTimeTemp > beginTime ? beginTimeTemp : beginTime;
                eigenList.FindAll(item => item.MeasDefinitionID == data.MeasDefinitionID && item.AcquisitionTime >= beginTimeTemp && item.AcquisitionTime <= endTimeTemp).ForEach(i =>
                {
                    eigenValueList.Add(i);
                });
            }
            if (eigenValueList.Count == 0)
                return new List<AnalysisData>();
            //特征值分类字典
            Dictionary<string, List<EigenValueData_SVM>> eigenValueDic = new Dictionary<string, List<EigenValueData_SVM>>();
            //特征值索引
            List<string> dicIndex = new List<string>();
            //特征值名称
            List<string> dicNameIndex = new List<string>();
            //特征值List
            //List<EigenValue_FreqBand> eigenValue_FreqBand = EigenValue_FreqBand.EigenValueFreqBandList();
            //数据排序
            eigenValueList = eigenValueList.OrderBy(item => item.AcquisitionTime).ToList();
            //数据分类,根据特征值ID，对取的所有数据进行分组
            for (int i = 0; i < eigenValueList.Count; i++)
            {
                if (myMeasLoc.MeasLocName.IndexOf("加速度") > -1)
                {
                    eigenValueList[i].Eigen_Value = eigenValueList[i].Eigen_Value * 1000;
                }
                if (eigenValueDic.ContainsKey(eigenValueList[i].EigenValueID))
                {
                    eigenValueDic[eigenValueList[i].EigenValueID].Add(eigenValueList[i]);
                }
                else
                {
                    List<EigenValueData_SVM> list = new List<EigenValueData_SVM>();
                    //string eigenValueName = EnumHelper.GetDescription(eigenValueList[i].EigenValueType);
                    string eigenValueName = eigenValueList[i].EigenValueID.Replace(eigenValueList[i].MeasLocationID, "").Replace("&&", "");
                    dicIndex.Add(eigenValueList[i].EigenValueID);//特征值ID索引

                    string eigenUnit = "";
                    if (myMeasLoc.MeasLocName.IndexOf("加速度") > -1)
                    {
                        if (eigenValueList[i].EigenValueID.EndsWith("NFPitch") || eigenValueList[i].EigenValueID.EndsWith("NFRoll"))
                        {
                            eigenUnit = " Hz";
                        }
                        else
                        {
                            eigenUnit = " mg";
                        }

                    }
                    else
                    {
                        if (eigenValueList[i].EigenValueID.Contains("TDBAVG") || eigenValueList[i].EigenValueID.Contains("TDBMAX"))
                        {
                            eigenUnit = " m";
                        }
                        else
                        {
                            eigenUnit = " °";
                        }

                        //eigenUnit = " °";
                    }

                    list.Add(eigenValueList[i]);
                    dicNameIndex.Add(eigenValueName + eigenUnit);
                    eigenValueDic.Add(eigenValueList[i].EigenValueID, list);//特征值ID和值
                }
            }
            List<AnalysisData> analysisChartList = new List<AnalysisData>();
            for (int j = 0; j < dicIndex.Count; j++)
            {
                AnalysisData analysisData = new AnalysisData();
                //取得第一个数据
                EigenValueData_SVM eigenValueData = eigenValueDic[dicIndex[j]][0];
                analysisData = CreateAnalysisChartBySVMEigenValue(turbineID, dicNameIndex[j], dicNameIndex[j], eigenValueDic[dicIndex[j]]);
                analysisChartList.Add(analysisData);
            }
            return analysisChartList;
        }

        //20181218 by SUNQI 查找晃度仪特征值趋势
        /// <summary>
        /// 查找晃度仪特征值趋势
        /// </summary>
        /// <param name="windTurbineID"></param>
        /// <param name="eigenValueID"></param>
        /// <param name="begin"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        private List<EigenValueData_SVM> GetEigenValueData_SVM_list(string windTurbineID, string eigenValueID, DateTime begin, DateTime end)
        {
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            return readLogic.GetEigenValueData_SVM_ByEVID(windTurbineID, eigenValueID, begin, end);
        }

        public List<AnalysisData> GetSVMEVDataTrendByTime(string turbineID, DateTime beginTime, DateTime endTime, string measLocId, string measType, string EigenValueId)
        {
            List<EigenValueData_SVM> eigenValueList = new List<EigenValueData_SVM>();
            List<AlarmDefinition> turbineAlarmListSVM = new List<AlarmDefinition>();
            List<MeasLoc_SVM> measLocList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
            //测量位置
            IReadMeasDataLogic readLogic = StorageFactoryLogic.GetReadDataLogic();
            MeasLoc_SVM myMeasLoc = measLocList.Find(i => i.MeasLocationID == measLocId);
            eigenValueList = readLogic.GetEigenValueData_SVM_ByEVID(turbineID, EigenValueId, beginTime, endTime);
            if (eigenValueList.Count == 0)
                return new List<AnalysisData>();
            //eigenValueList = TrendEVDataManage.GetSVMEigenValueTrendDataList(Convert.ToInt32(turbineID), beginTime, endTime);
            //特征值分类字典
            Dictionary<string, List<EigenValueData_SVM>> eigenValueDic = new Dictionary<string, List<EigenValueData_SVM>>();
            //特征值索引
            List<string> dicIndex = new List<string>();
            //特征值名称
            List<string> dicNameIndex = new List<string>();
            //特征值List
            //List<EigenValue_FreqBand> eigenValue_FreqBand = EigenValue_FreqBand.EigenValueFreqBandList();
            //数据排序
            eigenValueList = eigenValueList.OrderBy(item => item.AcquisitionTime).ToList();
            //数据分类,根据特征值ID，对取的所有数据进行分组
            for (int i = 0; i < eigenValueList.Count; i++)
            {
                //如果是加速度特征值，就给有效值扩大1000
                if (myMeasLoc.MeasLocName.IndexOf("加速度") > -1)
                {
                    if (!eigenValueList[i].EigenValueID.EndsWith("NFPitch") && !eigenValueList[i].EigenValueID.EndsWith("NFRoll"))
                    {
                        eigenValueList[i].Eigen_Value = eigenValueList[i].Eigen_Value * 1000;
                    }
                        
                }
                if (eigenValueDic.ContainsKey(eigenValueList[i].EigenValueID))
                {
                    eigenValueDic[eigenValueList[i].EigenValueID].Add(eigenValueList[i]);
                }
                else
                {
                    List<EigenValueData_SVM> list = new List<EigenValueData_SVM>();
                    //string eigenValueName = EnumHelper.GetDescription(eigenValueList[i].EigenValueType);
                    string eigenValueName = eigenValueList[i].EigenValueID.Replace(eigenValueList[i].MeasLocationID,"").Replace("&&","");
                    dicIndex.Add(eigenValueList[i].EigenValueID);//特征值ID索引
                    string eigenUnit = "";
                    if (myMeasLoc.MeasLocName.IndexOf("加速度") > -1)
                    {
                        if(eigenValueList[i].EigenValueID.EndsWith("NFPitch") || eigenValueList[i].EigenValueID.EndsWith("NFRoll"))
                        {
                            eigenUnit = " Hz";
                        }
                        else
                        {
                            eigenUnit = " mg";
                        }
                        
                    }
                    else
                    {
                        if (eigenValueList[i].EigenValueID.Contains("TDBAVG") || eigenValueList[i].EigenValueID.Contains("TDBMAX"))
                        {
                            eigenUnit = " m";
                        }
                        else
                        {
                            eigenUnit = " °";
                        }
                        //eigenUnit = " °";
                    }
                    dicNameIndex.Add(eigenValueName + eigenUnit);
                    list.Add(eigenValueList[i]);
                    eigenValueDic.Add(eigenValueList[i].EigenValueID, list);//特征值ID和值
                }
            }
            List<AnalysisData> analysisChartList = new List<AnalysisData>();
            for (int j = 0; j < dicIndex.Count; j++)
            {
                AnalysisData analysisData = new AnalysisData();
                //取得第一个数据
                EigenValueData_SVM eigenValueData = eigenValueDic[dicIndex[j]][0];
                analysisData = CreateAnalysisChartBySVMEigenValue(turbineID, dicNameIndex[j], dicNameIndex[j], eigenValueDic[dicIndex[j]]);
                analysisChartList.Add(analysisData);
            }
            return analysisChartList;
        }

        private AnalysisData CreateAnalysisChartBySVMEigenValue(string turbineID, string titleName, string subTitleName, List<EigenValueData_SVM> list)
        {
            //报警定义
            //晃度特征值目前报警定义获取为自定义，无VDI3834标准
            List<AlarmDefinition> turbineAlarmList = AlarmDefConfig.GetSVMAlarmListByTurID(turbineID);
            AnalysisData analysisChart = new AnalysisData();
            analysisChart.titleName = titleName;
            analysisChart.subText = subTitleName;

            List<double> eigenValueList = new List<double>();//有效值
            List<double> waringValueData = new List<double>();//警告 
            List<double> errorValueData = new List<double>();//危险 
            List<string> workConditionValueData = new List<string>();//工况
            List<string> timeValueData = new List<string>();//时间
            double error = 0;
            double waring = 0;
            for (int i = 0; i < list.Count; i++)
            {
                eigenValueList.Add(Math.Round(list[i].Eigen_Value, 5));
                timeValueData.Add(list[i].AcquisitionTime.ToString("yyyy-MM-dd HH:mm:ss"));
                //workConditionValueData.Add(list[i].LevelCode);
                if (error == 0 || waring == 0)
                {
                    //获取VDI3834报警定义设置
                    AlarmDefinition AlarmDef = turbineAlarmList.Find(item => item.EigenValueID == list[i].EigenValueID);
                    if (AlarmDef == null)
                        continue;
                    AlarmDefThreshold alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning);
                    if (alarmhold != null)
                    {
                        waring = (double)alarmhold.ThresholdValue;
                    }
                    alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm);
                    if (alarmhold != null)
                    {
                        error = (double)alarmhold.ThresholdValue;
                    }
                }
            }
            if (error > 0 || waring > 0)
            {
                //if (titleName.IndexOf("加速度") > -1)
                //{
                //    //如果是加速度特征值，需要*1000
                //    error = error * 1000;
                //    waring = waring * 1000;
                //}
                for (int j = 0; j < eigenValueList.Count; j++)
                {
                    errorValueData.Add(error);
                    waringValueData.Add(waring);
                }
            }
            analysisChart.eigenValueData = eigenValueList.ToArray();
            analysisChart.workConditionValueData = workConditionValueData.ToArray();
            analysisChart.timeValueData = timeValueData.ToArray();
            analysisChart.waringValueData = waringValueData.ToArray();
            analysisChart.errorValueData = errorValueData.ToArray();
            return analysisChart;
        }
    }
    /// <summary>
    /// 绘制趋势图数据类
    /// 表示一个图表
    /// </summary>
    public class AnalysisData
    {
        public string titleName { get; set; }
        public string subText { get; set; }
        //MeasDefinitionID 测量定义
        public string id { get; set; }
        public string EigenValue { get; set; }
        public double[] eigenValueData { get; set; }//
        public double[] waringValueData { get; set; }//
        public double[] errorValueData { get; set; }//
        public string[] workConditionValueData { get; set; }//
        public string[] timeValueData { get; set; }//
        public string[] lineColorList { get; set; }
        public List<AnalysisLine> LineList { get; set; }


        /// <summary>
        /// 用户配置报警线，
        /// </summary>
        public List<ThresholdLineData> ThresholdLineDatas { get; set; }
    }
    /// <summary>
    /// 趋势图中的一条线段
    /// </summary>
    public class AnalysisLine
    {
        public string lineName { get; set; }
        public string lineColor { get; set; }
        public int[] ValueData { get; set; }
        public int[] workConditionData { get; set; }
    }

    public class ThresholdLineData
    {
        /// <summary>
        /// 注意预警线
        /// </summary>
        public string WarningLine { get; set; }

        /// <summary>
        /// 危险报警线
        /// </summary>
        public string AlarmLine { get; set; }

        /// <summary>
        /// 报警线名称
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// 报警线类型
        /// </summary>
        public string LineType { get; set; }
    }
}