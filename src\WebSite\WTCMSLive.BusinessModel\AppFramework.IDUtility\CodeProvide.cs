﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppFramework.IDUtility
{
    /// <summary>
    /// 获取编号
    /// </summary>
    public static class CodeProvide
    {
        /// <summary>
        ///部件列表 
        /// </summary>
        /// <returns>(名称,编号)</returns>
        public static Dictionary<string, string> GetComponentDic()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("主轴承", "MBR");
            dic.Add("齿轮箱", "GBX");
            dic.Add("发电机", "GEN");
            dic.Add("塔筒", "TOW");
            dic.Add("机舱", "NAC");
            dic.Add("叶片一", "BLD1");
            dic.Add("叶片二", "BLD2");
            dic.Add("叶片三", "BLD3");
            dic.Add("轮毂", "HUB");
            dic.Add("变桨轴承一", "PBR1");
            dic.Add("变桨轴承二", "PBR2");
            dic.Add("变桨轴承三", "PBR3");

            // add by steel @2016-6-20 增加联轴器 couple
            dic.Add("联轴器", "CPL");

            return dic;
        }
        /// <summary>
        /// 截面字典
        /// </summary>
        /// <returns>名称,编号</returns>
        public static Dictionary<string, string> GetSectionDic(string component)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            switch (component)
            {
                case "主轴承":
                case "MBR":
                    dic.Add("无", "NUL");
                    dic.Add("前轴承", "1");
                    dic.Add("后轴承", "2");

                    // 2023 add
                    // 明阳桂山传动链项目
                    dic.Add("径向", "R");
                    dic.Add("轴向", "A");
                    break;
                case "齿轮箱":
                case "GBX":

                    dic.Add("无", "NUL");
                    dic.Add("主轴", "MS");
                    dic.Add("输入轴", "IN");
                    dic.Add("第一级内齿圈", "1PS");
                    dic.Add("第二级内齿圈", "2PS");
                    dic.Add("第二级行星", "2PS");
                    dic.Add("低速轴", "LSS");
                    dic.Add("中间轴", "IMS");
                    dic.Add("高速轴", "HSS");
                    dic.Add("高速轴电机侧", "HSD");
                    dic.Add("高速轴叶轮侧", "HSY");
                    break;
                case "发电机":
                case "GEN":
                    dic.Add("驱动端", "DE");
                    dic.Add("非驱动端", "NDE");
                    dic.Add("定子", "STR");
                    dic.Add("定子左侧", "STR1");
                    dic.Add("定子右侧", "STR2");
                    dic.Add("轴承(左)", "BEL");
                    dic.Add("轴承(右)", "BER");
                    dic.Add("轴承座径向", "BEHR"); // Bearing housing radial direction
                    dic.Add("转子", "ROT");
                    break;
                case "机舱":
                case "NAC":
                    dic.Add("无", "NUL");
                    dic.Add("横向", "R");
                    dic.Add("轴向", "A");
                    break;
                case "塔筒":
                case "TOW":
                    dic.Add("无", "NUL");
                    dic.Add("法兰一", "FLA1");
                    dic.Add("法兰二", "FLA2");
                    dic.Add("法兰三", "FLA3");
                    dic.Add("法兰四", "FLA4");
                    dic.Add("法兰五", "FLA5");

                    dic.Add("法兰六", "FLA6");
                    dic.Add("法兰七", "FLA7");
                    dic.Add("法兰八", "FLA8");
                    dic.Add("法兰九", "FLA9");

                    dic.Add("法兰十", "FLA10");
                    dic.Add("法兰十一", "FLA11");
                    dic.Add("法兰十二", "FLA12");

                    dic.Add("基础", "FDA");
                    dic.Add("塔顶", "TOP");
                    // 光纤应变
                    dic.Add("截面一", "SEC1");
                    dic.Add("截面二", "SEC2");
                    break;
                case "叶片一":
                case "BLD1":
                    dic.Add("无", "NUL");
                    //dic.Add("根部", "RS");// 使用叶根而不是根部
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");

                    dic.Add("内法兰", "FLAID");
                    dic.Add("外法兰", "FLAOD");

                    break;
                case "叶片二":
                case "BLD2":
                    dic.Add("无", "NUL");
                    //dic.Add("根部", "RS");
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");

                    dic.Add("内法兰", "FLAID");
                    dic.Add("外法兰", "FLAOD");
                    break;
                case "叶片三":
                case "BLD3":
                    dic.Add("无", "NUL");
                    //dic.Add("根部", "RS");
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");
                    dic.Add("内法兰", "FLAID");
                    dic.Add("外法兰", "FLAOD");
                    break;
                case "轮毂":
                case "HUB":
                    dic.Add("无", "NUL");
                    break;
                case "变桨轴承一":
                case "PBR1":
                    dic.Add("无", "NUL");
                    dic.Add("法兰螺栓", "FLA");
                    //dic.Add("根部", "RS");
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");
                    break;
                case "变桨轴承二":
                case "PBR2":
                    dic.Add("无", "NUL");
                    dic.Add("法兰螺栓", "FLA");
                    //dic.Add("根部", "RS");
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");
                    break;
                case "变桨轴承三":
                case "PBR3":
                    dic.Add("无", "NUL");
                    dic.Add("法兰螺栓", "FLA");
                    //dic.Add("根部", "RS");
                    dic.Add("叶根", "BR");
                    dic.Add("中部", "MP");
                    break;
                case "左电动轮":
                case "LEW":
                case "右电动轮":
                case "REW":
                    dic.Add("无", "NUL");
                    dic.Add("径向", "R");
                    dic.Add("轴向", "A");
                    break;

                default:
                    break;
            }
            return dic;
        }
        /// <summary>
        /// 方向列表
        /// </summary>
        /// <returns>名称,编码</returns>
        public static Dictionary<string, string> GetOrientationDic()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("水平", "H");
            dic.Add("垂直", "V");
            dic.Add("径向", "R");
            dic.Add("轴向", "A");
            //Added by zxk @2018-2-5 此处加入叶片方向
            dic.Add("切向", "QX");
            dic.Add("挥舞", "HW");
            dic.Add("摆振", "BZ");

            dic.Add("垂直(左)", "VL");
            dic.Add("垂直(右)", "VR");
            dic.Add("轴向(左)", "AL");
            dic.Add("轴向(右)", "AR");
            dic.Add("径向(左)", "RL");
            dic.Add("径向(右)", "RR");

            dic.Add("电压A相", "VOTA");
            dic.Add("电压B相", "VOTB");
            dic.Add("电压C相", "VOTC");
            dic.Add("电流A相", "CURA");
            dic.Add("电流B相", "CURB");
            dic.Add("电流C相", "CURC");

            dic.Add("螺栓一", "B1");
            dic.Add("螺栓二", "B2");
            dic.Add("螺栓三", "B3");
            dic.Add("螺栓四", "B4");
            dic.Add("螺栓五", "B5");
            dic.Add("螺栓六", "B6");
            dic.Add("螺栓七", "B7");
            dic.Add("螺栓八", "B8");

            // 加速度方案
            dic.Add("A内圈", "AI");
            dic.Add("A外圈", "AO");

            dic.Add("无", "NUL");

            dic.Add("转速", "ROT");

            // 光纤应变
            dic.Add("温度一", "T1");
            dic.Add("温度二", "T2");
            dic.Add("温度三", "T3");
            dic.Add("温度四", "T4");
            dic.Add("应变一", "S1");
            dic.Add("应变二", "S2");
            dic.Add("应变三", "S3");
            dic.Add("应变四", "S4");

            //方向
            dic.Add("0点", "0OC"); 
            dic.Add("1点", "1OC");
            dic.Add("2点", "2OC");
            dic.Add("3点", "3OC");
            dic.Add("4点", "4OC");
            dic.Add("5点", "5OC");
            dic.Add("6点", "6OC");
            dic.Add("7点", "7OC");
            dic.Add("8点", "8OC");
            dic.Add("9点", "9OC");
            dic.Add("10点", "10OC");
            dic.Add("11点", "11OC");
            dic.Add("12点", "12OC");

            // 法兰间隙
            dic.Add("0D", "000D");
            dic.Add("45D", "045D");
            dic.Add("90D", "090D");
            dic.Add("135D", "135D");
            dic.Add("180D", "180D");
            dic.Add("225D", "225D");
            dic.Add("270D", "270D");
            dic.Add("315D", "315D");


            return dic;
        }
        /// <summary>
        /// 工况参数字典
        /// </summary>
        /// <returns>名称,编码</returns>
        public static Dictionary<string, string> GetWorkCondDic()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            foreach (CMSFramework.BusinessEntity.EnumWorkCondition_ParamType item in Enum.GetValues(typeof(CMSFramework.BusinessEntity.EnumWorkCondition_ParamType)))
            {
                dic.Add(Utility.EnumHelper.GetDescription(item),((int)item).ToString());
            }
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_WindSpeed), "0");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Power), "1");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_RotSpeed), "3");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_YAWState), "4");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_MBRTemp), "2");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GB_HSSTemp), "5");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GB_OilTemp), "6");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GEN_DETemp), "7");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GEN_NDETemp), "8");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GEN_STR1Temp), "9");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GEN_STR2Temp), "10");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GEN_STR3Temp), "11");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_NAC_Temp), "12");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_RotSpeed_MCS), "13");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_NAC_Direction), "14");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_BLD_Angle1), "16");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_BLD_Angle2), "17");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_BLD_Angle3), "18");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Pitch_Motor1Temp), "19");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Pitch_Motor2Temp), "20");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Pitch_Motor3Temp), "21");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GBX_BRTemp1), "22");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_GBX_BRTemp2), "23");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_TD_MacTemp), "24");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_TD_NetTemp), "25");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_YAW_Power), "26");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_NAC_Location), "27");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_NAC_Wind), "28");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Tow_DEApe), "29");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Tow_NDEApe), "30");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_HubTemp), "31");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Blade_01Temp), "32");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Blade_02Temp), "33");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Blade_03Temp), "34");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Oil_Debris), "35");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_YawAngle), "36");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_WindDirection), "37");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_Environment_Temp), "38");
            //dic.Add(AppFramework.Utility.EnumHelper.GetDescription(EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION), "99");
            return dic;
        }

        /// <summary>
        /// 晃度测量位置
        /// </summary>
        /// <returns>位置,编号</returns>
        public static Dictionary<string, string> GetSVMLocDic()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            // 塔顶
            dic.Add("俯仰角", "Pi");
            dic.Add("横滚角", "Ro");
            dic.Add("垂直加速度", "Ve");
            dic.Add("水平加速度", "Ho");
            dic.Add("轴向加速度", "Ax");

            //塔基使用
            dic.Add("X轴倾角", "XI");
            dic.Add("Y轴倾角", "YI");
            dic.Add("X轴加速度", "XA");
            dic.Add("Y轴加速度", "YA");
            dic.Add("Z轴加速度", "ZA");

            //通用
            dic.Add("温度", "T");
            return dic;
        }
        /// <summary>
        /// 公司列表
        /// </summary>
        /// <returns>位置，编号</returns>
        public static Dictionary<string, string> GetGroupCompanyDic()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("华能集团", "HN");
            dic.Add("华电集团", "HD");
            dic.Add("龙源集团", "LY");
            dic.Add("中国大唐集团", "DT");
            //国家电投和中电投集团重组，但是不知道所属集团名称是否修改，所以两个先独立添加
            //后期如果统计，可以认为两个集团公司是一个 已“中电投”为主
            //@wangy 2016年9月22日
            dic.Add("中电投集团", "ZT");
            dic.Add("国家电投集团", "GT");

            dic.Add("国华集团", "GH");
            dic.Add("中广核集团", "ZH");
            //dic.Add("华润集团", "HR");
            dic.Add("国投电力集团", "GDT");

            dic.Add("中国水电集团", "ZS");
            dic.Add("中国电建集团", "ZDJ");
            dic.Add("中国三峡集团", "SX");
            //@wangy 2016年12月5日 新增 中国核电集团 编码
            dic.Add("中国核电集团", "ZHH");

            dic.Add("广东粤电集团", "YD");

            dic.Add("中国三峡新能源", "SX");
            // @chuss 2017年6月5日 新增 中科蓝天 编码
            dic.Add("中科蓝天", "ZK");
            // @wangy 2017年7月17日 新增 南高齿 编码
            dic.Add("南高齿", "NGC");
            // add by zhanggw @20170802 天合能源 Code：TH
            dic.Add("天合能源", "TH");
            // add by chuss @20170822 西北水电 Code：XBSD
            dic.Add("西北水电", "XBSD");
            // add by chuss @20180112 闽东电力 Code：MDDL
            dic.Add("闽东电力", "MDDL");
            // add by chuss @20180320 鲁能新能源 Code：LN
            dic.Add("鲁能新能源", "LN");
            // add by chuss @20180402 Goldwind.USA Code：USA
            dic.Add("Goldwind.USA", "USA");
            // add by chuss @20180417 华润电力 Code：HR
            dic.Add("华润电力", "HR");
            // add by chuss @20180511 协合新能源集团有限公司 Code：XH
            dic.Add("协合", "XH");
            // add by chuss @20180709 青海启明 Code：QM
            dic.Add("青海启明", "QM");
            // add by chuss @20180824 哈萨克斯坦 Code：KAZ
            dic.Add("Kazakhstan", "KAZ");
            // added by dds @2019-1-8 中国节能环保集团有限公司 Code：ZJN
            dic.Add("中国节能", "ZJN");
            // added by dds @2019-4-11 特变电工股份有限公司 TBEA Co.,Ltd  Code：TB
            dic.Add("特变电工", "TB");
            // added by chuss @2019-12-2 国家电网有限公司  Code：GW
            dic.Add("国家电网", "GW");
            // added by chuss @2019-12-2 天津津能国际新能源科技有限公司  Code：JN
            dic.Add("天津津能", "JN");
            // added by chuss @2019-12-17 辽宁华隆电力科技有限公司  Code：HL
            dic.Add("华隆电力", "HL");
            // added by chuss @2019-12-17 广东明阳新能源科技有限公司  Code：MY
            dic.Add("明阳能源", "MY");
            // added by chuss @2019-12-20 广东广业南华新能源有限公司  Code：GY
            dic.Add("广东广业", "GY");
            // added by dds @2020-4-7  大连汇能辽宁丰沃新能源有限公司 大连汇能 DH 
            dic.Add("大连汇能", "DH");
            // added by dds @2020-4-29 大同煤矿集团有限责任公司” “同煤集团” “TM”
            dic.Add("同煤集团", "TM");
            // added by dds @2020-8-25 “中国国电集团有限公司”“中国国电集团”“GD”
            dic.Add("中国国电集团", "GD");
            // added by dds @2020-11-16 内蒙古大漠风电有限责任公司  大漠风电 DM
            dic.Add("大漠风电", "DM");
            // added by dds @2020-11-16 盾安控股集团有限公司   盾安新能源  DA
            dic.Add("盾安新能源", "DA");
            //added by dds @2021-3-9 中车风电有限公司  中车风电  ZC
            dic.Add("中车风电", "ZC");
            //added by dds @2021-3-19 北京首华信能源科技开发有限公司  首华信  SHX
            dic.Add("首华信", "SHX");


            //added by dds @2021-4-29 河北建投新能源有限公司 河北建投 HJT
            dic.Add("河北建投", "HJT");
            //added by dds @2021-5-21 深圳能源集团股份有限公司 深圳能源 SN
            dic.Add("深圳能源", "SN");
            //added by dds@2021-6-28 国龙新能源有限公司 国龙 GL
            dic.Add("国龙", "GL");
            //added by dds@2021-7-5 上海电气环保集团 上气环保 SH
            dic.Add("上气环保", "SH");
            //added by dds@2021-7-6 国家能源投资集团有限责任公司 国家能源投资集团 GN
            dic.Add("国家能源投资集团", "GN");
            //added by liyanchao @2022-3-8  酒泉泰源新能源有限公司  酒泉泰源  TY
            dic.Add("酒泉泰源", "TY");
            // added by zhanggw @2022年3月15日  上海绿色环保能源有限公司  LNJ(LN和鲁能鲁能新能源重复)
            dic.Add("绿能", "LNJ");
            //added by liyanchao @2022-4-12  道裕科技（天津）有限公司  道裕科技  DY
            dic.Add("道裕科技", "DY");


            dic.Add("齐齐哈尔汇能", "QQHEHN");
            dic.Add("山能集团", "SN");
            dic.Add("天润新能","TRXN");
            dic.Add("内蒙古能源集团","NMNY");
            dic.Add("浙江新能源", "ZN");
            dic.Add("南方电网", "NW");
            dic.Add("京能国际", "JNGJ");

            //added by sunqi ,@2025/3/6  金麒麟新能源股份有限公司
            dic.Add("金麒麟新能源股份有限公司", "JQL");

            //added by sunqi,添加其他行业

            dic.Add("水泥集团", "NWSN");
            dic.Add("水务集团", "NWSW");
            dic.Add("制药集团", "NWZY");
            dic.Add("水利集团", "NWSL");
            dic.Add("钢铁集团", "NWGT");
            dic.Add("矿产集团", "NWKC");
            dic.Add("特种车辆集团", "NWSV");
            dic.Add("石化集团", "NWSH");
            dic.Add("轨道交通集团", "NWRT");
            dic.Add("冶金集团", "NWYJ");
            dic.Add("港务集团", "NWGW");
            dic.Add("煤炭集团", "NWMT");

            return dic;
        }
    }
}
