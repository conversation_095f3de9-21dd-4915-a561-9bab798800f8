﻿namespace WTCMSLive.WebSite.Models
{
    public class WindTurbineInfo
    {
        public string windTurbineId { get; set; }
        public string windTurbineName { get; set; }
        public DiagStatus diagStatus { get; set; }
        public Dictionary<string, string> WindTurbineComponent { get; set; }
        public DateTime windTurbineUpdateTime { get; set; }
        public DiagLevel diagLevel { get; set; }
        public DateTime diagTime { get; set; }
        public string alarmEvent { get; set; }
        public string DAUstatus { get; set; }
        public DateTime DAUupdateTime { get; set; }
    }

    public enum DiagLevel
    {
        Normal = 0,
        Waring = 1,
        Danger = 2,
    }
    public enum DiagStatus
    {
        Normal = 0,
        Apply = 1,
        Accept = 2,
        Diagnosis = 3,
        Complete = 4,
        Feedback = 5,
    }
}