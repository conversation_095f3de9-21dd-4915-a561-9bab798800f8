import{W as O,a as A}from"./table-DznTy2O5.js";import{O as F}from"./index-BKL_RKUZ.js";import{u as U}from"./account-D2g4QLee.js";import{u as q}from"./role-Cm5IINmK.js";import{g as C}from"./tools-DC78Tda0.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as P}from"./ActionButton-FpgTQOJj.js";import{M as V}from"./index-BD0EDEeZ.js";import{r as n,X as G,h as K,f as R,d as p,o as f,i as k,b as S,q as L,c as $,m as a}from"./index-BedJHPLx.js";import"./styleChecker-D6uzjM95.js";import"./index-CTEgH1Bv.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const z={key:1},J={__name:"users",setup(X){const i=U(),h=q();let I=window.localStorage.getItem("user")?JSON.parse(window.localStorage.getItem("user")):{};const _=(e={isform:!1,isEdit:!1})=>{let s=[{title:"用户名称",dataIndex:"userName",align:"center",columnWidth:150,formItemWidth:320,isrequired:!0},{title:"账号",dataIndex:"account",align:"center",columnWidth:150,formItemWidth:320,isrequired:!0,isdisplay:!e.isEdit,customRender:({record:t})=>t.userID||"",...e&&e.isform?{}:{customRender:({record:t})=>t.userID||""}},{title:"角色名称",dataIndex:"role",columnWidth:120,formItemWidth:320,align:"center",isrequired:!0,isdisplay:!e.isEdit,inputType:"select",selectOptions:[],...e&&e.isform?{}:{customRender:({record:t})=>t.userRole&&t.userRole.roleName?t.userRole.roleName:""}},{title:"邮箱",dataIndex:"email",align:"center",columnWidth:100,formItemWidth:320,validateRules:C({type:"email",title:"邮箱",required:!0})},{title:"电话",dataIndex:"phone",align:"center",canEdit:!0,columnWidth:100,formItemWidth:320,validateRules:C({type:"phone",title:"电话"})},{title:"状态",dataIndex:"userState",align:"center",columnWidth:80,formItemWidth:320,isdisplay:!1}];return e&&e.isform&&!e.isEdit?[...s,{title:"密码",dataIndex:"password",inputType:"password",align:"center",columnWidth:80,formItemWidth:320,isrequired:!0}]:s},d=n(""),r=n(""),v=n({}),l=n([]),g=n(!1),y=G({table:[],tableColumns:_()}),b=n(!1),u=async()=>{g.value=!0,y.table=await i.fetchGetuserlist(),g.value=!1};K(()=>{u()});const D=async()=>{h.roleOptions.length===0&&await h.fetchGetrolelist();let e=l.value;e[2].selectOptions=h.roleOptions,l.value=[...e]},W=()=>{b.value=!0},m=e=>{b.value=!1,l.value=[],v.value={},d.value="",r.value=""},x=e=>{const{title:o,operateType:s}=e;d.value="添加用户",r.value=s,l.value=[..._({isform:!0})],D(),W()},E=async e=>{const{tableKey:o,selectedkeys:s,record:t}=e;if(I&&I.userId&&I.userId===t.userID){a.error("当前用户不能删除!与登录用户一致！");return}let c=await i.fetchDeleteUser({account:t.userID});c&&c.code===1?(u(),a.success("提交成功")):a.error("提交失败:"+c.msg)},N=e=>{const{title:o,operateType:s,rowData:t}=e;d.value="编辑用户",r.value=s,v.value={...t,role:t.userRole&&t.userRole.roleID?t.userRole.roleID:"",account:t.userID},l.value=[..._({isform:!0,isEdit:!0})],D(),W()},T=async e=>{let o=await i.fetchResetUser({account:e.userID});o&&o.code===1?(u(),a.success("密码重置成功")):a.error("密码重置失败:"+o.msg)},M=async e=>{switch(r.value){case"add":let o=await i.fetchAdduser({...e,phone:e.phone||""});o&&o.code===1?(u(),a.success("提交成功"),m()):a.error("提交失败:"+o.msg);break;case"edit":let s=await i.fetchEdituser(e);s&&s.code===1?(u(),a.success("提交成功"),m()):a.error("提交失败:"+s.msg);break}};return(e,o)=>{const s=A,t=V,c=P;return f(),R(c,{spinning:g.value,size:"large"},{default:p(()=>[k("div",null,[S(O,{tableTitle:"用户列表","table-key":"0","table-columns":y.tableColumns,"table-operate":["add","edit","delete"],"record-key":"ModbusUnitID","table-datas":y.table,noBatchApply:!0,onAddRow:x,onEditRow:N,onDeleteRow:E,actionCloumnProps:{width:170,align:"center"},hideOperateColumnDataKey:{editkeys:{userID:"ADMIN_SUPER"},deletekeys:{userID:"ADMIN_SUPER"}}},{otherOperate:p(({record:w})=>[w.userID!=="ADMIN_SUPER"?(f(),R(s,{key:0,placement:"bottomRight","ok-text":"是",title:`${w.userName}密码将被重置为666666？`,"cancel-text":"否",onConfirm:j=>T(w)},{default:p(()=>o[0]||(o[0]=[k("span",{class:"editBtn"},"重置密码",-1)])),_:2,__:[0]},1032,["title","onConfirm"])):L("",!0)]),_:1},8,["table-columns","table-datas"])]),S(t,{maskClosable:!1,width:"600px",open:b.value,title:d.value,footer:"",onCancel:m},{default:p(()=>[r.value==="add"||r.value==="edit"?(f(),R(F,{key:0,titleCol:l.value,initFormData:v.value,onSubmit:M,onCancelForm:m},null,8,["titleCol","initFormData"])):(f(),$("div",z))]),_:1},8,["open","title"])]),_:1},8,["spinning"])}}},me=B(J,[["__scopeId","data-v-1b7a8509"]]);export{me as default};
