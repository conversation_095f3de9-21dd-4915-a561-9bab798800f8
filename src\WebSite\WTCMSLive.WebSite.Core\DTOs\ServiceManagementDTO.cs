using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 服务配置DTO
    /// </summary>
    public class ServiceConfigDTO
    {
        /// <summary>
        /// 服务ID（用于标识）
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 服务显示名称
        /// </summary>
        [Required(ErrorMessage = "服务名称不能为空")]
        public string ServiceName { get; set; }

        /// <summary>
        /// 系统服务名称（Windows服务名或Linux systemd服务名）
        /// </summary>
        [Required(ErrorMessage = "系统服务名称不能为空")]
        public string SystemServiceName { get; set; }

        /// <summary>
        /// 服务日志根目录路径（可选，如果为空则使用默认路径）
        /// </summary>
        public string LogRootPath { get; set; }
    }

    /// <summary>
    /// 服务状态DTO
    /// </summary>
    public class ServiceStatusDTO
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 服务显示名称
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// 系统服务名称
        /// </summary>
        public string SystemServiceName { get; set; }

        /// <summary>
        /// 服务状态
        /// </summary>
        public ServiceState Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 是否已安装
        /// </summary>
        public bool IsInstalled { get; set; }

        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 服务操作请求DTO
    /// </summary>
    public class ServiceOperationRequestDTO
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        [Required(ErrorMessage = "服务ID不能为空")]
        public string ServiceId { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required(ErrorMessage = "操作类型不能为空")]
        public ServiceOperation Operation { get; set; }
    }



    /// <summary>
    /// 服务状态枚举
    /// </summary>
    public enum ServiceState
    {
        /// <summary>
        /// 未安装
        /// </summary>
        NotInstalled = 0,

        /// <summary>
        /// 已停止
        /// </summary>
        Stopped = 1,

        /// <summary>
        /// 正在启动
        /// </summary>
        Starting = 2,

        /// <summary>
        /// 正在运行
        /// </summary>
        Running = 3,

        /// <summary>
        /// 正在停止
        /// </summary>
        Stopping = 4,

        /// <summary>
        /// 错误/未知
        /// </summary>
        Error = 5
    }

    /// <summary>
    /// 服务操作枚举
    /// </summary>
    public enum ServiceOperation
    {
        /// <summary>
        /// 启动
        /// </summary>
        Start = 1,

        /// <summary>
        /// 停止
        /// </summary>
        Stop = 2,

        /// <summary>
        /// 重启
        /// </summary>
        Restart = 3
    }

    /// <summary>
    /// 服务日志查询请求DTO
    /// </summary>
    public class ServiceLogQueryRequestDTO
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        [Required(ErrorMessage = "服务ID不能为空")]
        public string ServiceId { get; set; }

        /// <summary>
        /// 日志类型 (debuggerlog/errorlog)
        /// </summary>
        [Required(ErrorMessage = "日志类型不能为空")]
        public string LogType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 日志文件信息DTO
    /// </summary>
    public class LogFileInfoDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件大小（格式化）
        /// </summary>
        public string FileSizeFormatted { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifiedTime { get; set; }

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; }
    }

    /// <summary>
    /// 日志内容DTO
    /// </summary>
    public class LogContentDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; }
    }

    /// <summary>
    /// 实时日志项DTO
    /// </summary>
    public class RealTimeLogItemDTO
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string Level { get; set; }
    }

    /// <summary>
    /// 实时日志控制请求DTO
    /// </summary>
    public class RealTimeLogControlRequestDTO
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        [Required(ErrorMessage = "服务ID不能为空")]
        public string ServiceId { get; set; }

        /// <summary>
        /// 日志类型 (debuggerlog/errorlog)
        /// </summary>
        [Required(ErrorMessage = "日志类型不能为空")]
        public string LogType { get; set; }
    }
}
