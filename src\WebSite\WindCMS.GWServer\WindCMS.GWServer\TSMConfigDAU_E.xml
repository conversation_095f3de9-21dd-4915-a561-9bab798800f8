﻿<?xml version="1.0" encoding="utf-8" ?>
<ArrayOfTSMConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XM机组TS_Turbine_1未配置温度TSMLSchema">
	<!--
  配置说明:
  此文件为TSM温度模块的配置文件，如果DAU有额外的温度模块并且需要采集温度的时候，
  请根据需要配置，各字段含义和配置方法如下:
  windturbineID : 机组ID 
  channelID: 通道ID，一个机组的可以从0开始自增
  ChannelName：通道名称 和通道类型对应
  ChannelType: 通道类型，目前TSM模块的是用于叶片温度采集，所以有下面模板的四种类型。
  配置方法:
  1. 查出需要配置机组的ID
  2. 根据情况，如果温度模块的四个都要采集，则可以拷贝下面的内容，只修改"windturbineID"为对应的机组ID即可。
  -->
	<TSMConfig>
		<windturbineID></windturbineID>
		<DauID>2</DauID>
		<!--0代表独立温度板，1代表叶片扩展板-->
		<DauChannelID>24</DauChannelID>
		<!--Modbus地址-->
		<ModbusAddr>2</ModbusAddr>
		<!--采样时间-->
		<SamplingTime>120</SamplingTime>
		<!--采样频率-->
		<SampleFrequncy>1</SampleFrequncy>
		<!--波特率-->
		<BaudRate>115200</BaudRate>
		<ZeroPoint>1000</ZeroPoint>
		<!-- R>1000 -->
		<PlusK>0.25943</PlusK>
		<PlusB>-259.64</PlusB>
		<!-- R<=1000 -->
		<MinusK>0.25489</MinusK>
		<MinusB>-254.9</MinusB>
		<VirtualRs485Type>0</VirtualRs485Type>
		<!--TCP转RS485模块服务器端口号-->
		<TcpToRs485ModulePort>3000</TcpToRs485ModulePort>
		<!--TCP转RS485模块服务器IP-->
		<TcpToRs485ModuleIP>**************</TcpToRs485ModuleIP>
		<ChannelConfigs>
			<TSMChannelConfig>
				<channelID>0</channelID>
				<Binding>201</Binding>
				<ChannelName>叶片0温度</ChannelName>
				<ChannelType>WCPT_Blade_01Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>1</channelID>
				<Binding>202</Binding>
				<ChannelName>叶片1温度</ChannelName>
				<ChannelType>WCPT_Blade_02Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>2</channelID>
				<Binding>203</Binding>
				<ChannelName>叶片2温度</ChannelName>
				<ChannelType>WCPT_Blade_03Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>3</channelID>
				<Binding>204</Binding>
				<ChannelName>轮毂</ChannelName>
				<ChannelType>WCPT_HubTemp</ChannelType>
				<IsGetWaveData>false</IsGetWaveData>
			</TSMChannelConfig>
		</ChannelConfigs>
		<Evs>
			<MeasDef_Ev_Process>
				<EvId>201</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<MeasDef_Ev_Process>
				<EvId>202</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<MeasDef_Ev_Process>
				<EvId>203</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<!-- <MeasDef_Ev_Process> -->
			<!-- <EvId>204</EvId> -->
			<!-- <Type>Enum_Temperature</Type> -->
			<!-- <Name>Temperature</Name> -->
			<!-- </MeasDef_Ev_Process> -->
		</Evs>
	</TSMConfig>
	<TSMConfig>
		<windturbineID>ZHH1110006</windturbineID>
		<DauID>2</DauID>
		<!--0代表独立温度板，1代表叶片扩展板-->
		<DauChannelID>24</DauChannelID>
		<!--Modbus地址-->
		<ModbusAddr>2</ModbusAddr>
		<!--采样时间-->
		<SamplingTime>120</SamplingTime>
		<!--采样频率-->
		<SampleFrequncy>1</SampleFrequncy>
		<!--波特率-->
		<BaudRate>115200</BaudRate>
		<ZeroPoint>1000</ZeroPoint>
		<!-- R>1000 -->
		<PlusK>0.25943</PlusK>
		<PlusB>-259.64</PlusB>
		<!-- R<=1000 -->
		<MinusK>0.25489</MinusK>
		<MinusB>-254.9</MinusB>
		<VirtualRs485Type>0</VirtualRs485Type>
		<!--TCP转RS485模块服务器端口号-->
		<TcpToRs485ModulePort>3000</TcpToRs485ModulePort>
		<!--TCP转RS485模块服务器IP-->
		<TcpToRs485ModuleIP>**************</TcpToRs485ModuleIP>
		<ChannelConfigs>
			<TSMChannelConfig>
				<channelID>0</channelID>
				<Binding>201</Binding>
				<ChannelName>叶片0温度</ChannelName>
				<ChannelType>WCPT_Blade_01Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>1</channelID>
				<Binding>202</Binding>
				<ChannelName>叶片1温度</ChannelName>
				<ChannelType>WCPT_Blade_02Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>2</channelID>
				<Binding>203</Binding>
				<ChannelName>叶片2温度</ChannelName>
				<ChannelType>WCPT_Blade_03Temp</ChannelType>
				<IsGetWaveData>true</IsGetWaveData>
			</TSMChannelConfig>
			<TSMChannelConfig>
				<channelID>3</channelID>
				<Binding>204</Binding>
				<ChannelName>轮毂</ChannelName>
				<ChannelType>WCPT_HubTemp</ChannelType>
				<IsGetWaveData>false</IsGetWaveData>
			</TSMChannelConfig>
		</ChannelConfigs>
		<Evs>
			<MeasDef_Ev_Process>
				<EvId>201</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<MeasDef_Ev_Process>
				<EvId>202</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<MeasDef_Ev_Process>
				<EvId>203</EvId>
				<Type>Enum_Temperature</Type>
				<Name>Temperature</Name>
			</MeasDef_Ev_Process>

			<!-- <MeasDef_Ev_Process> -->
			<!-- <EvId>204</EvId> -->
			<!-- <Type>Enum_Temperature</Type> -->
			<!-- <Name>Temperature</Name> -->
			<!-- </MeasDef_Ev_Process> -->
		</Evs>
	</TSMConfig>
</ArrayOfTSMConfig>
