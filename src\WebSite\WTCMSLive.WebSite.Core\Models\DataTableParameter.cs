﻿namespace WTCMSLive.WebSite.Models
{
    public class DataTableParameter
    {
        /// <summary>
        /// DataTable请求服务器端次数
        /// </summary>       
        public string sEcho { get; set; }

        /// <summary>
        /// 过滤文本
        /// </summary>
        public string sSearch { get; set; }

        /// <summary>
        /// 每页显示的数量
        /// </summary>
        public int iDisplayLength { get; set; }

        /// <summary>
        /// 分页时每页跨度数量
        /// </summary>
        public int iDisplayStart { get; set; }

        /// <summary>
        /// 列数
        /// </summary>
        public int iColumns { get; set; }

        /// <summary>
        /// 排序列的数量
        /// </summary>
        public int iSortingCols { get; set; }

        /// <summary>
        /// 逗号分割所有的列
        /// </summary>
        public string sColumns { get; set; }
    }

    public class BaseTableModel
    {
        /// <summary>
        /// table 的前台ID
        /// </summary>
        public string tableName { get; set; }
        /// <summary>
        /// table 的行数据
        /// </summary>
        public Rows[] rows { get; set; }
        /// <summary>
        /// table行的整体样式
        /// </summary>
        public string style { get; set; }
        ////取得行数据
        //public List<Cells> GetRows()
        //{
        public Rows[] addEmptyRow(int count)
        {
            List<Rows> rows = new List<Rows>();
            Rows row = new Rows();
            List<Cell> cells = new List<Cell>();
            for (int i = 0; i < count; i++)
            {
                Cell cell = new Cell();
                cell.displayValue = "";
                cells.Add(cell);
            }
            row.cells = cells.ToArray();
            rows.Add(row);
            return rows.ToArray();
        }
        //}
    }
    /// <summary>
    /// 定义行内各个单元的详细
    /// </summary>
    public class Rows
    {
        /// <summary>
        /// 行样式
        /// </summary>
        public string style { get; set; }
        /// <summary>
        /// 行内数据
        /// </summary>
        public Cell[] cells { get; set; }
    }
    /// <summary>
    /// 单元格对象
    /// </summary>
    public class Cell
    {
        /// <summary>
        /// 单元格样式
        /// </summary>
        public string style { get; set; }
        /// <summary>
        /// 单元格内容颜色
        /// </summary>
        public string color { get; set; }
        /// <summary>
        /// 显示值
        /// </summary>
        public string displayValue { get; set; }
        /// <summary>
        /// Title设置
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 单元格类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 链接事件
        /// </summary>
        public string onclick { get; set; }
        /// <summary>
        /// 链接地址
        /// </summary>
        public string href { get; set; }
        /// <summary>
        /// 图标
        /// </summary>
        public string icon { get; set; }
    }
}