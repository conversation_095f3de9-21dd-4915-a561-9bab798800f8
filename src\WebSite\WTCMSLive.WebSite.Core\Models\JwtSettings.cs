namespace WTCMSLive.WebSite.Core.Models
{
    public class JwtSettings
    {
        public string SecretKey { get; set; }
        public string Issuer { get; set; }
        public string Audience { get; set; }
        public int ExpirationInMinutes { get; set; }

        /// <summary>
        /// 续签宽限期（分钟），在Token过期前多长时间允许续签
        /// </summary>
        public int RefreshGracePeriodInMinutes { get; set; } = 10;
    }
}