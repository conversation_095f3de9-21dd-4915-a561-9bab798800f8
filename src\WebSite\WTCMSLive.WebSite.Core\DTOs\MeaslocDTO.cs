﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Core.DTOs
{
    public class MeaslocDTO
    {

        public string MeasLocationID { get; set; }

        public string WindTurbineID { get; set; }

        public string MeasLocName { get; set; }

        public int OrderSeq { get; set; }

        public string ComponentID { get; set; }

        public string SectionName { get; set; }
        public string Orientation { get; set; }
        public float GearRatio { get; set; }

        public string ComponentName { get; set; }
    }


    public class MeaslocDelDTO
    {

        public string MeasLocationID { get; set; }

        public string WindTurbineID { get; set; }

       
    }


    
}
