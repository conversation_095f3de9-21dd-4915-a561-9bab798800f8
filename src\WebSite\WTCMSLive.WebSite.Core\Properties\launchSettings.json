{"profiles": {"WTCMSLive.WebSite.Core": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ProxyTo": "http://localhost:55523"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:803", "hotReloadEnabled": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ProxyTo": "http://localhost:55523"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:32245", "sslPort": 0}}}