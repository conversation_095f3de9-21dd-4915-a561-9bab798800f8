﻿using System;
using System.Collections.Generic;
using System.Linq;
using CMSFramework.BusinessEntity;
using CMSFramework.MeasDefEntities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 测量定义逻辑层
    /// </summary>
    public static class MeasDefinitionManagement
    {

        //  #region 测量定义管理

        /// <summary>
        /// 获取机组下测量定义列表
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static List<MeasDefinition> GetMeasDefListByTurId(string _turbineId)
        {
            List<MeasDefinition> mdfModels = null;
            List<MeasDefinition_Ex> mdfex = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                //mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == _turbineId).OrderBy(t=> int.Parse(t.MeasDefinitionID)).ToList();
                mdfModels = ctx.MeasDefinitions
                            .Where(item => item.WindTurbineID == _turbineId)
                            .ToList() // 先将数据加载到内存
                            .OrderBy(t => int.Parse(t.MeasDefinitionID)) // 在内存中进行排序
                            .ToList();
                mdfex = ctx.MeasDefinitions_Exs.Where(item => item.WindTurbineID == _turbineId).ToList();

                foreach (var item in mdfModels)
                {
                    // 找到对应的DAU信息
                    MeasDefinition_Ex entity = mdfex.FirstOrDefault(obj => obj.WindTurbineID == item.WindTurbineID && obj.MeasDefinitionID == item.MeasDefinitionID);
                    item.Mdf_Ex = entity;

                    // 特征值
                    item.VibEigenValueConf = ctx.TimeDomainEvConfs.Where(t => t.WindTurbineID == item.WindTurbineID && t.MeasDefinitionID == item.MeasDefinitionID).ToList();
                    item.ProcessSuperviseDefList = ctx.ProcessEvConfs.Where(t => t.WindTurbineID == item.WindTurbineID && t.MeasDefinitionID == item.MeasDefinitionID).ToList();

                    // 触发采集
                    var trigger = ctx.TriggerRuleDefs.Where(t => t.WindTurbineID == item.WindTurbineID && t.MeasDefinitionID == item.MeasDefinitionID).ToList();
                    if (trigger.Count > 0)
                    {
                        foreach(var tri in trigger)
                        {
                            tri.SupervisedVariables = ctx.TriggerProcess.Where(t => t.RuleID == tri.RuleID).ToList();
                            tri.TriggerTime = ctx.TriggerTimes.FirstOrDefault(t => t.RuleID == tri.RuleID);
                            tri.ExecuteMdfs = ctx.ExecuteMdfs.Where(t => t.RuleID == tri.RuleID).ToList();
                        }
                    }
                    item.TriggerRules = trigger;

                }
            }
            
            return mdfModels;
        }

        /// <summary>
        /// 删除测量定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefId"></param>
        public static void DeleteMeasdefinition(string _turID, string _measDefId)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MeasDefinitions.RemoveRange(ctx.MeasDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                //Modify by zhanggw 删除测量定义时候，同时删除测量定义和DAU的关联。
                ctx.MeasDefinitions_Exs.RemoveRange(ctx.MeasDefinitions_Exs.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.SaveChanges();
                // 兼容sqlite，手动删除波形定义
                ctx.MDFWaveDefinitions.RemoveRange(ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.MDFWaveDefRotSpds.RemoveRange(ctx.MDFWaveDefRotSpds.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.MDFWorkConditions.RemoveRange(ctx.MDFWorkConditions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.SVMWaveDefinitions.RemoveRange(ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));

                // 删除modbus波形
                ctx.WDFModbusDefs.RemoveRange(ctx.WDFModbusDefs.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));

                ctx.MdfWaveDefVoltageCurrents.RemoveRange(ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));

                // 删除特征值
                ctx.ProcessEvConfs.RemoveRange(ctx.ProcessEvConfs.Where(t => t.WindTurbineID == _turID && t.MeasDefinitionID == _measDefId));
                ctx.TimeDomainEvConfs.RemoveRange(ctx.TimeDomainEvConfs.Where(t => t.WindTurbineID == _turID && t.MeasDefinitionID == _measDefId));

                // 删除触发采集
                var trigger = ctx.TriggerRuleDefs.Where(t=>t.WindTurbineID == _turID && t.MeasDefinitionID == _measDefId).ToList();
                if(trigger!=null && trigger.Count > 0)
                {
                    foreach(var tr in trigger)
                    {
                        ctx.TriggerProcess.RemoveRange(ctx.TriggerProcess.Where(t => t.RuleID == tr.RuleID));
                        ctx.TriggerTimes.RemoveRange(ctx.TriggerTimes.Where(t => t.RuleID == tr.RuleID));
                        ctx.ExecuteMdfs.RemoveRange(ctx.ExecuteMdfs.Where(t => t.RuleID == tr.RuleID));
                    }

                    ctx.TriggerRuleDefs.RemoveRange(trigger);
                }

                ctx.SaveChanges();
            }
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                ctx.MeasEventEVs.RemoveRange(ctx.MeasEventEVs.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.WFDataContext_RT ctx = new CMSFramework.EF.WFDataContext_RT(ConfigInfo.DBConnName))
            {
                ctx.MeasEvent_Waves.RemoveRange(ctx.MeasEvent_Waves.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId));
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_turID);
        }

        public static void DeleteMeasdefinition(string _turID)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MeasDefinitions.RemoveRange(ctx.MeasDefinitions.Where(item => item.WindTurbineID == _turID));
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                ctx.MeasEventEVs.RemoveRange(ctx.MeasEventEVs.Where(item => item.WindTurbineID == _turID));
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.WFDataContext_RT ctx = new CMSFramework.EF.WFDataContext_RT(ConfigInfo.DBConnName))
            {
                ctx.MeasEvent_Waves.RemoveRange(ctx.MeasEvent_Waves.Where(item => item.WindTurbineID == _turID));
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_turID);
        }

        private static void AddMeasDefinition(MeasDefinition _measDef, List<MeasLoc_Process> _proMeasLocList
            , List<MeasLoc_RotSpd> rotSpdLocList, List<string> _measLocIdList, List<string> _DAUmeasLocIdList, int RotWaveLineCounts, string dauid)
        {
            _measDef.RotSpdWaveDefList = new List<WaveDef_RotSpd>();
            //添加测量定义和转速测量位置的关联 
            rotSpdLocList.ForEach(
                rotSpdLoc =>
                {
                    WaveDef_RotSpd waveDefRotSpd = new WaveDef_RotSpd()
                    {
                        WindTurbineID = _measDef.WindTurbineID,
                        MeasDefinitionID = _measDef.MeasDefinitionID,
                        MeasLoc_RotSpdID = rotSpdLoc.MeasLocationID,
                        LineCounts = RotWaveLineCounts
                    };
                    _measDef.RotSpdWaveDefList.Add(waveDefRotSpd);
                });
            _measDef.ProcessDefList = new List<MeasDef_Process>();
            GetNewWordProcessLocList(_measDef, _proMeasLocList, _DAUmeasLocIdList, _measLocIdList);
            MeasDefinition_Ex measEx = new MeasDefinition_Ex()
            {
                WindTurbineID = _measDef.WindTurbineID,
                MeasDefinitionID = _measDef.MeasDefinitionID,
                DauID = dauid,
                DaqInterval = _measDef.Mdf_Ex.DaqInterval,
                ModelType = _measDef.Mdf_Ex.ModelType,
            };
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                // 添加测量定义
                ctx.MeasDefinitions.Add(_measDef);
                ctx.MeasDefinitions_Exs.Add(measEx);
                ctx.SaveChanges();
                ctx.MDFWaveDefRotSpds.AddRange(_measDef.RotSpdWaveDefList);
                ctx.MDFWorkConditions.AddRange(_measDef.ProcessDefList);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 获取工况测量位置
        /// </summary>
        /// <param name="_measDef"></param>
        /// <param name="_proMeasLocList"></param>
        /// <param name="_DAUmeasLocIdList"></param>
        private static void GetNewWordProcessLocList(MeasDefinition _measDef, List<MeasLoc_Process> _proMeasLocList, List<string> _DAUmeasLocIdList, List<string> _measLocIdList)
        {
            //把油液磨粒工况特殊处理
            var oil = _proMeasLocList.Find(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris);
            if (oil != null)
            {
                _measDef.ProcessDefList.Add(new MeasDef_Process()
                {
                    WindTurbineID = _measDef.WindTurbineID,
                    MeasDefinitionID = _measDef.MeasDefinitionID,
                    MeasLocationID = oil.MeasLocationID
                });
            }
            //关联主控下的工况
            //_proMeasLocList.Where(item => item.FieldBusType == EnumWorkConDataSource.ModbusOnTcp).ToList().ForEach(
            //        proMeasLoc =>
            //        {
            //            MeasDef_Process defWorkCondition = new MeasDef_Process() { WindTurbineID = _measDef.WindTurbineID, MeasDefinitionID = _measDef.MeasDefinitionID, MeasLocationID = proMeasLoc.MeasLocationID };
            //            _measDef.ProcessDefList.Add(defWorkCondition);
            //        }
            //    );

            if (_measLocIdList != null)
            {
                _measLocIdList.ForEach(item =>
                {
                    var data = item.Split('#');
                    MeasDef_Process defWorkCondition = new MeasDef_Process()
                    {
                        WindTurbineID = _measDef.WindTurbineID,
                        MeasDefinitionID = _measDef.MeasDefinitionID,
                        MeasLocationID = data[0],
                        UpperLimitFreqency = float.Parse(data[1]),
                        SampleLength = short.Parse(data[2])
                    };
                    _measDef.ProcessDefList.Add(defWorkCondition);
                });
            }
            //关联DAU下的工况
            if (_DAUmeasLocIdList != null)
            {
                _DAUmeasLocIdList.ForEach(item =>
                {
                    var data = item.Split('#');
                    MeasDef_Process defWorkCondition = new MeasDef_Process()
                    {
                        WindTurbineID = _measDef.WindTurbineID,
                        MeasDefinitionID = _measDef.MeasDefinitionID,
                        MeasLocationID = data[0],
                        UpperLimitFreqency = float.Parse(data[1]),
                        SampleLength = short.Parse(data[2])
                    };
                    _measDef.ProcessDefList.Add(defWorkCondition);
                });
            }
        }

        #region 工况波形定义
        /// <summary>
        /// 工况测量定义添加
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="measLocId"></param>
        /// <param name="frequency"></param>
        /// <param name="len"></param>
        public static void SetWorkCondMeasdefinition(string WindTurbineID, string MeasDefinitionID, string measLocId,int UpperLimitFreqency, int SampleLength)
        {
            List<MeasLoc_Process> _ALLprocessLocList = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                _ALLprocessLocList.AddRange(ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == WindTurbineID));
                //if (_isAcqRotSpd && rotSpdLocList != null)
                //{
                //    rotSpdLocList.AddRange(ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _measDef.WindTurbineID));
                //}
            }
            var measDefProcess = _ALLprocessLocList.Find(item => item.MeasLocationID == measLocId);
            if (measDefProcess != null)
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {

                    var _mdf = ctx.MDFWorkConditions.FirstOrDefault(t => t.WindTurbineID == WindTurbineID && t.MeasDefinitionID == MeasDefinitionID && t.MeasLocationID == measLocId);
                    if(_mdf != null)
                    {
                        // 修改
                        _mdf.UpperLimitFreqency = UpperLimitFreqency;
                        _mdf.SampleLength = SampleLength;
                    }
                    else
                    {
                        // 新增
                        ctx.MDFWorkConditions.Add(new MeasDef_Process()
                        {
                            WindTurbineID = WindTurbineID,
                            MeasDefinitionID = MeasDefinitionID,
                            MeasLocationID = measLocId,
                            UpperLimitFreqency = UpperLimitFreqency,
                            SampleLength = SampleLength
                        });
                    }
                    ctx.SaveChanges();
                }
            }
        }
        /// <summary>
        /// 删除工况配置
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasDefinitionID"></param>
        /// <param name="measLocId"></param>
        //public static void DeleteWorkCondMeasdefinition(string WindTurbineID, string MeasDefinitionID, string measLocId)
        //{
        //    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
        //    {

        //        ctx.MDFWorkConditions.RemoveRange(ctx.MDFWorkConditions.Where(item => item.WindTurbineID == WindTurbineID && item.MeasDefinitionID == MeasDefinitionID && item.MeasLocationID == measLocId));
        //        ctx.SaveChanges();
        //    }
        //}
        /// <summary>
        /// 工况配置修改
        /// </summary>
        /// <param name="turbineid"></param>
        /// <returns></returns>
        //public static void GetWorkCondEigenValue(string turbineid, string MeasDefinitionID, string DauID,string wkname,int wkcode)
        //{
        //    using(CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
        //    {
        //        var _md = ctx.MeasDefinitions_Exs.FirstOrDefault(t => t.WindTurbineID == turbineid && t.MeasDefinitionID == MeasDefinitionID && t.DauID == DauID);

        //        if (_md != null)
        //        {
        //            JObject jsonData = null;
        //            if (string.IsNullOrEmpty(_md.XmlEigenValueDef))
        //            {
        //                // 构建数据json格式
        //                jsonData = new JObject()
        //                {
        //                    {"EstEV",new JObject{
        //                        { "GeneralEVList",new JArray(){
        //                            new JObject{
        //                                new JProperty("EvId",wkcode),
        //                                new JProperty("Name",wkname),
        //                                new JProperty("Type",27),
        //                            }
        //                        } },
        //                        { "SNBEList",new JArray() },
        //                        { "HFNBEList",new JArray() },
        //                        { "SFNBEList",new JArray() },
        //                    } },
        //                    {"SpvEV",null},
        //                };
        //            }
        //            else
        //            {
        //                // 追加
        //                //JArray _mdJArray = JObject.FromObject(_md.XmlEigenValueDef)["EstEV"]["GeneralEVList"].Value<JArray>();
        //                jsonData = JObject.Parse(_md.XmlEigenValueDef);
        //                var _cur = jsonData["EstEV"]["GeneralEVList"].FirstOrDefault(t => t["Name"].Value<string>() == wkname && t["Type"].Value<int>() == wkcode);
        //                if (_cur == null)
        //                {
        //                    jsonData["EstEV"]["GeneralEVList"].Value<JArray>().Add(new JObject() {

        //                        { "EvId",wkcode},
        //                        { "Name",wkname},
        //                        { "Type",27}
        //                    });
        //                }
        //            }

        //            if (jsonData != null)
        //            {
        //                _md.XmlEigenValueDef = jsonData.ToString().Replace("\n", "").Replace(" ", "").Replace("\t", "").Replace("\r", "");
        //                ctx.SaveChanges();
        //                // 提升DAU测量定义版本
        //                DauManagement.UpdateMeasDefVersion(turbineid.ToString());
        //            }
        //        }
        //    }
        //}
        //public static void DeleteWorkCondEigenValue(string turbineid, string MeasDefinitionID, string DauID, string wkname, int wkcode)
        //{
        //    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
        //    {
        //        var _md = ctx.MeasDefinitions_Exs.FirstOrDefault(t => t.WindTurbineID == turbineid && t.MeasDefinitionID == MeasDefinitionID && t.DauID == DauID);

        //        if (_md != null && !string.IsNullOrEmpty(_md.XmlEigenValueDef))
        //        {
        //            JObject xmldata = JObject.Parse(_md.XmlEigenValueDef);
        //            JArray _mdJArray = xmldata["EstEV"]["GeneralEVList"].Value<JArray>();
        //            _mdJArray.Remove(_mdJArray.FirstOrDefault(t => t["Name"].Value<string>() == wkname && t["EvId"].Value<int>() == wkcode));
        //            xmldata["EstEV"]["GeneralEVList"] = _mdJArray;

        //            _md.XmlEigenValueDef = xmldata.ToString();
        //            ctx.SaveChanges();
        //        }
        //    }
        //}

        public static string EigenStringConverToJSON(string GeneralEVList, string FBEList)
        {
            JObject res = new JObject()
            {
                {"GeneralEVList",new JArray()},
                {"FBEList",new JArray()},
                {"SNBEList",new JArray() },
                {"HFNBEList",new JArray() },
                {"SFNBEList",new JArray()},
            };
            int id = 1;
            // 通用特征值
            if (!string.IsNullOrEmpty(GeneralEVList))
            {
                JArray generalArr = new JArray();
                List<string> general = GeneralEVList.Split(',').ToList();
         
                for(int i = 0; i < general.Count; i++)
                {
                    generalArr.Add(new JObject()
                    {
                        {"EvId",id++},
                        {"Name",general[i]},
                        {"Type", CommonRefData.MeasEVType.ContainsKey(general[i])?CommonRefData.MeasEVType[general[i]]:0},
                        {"ConstraintType", null},
                        {"ConstraintScale", null},
                        {"Spectraltype", null},
                        {"UnderLimitValue", null},
                        {"UnderLimitUnit", null},
                        {"UpperLimitValue",null},
                        {"UpperLimitUnit", null},
                        {"CenterOrder", null},
                        {"Bandwidth", null},
                        {"ScanningTime",null},
                        {"RegisterAddress", null},
                    });
                }
                res["GeneralEVList"] = generalArr;
            }

            // 频带特征值
            if (!string.IsNullOrEmpty(FBEList))
            {
                JArray fbelist = new JArray();
                List<string> fbe = FBEList.Split(',').ToList();
                for (int i = 0; i < fbe.Count; i++)
                {
                    List<string> _cur = fbe[i].Split('#').ToList();

                    fbelist.Add(new JObject()
                    {
                        {"EvId", id++},
                        {"Name","BRMS"}, //_cur[0]  //RMS
                        {"UnderLimitValue", _cur[1]},
                        {"UpperLimitValue", _cur[2]},

                        {"ConstraintType", "1"},
                        {"ConstraintScale", "20"},
                        {"Spectraltype", "1"},
                        {"UnderLimitUnit", "1"},
                        {"UpperLimitUnit", "1"},
                        {"CenterOrder", null},
                        {"Bandwidth", null},
                        {"ScanningTime",null},
                        {"RegisterAddress", null},
                        {"Type", 14},
                    });
                }
                res["FBEList"] = fbelist;
            }

            return new JObject()
            {
                {"EstEV",res },
                {"SpvEV",null },
            }.ToString().Replace("\n", "").Replace(" ", "").Replace("\t", "").Replace("\r", "");
            //return res.ToString();
        }
        #endregion


        /// <summary>
        /// 添加测量定义
        /// </summary>
        /// <param name="_measDef"></param>
        /// <param name="_measLocIdList"></param>
        /// <param name="_isAcqRotSpd"></param>
        public static void AddMeasdefinition(
            MeasDefinition _measDef,
            List<string> _measLocIdList,
            List<string> _DAUmeasLocIdList,
            bool _isAcqRotSpd, int RotWaveLineCounts,
            string dauid)
        {
            // 获取工况测量位置
            List<MeasLoc_Process> _processLocList = new List<MeasLoc_Process>();
            // 获取机组的转速测量位置 
            List<MeasLoc_RotSpd> rotSpdLocList = new List<MeasLoc_RotSpd>();
            GetMeasLocProcess(_measDef, _measLocIdList, _DAUmeasLocIdList, _isAcqRotSpd, _processLocList, rotSpdLocList);
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence seq = ctx.SysIDSequence.Find(EnumSequenceType.MeasDefinitionID);
                seq.LastNumber += 1;
                ctx.SysIDSequence.Attach(seq);
                ctx.Entry(seq).State = EntityState.Modified;
                ctx.SaveChanges();
                _measDef.MeasDefinitionID = seq.LastNumber.ToString();
            }
            AddMeasDefinition(_measDef, _processLocList, rotSpdLocList, _measLocIdList, _DAUmeasLocIdList, RotWaveLineCounts, dauid);

            // 提升DAU测量定义版本
            DauManagement.UpdateMeasDefVersion(_measDef.WindTurbineID, dauid);
        }
        /// <summary>
        /// 添加的工况特征值信息
        /// </summary>
        public static void EditMeasDefSupervise2(string WindTurbineID, MeasDefinition measDefinition, string conditionMonitoringLocIds,bool IsAcqRotSpd)
        {
            CMSFramework.BusinessEntity.MeasDef_Supervise measDef_Supervise = new MeasDef_Supervise();

            List<MeasLoc_Process> allProcessLocList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                allProcessLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == measDefinition.WindTurbineID).ToList();
            }

            //添加转速
            List<MeasLoc_RotSpd> allRotSpdList = null;
            using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                allRotSpdList = ctxx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == measDefinition.WindTurbineID).ToList();
            }

            
            List<string> evList = new List<string>();
            if (!string.IsNullOrEmpty(conditionMonitoringLocIds))
            {
                evList = conditionMonitoringLocIds.Split(',').ToList();
            }

            List<MeasDef_Ev_Process> res = new List<MeasDef_Ev_Process>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.ProcessEvConfs.RemoveRange(ctx.ProcessEvConfs.Where(item => item.WindTurbineID == measDefinition.WindTurbineID && item.MeasDefinitionID == measDefinition.MeasDefinitionID));
                ctx.SaveChanges();

                int maxIndex = ctx.ProcessEvConfs?.Max(t => (int?)t.EvId) ?? 0;
                foreach (string data in evList)
                {
                    maxIndex++;
                    if (data.Contains("RSPD"))
                    {
                        var spd = allRotSpdList.FirstOrDefault(t => t.MeasLocationID == data);

                        if (spd != null)
                        {
                            res.Add(new MeasDef_Ev_Process()
                            {
                                MeasDefinitionID = measDefinition.MeasDefinitionID,
                                WindTurbineID = measDefinition.WindTurbineID,
                                //EvId = (int)EnumWorkCondition_ParamType.WCPT_RotSpeed,
                                MeasLocationID = data,
                                Name = "WCPT_RotSpeed",
                                Type = EnumEigenvalueName.Enum_SpdMean,
                                EvId = maxIndex+1,
                            });
                        }
                        
                    }
                    else
                    {
                        // 工况
                        var measDefProcess = allProcessLocList.FirstOrDefault(item => item.MeasLocationID == data);
                        if (measDefProcess != null)
                        {
                            res.Add(new MeasDef_Ev_Process()
                            {
                                MeasDefinitionID = measDefinition.MeasDefinitionID,
                                WindTurbineID = measDefinition.WindTurbineID,
                                //EvId = (int)measDefProcess.Param_Type_Code,
                                MeasLocationID = data,
                                Name = measDefProcess.Param_Type_Code.ToString(),
                                Type = EnumEigenvalueName.Enum_WorkEnv,
                                EvId = maxIndex + 1,
                            }) ;
                        }
                    }
                }
                ctx.ProcessEvConfs.AddRange(res);
                ctx.SaveChanges();
                /*                if (IsAcqRotSpd == true) {
                                List<MeasLoc_RotSpd> allRotSpdList = null;
                                using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                {
                                    allRotSpdList = ctxx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == measDefinition.WindTurbineID).ToList();
                                }
                                string MeasLocationRotSpdID = allRotSpdList[0].MeasLocationID;
                                ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                                {
                                    MeasDefinitionID = measDefinition.MeasDefinitionID,
                                    WindTurbineID = measDefinition.WindTurbineID,
                                    EvId = ((int)EnumWorkCondition_ParamType.WCPT_RotSpeed).ToString(),
                                    MeasLocationID = MeasLocationRotSpdID,
                                    SuperviseEvName = "WCPT_RotSpeed",
                                });
                                ctx.SaveChanges();
                                }*/

            }
        }
        /// <summary>
        /// 修改的工况特征值信息
        /// </summary>
        public static void EditMeasDefSupervise(string WindTurbineID, MeasDefinition measDefinition, string conditionMonitoringLocIds, bool IsAcqRotSpd)
        {
            CMSFramework.BusinessEntity.MeasDef_Supervise measDef_Supervise = new MeasDef_Supervise();

            List<MeasLoc_Process> allProcessLocList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                allProcessLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == measDefinition.WindTurbineID).ToList();
            }
            //主控下的工况
            List<string> measLocIdList = new List<string>();
            if (!string.IsNullOrEmpty(conditionMonitoringLocIds))
            {
                measLocIdList = conditionMonitoringLocIds.Split(',').ToList();
            }
            List<MeasDef_Ev_Process> measdefSuperviseProcessList = new List<MeasDef_Ev_Process>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.ProcessEvConfs.RemoveRange(ctx.ProcessEvConfs.Where(item => item.WindTurbineID == measDefinition.WindTurbineID && item.MeasDefinitionID == measDefinition.MeasDefinitionID));
                ctx.SaveChanges();

                foreach (string data in measLocIdList)
                {
                    string proId = data.Split('#')[0];
                    if (proId != WindTurbineID + "RSPD")
                    {
                        var measDefProcess = allProcessLocList.Find(item => item.MeasLocationID == proId);
                        ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                        {
                            MeasDefinitionID = measDefinition.MeasDefinitionID,
                            WindTurbineID = measDefinition.WindTurbineID,
                            EvId = (int)measDefProcess.Param_Type_Code,
                            MeasLocationID = proId,
                            Name = measDefProcess.Param_Type_Code.ToString(),
                               Type = EnumEigenvalueName.Enum_WorkEnv,
                        });
                        ctx.SaveChanges();
                    }
                    else
                    {
                        //添加转速
                        List<MeasLoc_RotSpd> allRotSpdList = null;
                        using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            allRotSpdList = ctxx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == measDefinition.WindTurbineID).ToList();
                        }
                        string MeasLocationRotSpdID = allRotSpdList[0].MeasLocationID;
                        ctx.ProcessEvConfs.Add(new MeasDef_Ev_Process()
                        {
                            MeasDefinitionID = measDefinition.MeasDefinitionID,
                            WindTurbineID = measDefinition.WindTurbineID,
                            EvId = (int)EnumWorkCondition_ParamType.WCPT_RotSpeed,
                            MeasLocationID = MeasLocationRotSpdID,
                            Name = "WCPT_RotSpeed",
                            Type = EnumEigenvalueName.Enum_SpdMean,
                        });
                        ctx.SaveChanges();
                    }
                }
        


            }
        }
        /// <summary>
        /// 添加的触发测量定义表
        /// </summary>
        public static void AddMdfTrigger(string TurbineID, string TriggerDefinitionName, string TriggerMeasDefName, bool IsAvailable)
        {
            CMSFramework.BusinessEntity.MeasDef_Supervise measDef_Supervise = new MeasDef_Supervise();

            List<MeasDefinition> mdfModels = null;
            //查询测量定义
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionName== TriggerMeasDefName).ToList();
            }
            int newRuleID = 1;
            using (CMSFramework.EF.SysContext ctxs = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence seq = ctxs.SysIDSequence.Find(EnumSequenceType.MeasDefinitionID);
                seq.LastNumber += 1;
                ctxs.SysIDSequence.Attach(seq);
                ctxs.Entry(seq).State = EntityState.Modified;
                ctxs.SaveChanges();
                newRuleID = seq.LastNumber;
            }
            //添加触发测量定义
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName)) {
              
                ctx.TriggerRuleDefs.Add(new MeasTriggerRuleDef()
                {

                    WindTurbineID = TurbineID,
                    RuleID = newRuleID.ToString(),
                    RuleName = TriggerDefinitionName,
                    MeasDefinitionID = mdfModels[0].MeasDefinitionID,
                    IsAvailable = IsAvailable,
                });  
                ctx.SaveChanges();
            }
       
        }
        /// <summary>
        /// 添加触发监视变量测量定义
        /// </summary>
        public static void AddMdfMdfTriggerSupervisedVariable(string TurbineID,string TriggerRuleName, string TriggerMeasDefName, string ConditionMonitoringLocIds,  string[] TriggerData)
        {
            CMSFramework.BusinessEntity.MeasDef_Supervise measDef_Supervise = new MeasDef_Supervise();

            List<MeasDefinition> mdfModels = null;
            //查询测量定义
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionName == TriggerMeasDefName).ToList();
            }
            List<MeasLoc_Process> allProcessLocList = null;
            List<MeasLoc_RotSpd> allRotSpdList = null;

            //添加触发监视变量测量定义

            //触发工况
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            //触发数据


            for (int i = 0; i < TriggerData.Length; i++)
            {
                List<string> triggerDataList = TriggerData[i].Split(',').ToList();
                EnumLogicCompareType CompareData;
                int a = 0;
                if (triggerDataList[1] == ">")
                {
                    a = 0;
                }
                else if (triggerDataList[1] == "<")
                {
                    a = 1;
                }
                else if (triggerDataList[1] == ">=")
                {
                    a = 2;
                }
                else if (triggerDataList[1] == "<=")
                {
                    a = 3;
                }
                else if (triggerDataList[1] == "=")
                {
                    a = 4;
                }
                else if (triggerDataList[1] == "!=")
                {
                    a = 5;
                }
                //测量位置id
                string triggerDataName = triggerDataList[0];
                //触发类型
               byte triggerTypeData =0;
/*                CMSFramework.BusinessEntity.EnumWorkCondition_ParamType enumWorkCondition_ParamType ;*/
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    allProcessLocList = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == TurbineID && item.MeasLocName == triggerDataName).ToList();

                }
                string mlid = "";
                if (allProcessLocList.Count == 0)
                {

                    using (CMSFramework.EF.DevContext ctxx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        allRotSpdList = ctxx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == TurbineID).ToList();

                    }
                    mlid = allRotSpdList[0].MeasLocationID;
                    triggerTypeData = 3;
                }
                else
                {
                    mlid = allProcessLocList[0].MeasLocationID;
                    //triggerTypeData = Convert.ToByte(allProcessLocList[0].Param_Type_Name);
                    triggerTypeData = Convert.ToByte(allProcessLocList[0].Param_Type_Code);
                }
                string mdfID = mdfModels[0].MeasDefinitionID;
                //查询触发定义id
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {

                    measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == TurbineID && item.RuleName == TriggerRuleName && item.MeasDefinitionID== mdfID).ToList();

                }
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    var VariableName = triggerDataList[0];
           
                    List<CMSFramework.BusinessEntity.MeasDef_Ev_Process> MeasDef_Ev_ProcessList = null;

                    MeasDef_Ev_ProcessList = ctx.ProcessEvConfs.Where(items => items.WindTurbineID == TurbineID && items.MeasDefinitionID == mdfID && items.MeasLocationID == mlid).ToList();

                    ctx.TriggerProcess.Add(new MeasTriggerProcess()
                    {

                        MeasLocationID = mlid,
                        RuleID = measTriggerDefsList[0].RuleID,
                        VariableName = MeasDef_Ev_ProcessList[0].Name,
                        WindTurbineID = TurbineID,
                        LogicCompare = (EnumLogicCompareType)a,
                        ThresholdStorageMode = 0,
                        Threshold = Convert.ToSingle(triggerDataList[2]),
                        ParamType = (EnumWorkCondition_ParamType)triggerTypeData,

                    });
                    ctx.SaveChanges();

                }

            }
        }

        /// <summary>
        /// 添加触发监视变量测量定义
        /// </summary>
        public static void AddMdfTriggeredExecuteMdf(string TurbineID, string ConditionMonitoringLocIds, string TriggerMeasDefName, string TriggerRuleName)
        {
            List<MeasDefinition> mdfModels = null;
            //查询测量定义
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == TurbineID && item.MeasDefinitionName == TriggerMeasDefName).ToList();
            }
            string mdfID = mdfModels[0].MeasDefinitionID;
            //触发工况
            List<MeasTriggerRuleDef> measTriggerDefsList = null;
            //查询规则id
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                measTriggerDefsList = ctx.TriggerRuleDefs.Where(item => item.WindTurbineID == TurbineID && item.RuleName == TriggerRuleName && item.MeasDefinitionID == mdfID).ToList();

            }
            //主控下的工况
            List<string> measDefinitionIdIdList = new List<string>();
            if (!string.IsNullOrEmpty(ConditionMonitoringLocIds))
            {
                measDefinitionIdIdList = ConditionMonitoringLocIds.Split(',').ToList();
            }
            foreach (string data in measDefinitionIdIdList)
            {
                string proId = data.Split('#')[0];
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.ExecuteMdfs.Add(new MeasTriggeredExecuteMdf()
                    {
                        WindTurbineID=TurbineID,
                        MeasDefinitionID=proId,
                        RuleID= measTriggerDefsList[0].RuleID,
                    });
                    ctx.SaveChanges();
                }
                }
        }
        /// <summary>
        /// 添加触发时间
        /// </summary>
        public static void AddMdfTriggeredTime(string ruleId, string timeinterval) {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.TriggerTimes.Add(new MeasTriggerTime()
                {
                    TimeInterval = Convert.ToInt32(timeinterval),
                    RuleID = ruleId,
                });
                ctx.SaveChanges();
            }
        }
        
        /// <summary>
        /// 获取需要添加的工况信息
        /// </summary>
        /// <param name="_measDef"></param>
        /// <param name="_measLocIdList"></param>
        /// <param name="_DAUmeasLocIdList"></param>
        /// <param name="_isAcqRotSpd"></param>
        /// <param name="_processLocList"></param>
        /// <param name="rotSpdLocList"></param>
        private static void GetMeasLocProcess(MeasDefinition _measDef, List<string> _measLocIdList, List<string> _DAUmeasLocIdList, bool _isAcqRotSpd, List<MeasLoc_Process> _processLocList, List<MeasLoc_RotSpd> rotSpdLocList)
        {
            List<MeasLoc_Process> _ALLprocessLocList = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                _ALLprocessLocList.AddRange(ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _measDef.WindTurbineID));
                if (_isAcqRotSpd && rotSpdLocList != null)
                {
                    rotSpdLocList.AddRange(ctx.DevMeasLocRotSpds.Where(item => item.WindTurbineID == _measDef.WindTurbineID));
                }
            }
            //主控下的工况
            foreach (string data in _measLocIdList)
            {
                string proId = data.Split('#')[0];
                var measDefProcess = _ALLprocessLocList.Find(item => item.MeasLocationID == proId);
                if (measDefProcess != null)
                {
                    _processLocList.Add(measDefProcess);
                }
            }
            //DAU下的工况
            foreach (string data in _DAUmeasLocIdList)
            {
                string proId = data.Split('#')[0];
                var measDefProcess = _ALLprocessLocList.Find(item => item.MeasLocationID == proId);
                if (measDefProcess != null)
                {
                    _processLocList.Add(measDefProcess);
                }
            }
         
        }

        /// <summary>
        /// 修改测量定义
        /// </summary>
        /// <param name="_measDef"></param>
        /// <param name="_measLocIdList"></param>
        /// <param name="_isAcqRotSpd"></param>
        public static string EditMeasdefinition(
            MeasDefinition _measDef,
            List<string> _measLocIdList,
            List<string> _DAUmeasLocIdList,
            bool _isAcqRotSpd, int RotWaveLineCounts,string dauid,int modelType)
        {
            List<MeasLoc_RotSpd> locList = DevTreeManagement.GetMDFRotSpdMeasLocListByTurId(_measDef.WindTurbineID);
            bool isNeedDelelRotSpdRTdata = false;
            WaveDef_RotSpd mdfWaveRotSpd = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MeasDefinitions.Attach(_measDef);
                ctx.Entry(_measDef).State = EntityState.Modified;

                MeasDefinition_Ex editMeasEX = new MeasDefinition_Ex()
                {
                    DaqInterval = _measDef.Mdf_Ex.DaqInterval,
                    DauID = _measDef.Mdf_Ex.DauID,
                    MeasDefinitionID = _measDef.MeasDefinitionID,
                    WindTurbineID = _measDef.WindTurbineID,
                    ModelType = (EnumMeasDefModelType)modelType,
                    DaqIntervalUnit = _measDef.Mdf_Ex.DaqIntervalUnit,
                };
                ctx.MeasDefinitions_Exs.Attach(editMeasEX);
                ctx.Entry(editMeasEX).State = EntityState.Modified;


                mdfWaveRotSpd = ctx.MDFWaveDefRotSpds.FirstOrDefault(item => item.WindTurbineID == _measDef.WindTurbineID
                    && item.MeasDefinitionID == _measDef.MeasDefinitionID);
                //处理转速波形定义
                if (_isAcqRotSpd)
                {
                    //之前无转速，现在需要添加
                    if (mdfWaveRotSpd == null && locList.Count > 0)
                    {
                        mdfWaveRotSpd = new WaveDef_RotSpd()
                        {
                            WindTurbineID = _measDef.WindTurbineID,
                            MeasDefinitionID = _measDef.MeasDefinitionID,
                            MeasLoc_RotSpdID = locList[0].MeasLocationID,
                            LineCounts = RotWaveLineCounts
                        };
                        ctx.MDFWaveDefRotSpds.Add(mdfWaveRotSpd);
                    }
                    else
                    {
                        if (mdfWaveRotSpd != null)
                        {
                            mdfWaveRotSpd.LineCounts = RotWaveLineCounts;
                            ctx.MDFWaveDefRotSpds.Attach(mdfWaveRotSpd);
                            ctx.Entry(mdfWaveRotSpd).State = EntityState.Modified;
                        }
                    }
                }
                else
                {
                    if (mdfWaveRotSpd != null)
                    {
                        ctx.MDFWaveDefRotSpds.Remove(mdfWaveRotSpd);
                        isNeedDelelRotSpdRTdata = true;
                    }
                }
                ctx.SaveChanges();
            }
            List<MeasLoc_Process> _proMeasLocList = new List<MeasLoc_Process>();
            GetMeasLocProcess(_measDef, _measLocIdList, _DAUmeasLocIdList, _isAcqRotSpd, _proMeasLocList, null);
            GetNewWordProcessLocList(_measDef, _proMeasLocList, _DAUmeasLocIdList, _measLocIdList);
            if (_proMeasLocList != null)
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    //处理工况波形定义
                    ctx.MDFWorkConditions.RemoveRange(ctx.MDFWorkConditions.Where(item => item.WindTurbineID == _measDef.WindTurbineID && item.MeasDefinitionID == _measDef.MeasDefinitionID));
                    //添加新的工况
                    ctx.MDFWorkConditions.AddRange(_measDef.ProcessDefList);
                    ctx.SaveChanges();
                }
            }
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                if (isNeedDelelRotSpdRTdata)
                {
                    ctx.WorkingConditionDatas.RemoveRange(ctx.WorkingConditionDatas.Where(item => item.MeasLocationID == mdfWaveRotSpd.MeasLoc_RotSpdID && item.MeasDefinitionID == mdfWaveRotSpd.MeasDefinitionID));
                    ctx.SaveChanges();
                }
                if (_proMeasLocList != null)
                {
                    //由于转速和工况合并，所以删除前工况，不能影响转速数据
                    //转速和其他工况独立处理
                    ctx.WorkingConditionDatas.RemoveRange(ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _measDef.WindTurbineID && item.MeasDefinitionID == _measDef.MeasDefinitionID && item.Param_Type_Code != EnumWorkCondition_ParamType.WCPT_RotSpeed));
                    ctx.EVData_Vibs.RemoveRange(ctx.EVData_Vibs.Where(item => item.WindTurbineID == _measDef.WindTurbineID && item.MeasDefinitionID == _measDef.MeasDefinitionID));
                    ctx.EVData_SVMs.RemoveRange(ctx.EVData_SVMs.Where(item => item.WindTurbineID == _measDef.WindTurbineID && item.MeasDefinitionID == _measDef.MeasDefinitionID));
                    ctx.SaveChanges();
                }
            }
            // 提升DAU测量定义版本
            DauManagement.UpdateMeasDefVersion(_measDef.WindTurbineID.ToString(), dauid);
            return "";
        }




        /// <summary>
        /// 获取测量定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefId"></param>
        /// <returns></returns>
        public static MeasDefinition GetMeasdefinition(string _turID, string _measDefId)
        {
            MeasDefinition mdfModel = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModel = ctx.MeasDefinitions.Find(_turID, _measDefId);
            }
            return mdfModel;
        }



        public static MeasDefinition GetMeasdefinitionByName(string _turID, string _measDefName)
        {
            MeasDefinition mdfModel = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModel = ctx.MeasDefinitions.FirstOrDefault(t=>t.WindTurbineID == _turID && t.MeasDefinitionName == _measDefName);
            }
            return mdfModel;
        }
        /// <summary>
        /// 获取测量定义下工况测量定义列表
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefId"></param>
        /// <returns></returns>
        public static List<MeasDef_Process> GetMDFWorkCondLocListByMeasDefId(string _turID, string _measDefId)
        {
            List<MeasDef_Process> wdfWorkConValues = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfWorkConValues = ctx.MDFWorkConditions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId).ToList();
            }
            return wdfWorkConValues;
        }
        /// <summary>
        /// 获取机组下的工况测量定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static List<MeasDef_Process> GetMDFWorkCondLocListByTurbineId(string _turID)
        {
            List<MeasDef_Process> wdfWorkConValues = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfWorkConValues = ctx.MDFWorkConditions.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return wdfWorkConValues;
        }

        public static List<MeasLoc_Process> GetMeasLocWorkCondLocListByMeasDefId(string _turID, string _measDefId)
        {
            List<MeasDef_Process> wdfWorkConValues = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfWorkConValues = ctx.MDFWorkConditions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId).ToList();
            }
            if (wdfWorkConValues == null || wdfWorkConValues.Count == 0)
                return new List<MeasLoc_Process>();
            List<MeasLoc_Process> MeasLoc = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                MeasLoc = ctx.DevMeasLocProcesses.Where(item => item.WindTurbineID == _turID).ToList();
                /*MeasLoc.ForEach(item =>
                {
                    var workMdf = wdfWorkConValues.Find(mdf => mdf.MeasLocationID == item.MeasLocationID);
                    if (workMdf == null)
                    {
                        MeasLoc.Remove(item);
                    }
                });*/
            }
            MeasLoc = MeasLoc.Where(item => wdfWorkConValues.Select(m => m.MeasLocationID).Contains(item.MeasLocationID)).ToList();
            return MeasLoc;
        }

        public static bool GetWorkCondMeasLocUsedByMeasLocId(string _turID, string measLocId)
        {
            MeasDef_Process process = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                process = ctx.MDFWorkConditions.FirstOrDefault(item => item.WindTurbineID == _turID && item.MeasLocationID == measLocId);
            }
            return process != null;
        }
        /// <summary>
        /// 获取机组测量定义扩展
        /// </summary>
        /// <param name="windTurbineIDOld"></param>
        /// <returns></returns>
        public static List<MeasDefinition_Ex> GetMeasdefinitionEXListByTurID(string windTurbineIDOld)
        {
            List<MeasDefinition_Ex> measDefEx = new List<MeasDefinition_Ex>();
            try
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    measDefEx = ctx.MeasDefinitions_Exs.Where(p => p.WindTurbineID == windTurbineIDOld).ToList();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"GetMeasdefinitionEXListByTurID :{windTurbineIDOld}", ex);
            }
            return measDefEx;
        }

        /// <summary>
        /// (振动)测量位置下波形定义列表
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measLocID"></param>
        /// <returns></returns>
        public static List<WaveDefinition> GetWaveDefByMeasLoc(string _measLocID)
        {
            List<WaveDefinition> list = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                list = ctx.MDFWaveDefinitions.Where(item => item.MeasLocationID == _measLocID).ToList();
            }
            return list;
        }

        /// <summary>
        /// 检测是否有振动波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefID"></param>
        /// <param name="_measLocID"></param>
        /// <param name="_enumMeasDefType"></param>
        /// <returns></returns>
        public static bool IsExsitsWaveDef(string _turID, string _measDefID, string _measLocID, EnumWaveFormType _enumMeasDefType)
        {
            return false;
            // 支持一个通道多种测量定义，
            // return false;
            //删除通道号验证
            //温度模块是单独的，所以通道号和振动的有重复
            //WaveDefinition wdf = null;
            //using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            //{
            //   wdf = ctx.MDFWaveDefinitions.FirstOrDefault(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefID && item.MeasLocationID == _measLocID && item.WaveFormType == _enumMeasDefType);
            //}
            //return wdf != null;
        }

        /// <summary>
        /// 添加时域波形定义
        /// </summary>
        /// <param name="_entity"></param>
        public static void AddWaveDef_Time(WaveDef_Time _entity)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
                SequenceEntity.LastNumber += 1;
                ctx.SysIDSequence.Attach(SequenceEntity);
                ctx.Entry(SequenceEntity).State = EntityState.Modified;
                ctx.SaveChanges();
                _entity.WaveDefinitionID = SequenceEntity.LastNumber.ToString();
            }
            EditWaveDef_Time(_entity, EntityState.Added);
        }

        public static void AddWaveDef_VoltageCurrent(WaveDef_Time _entity)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
                SequenceEntity.LastNumber += 1;
                ctx.SysIDSequence.Attach(SequenceEntity);
                ctx.Entry(SequenceEntity).State = EntityState.Modified;
                ctx.SaveChanges();
                _entity.WaveDefinitionID = SequenceEntity.LastNumber.ToString();
            }
            EditWaveDef_VoltageCurrent(_entity, EntityState.Added);
        }

        public static void AddMeaDefitionEX(List<MeasDefinition_Ex> measDefEXresult)
        {
            try
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ctx.MeasDefinitions_Exs.AddRange(measDefEXresult);
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("AddMeaDefitionEX", ex);
            }
        }

        /// <summary>
        /// 修改时域波形定义
        /// </summary>
        /// <param name="_measDef_Time"></param>
        public static void EditWaveDef_Time(WaveDef_Time _entity)
        {
            EditWaveDef_Time(_entity, EntityState.Modified);
        }

        private static void EditWaveDef_Time(WaveDef_Time _entity, EntityState eitityState)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefParam_Time ParamTime = ctx.WDFParamTimes.FirstOrDefault(item => item.LowerLimitFreqency == _entity.LowerLimitFreqency && item.UpperLimitFreqency == _entity.UpperLimitFreqency && item.SampleLength == _entity.SampleLength);
                if (ParamTime == null)
                {
                    var ParameList = ctx.WDFParamTimes.ToList();
                    int id = 1;
                    if (ParameList.Count > 0)
                    {
                        ParameList.ForEach(item =>
                        {
                            if (int.Parse(item.WaveDefParamID) > id)
                            {
                                id = int.Parse(item.WaveDefParamID);
                            }
                        });
                    }
                    ParamTime = new WaveDefParam_Time()
                    {
                        WaveDefParamID = (id + 1).ToString(),
                        WaveDefParamName = _entity.WaveDefinitionName,
                        LowerLimitFreqency = _entity.LowerLimitFreqency,
                        UpperLimitFreqency = _entity.UpperLimitFreqency,
                        SampleLength = _entity.SampleLength,
                    };
                    ctx.WDFParamTimes.Add(ParamTime);
                    ctx.SaveChanges();
                }
                WaveDefinition timeWaveDef = new WaveDefinition()
                {
                    WindTurbineID = _entity.WindTurbineID,
                    MeasDefinitionID = _entity.MeasDefinitionID,
                    MeasLocationID = _entity.MeasLocationID,
                    WaveDefinitionID = _entity.WaveDefinitionID,
                    WaveDefinitionName = _entity.WaveDefinitionName,
                    WaveDefParamID = ParamTime.WaveDefParamID,
                    WaveFormType = _entity.WaveFormType,
                    SignalType = _entity.SignalType,
                    //XmlEigenValueDef = _entity.XmlEigenValueDef,
                };
                ctx.MDFWaveDefinitions.Attach(timeWaveDef);
                ctx.Entry(timeWaveDef).State = eitityState;
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_entity.WindTurbineID);
        }

        /// <summary>
        /// 修改电流电压波形定义
        /// </summary>
        /// <param name="_entity"></param>
        public static void EditWaveDef_VoltageCurrent(WaveDef_Time _entity)
        {
            EditWaveDef_VoltageCurrent(_entity, EntityState.Modified);
        }

        private static void EditWaveDef_VoltageCurrent(WaveDef_Time _entity, EntityState eitityState)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefParam_Time ParamTime = ctx.WDFParamTimes.FirstOrDefault(item => item.LowerLimitFreqency == _entity.LowerLimitFreqency && item.UpperLimitFreqency == _entity.UpperLimitFreqency && item.SampleLength == _entity.SampleLength);
                if (ParamTime == null)
                {
                    var ParameList = ctx.WDFParamTimes.ToList();
                    int id = 1;
                    if (ParameList.Count > 0)
                    {
                        ParameList.ForEach(item =>
                        {
                            if (int.Parse(item.WaveDefParamID) > id)
                            {
                                id = int.Parse(item.WaveDefParamID);
                            }
                        });
                    }
                    ParamTime = new WaveDefParam_Time()
                    {
                        WaveDefParamID = (id + 1).ToString(),
                        WaveDefParamName = _entity.WaveDefinitionName,
                        LowerLimitFreqency = _entity.LowerLimitFreqency,
                        UpperLimitFreqency = _entity.UpperLimitFreqency,
                        SampleLength = _entity.SampleLength
                    };
                    ctx.WDFParamTimes.Add(ParamTime);
                    ctx.SaveChanges();
                }
                WaveDef_VoltageCurrent timeWaveDef = new WaveDef_VoltageCurrent()
                {
                    WindTurbineID = _entity.WindTurbineID,
                    MeasDefinitionID = _entity.MeasDefinitionID,
                    MeasLocationID = _entity.MeasLocationID,
                    WaveDefinitionID = _entity.WaveDefinitionID,
                    WaveDefinitionName = _entity.WaveDefinitionName,
                    WaveDefParamID = ParamTime.WaveDefParamID,
                    WaveFormType = _entity.WaveFormType,
                    SignalType = _entity.SignalType,
                    //XmlEigenValueDef = _entity.XmlEigenValueDef,
                };
                ctx.MdfWaveDefVoltageCurrents.Attach(timeWaveDef);
                ctx.Entry(timeWaveDef).State = eitityState;
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_entity.WindTurbineID);
        }

        /// <summary>
        /// 删除时域波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDef_TimeId"></param>
        public static void DeleteWaveDef(string _turID, string _wdfTimeId, string _measDefId)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MDFWaveDefinitions.RemoveRange(ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId && item.WaveDefinitionID == _wdfTimeId));
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_turID);
        }
        /// <summary>
        /// 删除电流电压波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_wdfTimeId"></param>
        /// <param name="_measDefId"></param>
        public static void DeleteWaveDefVoltageCurrent(string _turID, string _wdfTimeId, string _measDefId)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.MdfWaveDefVoltageCurrents.RemoveRange(ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId && item.WaveDefinitionID == _wdfTimeId));
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_turID);
        }


        /// <summary>
        /// 获取时域波形定义列表
        /// </summary>
        /// <param name="_mdfId"></param> 
        public static List<WaveDef_Time> GetWaveDefByMdfId_Time(string turID, string _mdfId)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.Where(item =>item.WindTurbineID == turID && item.MeasDefinitionID == _mdfId && item.WaveFormType == EnumWaveFormType.WDF_Time).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        /// <summary>
        /// 获取电流电压波形定义列表
        /// </summary>
        /// <param name="turID"></param>
        /// <param name="_mdfId"></param>
        /// <returns></returns>
        public static List<WaveDef_Time> GetWaveDefByMdfId_VoltageCurrent(string turID, string _mdfId)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDef_VoltageCurrent> WaveDefList = ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == turID && item.MeasDefinitionID == _mdfId && item.WaveFormType == EnumWaveFormType.WDF_Time).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        /// <summary>
        /// 获取波形定义列表
        /// </summary>
        /// <param name="_mdfId"></param> 
        public static List<WaveDef_Time> GetWaveDefByMdfIdAMS_Time(string turID, string _mdfId, EnumWaveFormType WDF_WaveType)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.MeasDefinitionID == _mdfId && item.WaveFormType == WDF_WaveType).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        /// <summary>
        /// 根据机组获取时域波形定义
        /// </summary>
        /// <param name="turID"></param>
        /// <returns></returns>
        public static List<WaveDef_Time> GetWaveDefByTurId_Time(string turID)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                //List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == turID && item.WaveFormType == EnumWaveFormType.WDF_Time).OrderBy(t=>int.Parse(t.WaveDefinitionID)).ToList();
                List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == turID && item.WaveFormType == EnumWaveFormType.WDF_Time).ToList().OrderBy(t=>int.Parse(t.WaveDefinitionID)).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        public static List<WaveDef_Time> GetWaveDefByTurId_VoltageCurrent(string turID)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDef_VoltageCurrent> WaveDefList = ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == turID && item.WaveFormType == EnumWaveFormType.WDF_Time).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        public static List<WaveDef_Time> GetWaveDefByTurId_AMSTime(string turID)
        {
            List<WaveDef_Time> TimeWaveDefList = new List<WaveDef_Time>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.AsNoTracking().Where(item => item.WindTurbineID == turID).ToList();
                List<WaveDefParam_Time> paramTime = ctx.WDFParamTimes.AsNoTracking().ToList();
                WaveDefList.ForEach(item =>
                {
                    TimeWaveDefList.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return TimeWaveDefList;
        }

        public static List<WaveDefinition> GetWaveDefByTurId(string turID)
        {
            List<WaveDefinition> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == turID).ToList();
            }
            return WaveDefList;
        }


        public static List<WaveDef_VoltageCurrent> GetWaveDefVoltageCurrentByTurId(string turID)
        {
            List<WaveDef_VoltageCurrent> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == turID).ToList();
            }
            return WaveDefList;
        }

        /// <summary>
        /// 获取波形定义实体
        /// </summary>
        /// <param name="_wdfId"></param> 
        public static WaveDef_Time GetWaveDefById_Time(string turID, string _wdfId)
        {
            WaveDef_Time waveDef = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefinition wavedef = ctx.MDFWaveDefinitions.FirstOrDefault(item => item.WindTurbineID == turID && item.WaveDefinitionID == _wdfId);
                if (wavedef != null)
                {
                    var timeDefParamID = ctx.WDFParamTimes.Find(wavedef.WaveDefParamID);
                    waveDef = wavedef.ConvertTo(timeDefParamID);
                }
            }
            return waveDef;
        }

        /// <summary>
        /// 获取电流电压波形
        /// </summary>
        /// <param name="turID"></param>
        /// <param name="_wdfId"></param>
        /// <returns></returns>
        public static WaveDef_Time GetWaveDefById_VoltageCurrent(string turID, string _wdfId)
        {
            WaveDef_Time waveDef = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDef_VoltageCurrent wavedef = ctx.MdfWaveDefVoltageCurrents.FirstOrDefault(item => item.WindTurbineID == turID && item.WaveDefinitionID == _wdfId);
                if (wavedef != null)
                {
                    var timeDefParamID = ctx.WDFParamTimes.Find(wavedef.WaveDefParamID);
                    waveDef = wavedef.ConvertTo(timeDefParamID);
                }
            }
            return waveDef;
        }

        /// <summary>
        /// 添加阶次包络波形定义
        /// </summary>
        /// <param name="_wdfEnv"></param>
        public static void AddWaveDef_Envelope(WaveDef_Envlope _entity)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
                SequenceEntity.LastNumber += 1;
                ctx.SysIDSequence.Attach(SequenceEntity);
                ctx.Entry(SequenceEntity).State = EntityState.Modified;
                ctx.SaveChanges();
                _entity.WaveDefinitionID = SequenceEntity.LastNumber.ToString();
            }
            EditWaveDef_Envlope(_entity, EntityState.Added);
        }

        private static void EditWaveDef_Envlope(WaveDef_Envlope _entity, EntityState entityState)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefParam_Envlope ParamEnvlope = ctx.WDFParamEnvlopes.FirstOrDefault(item => item.EnvBandWidth == _entity.EnvBandWidth && item.EnvFiterFreq == _entity.EnvFiterFreq && item.SampleLength == _entity.SampleLength);
                if (ParamEnvlope == null)
                {
                    var ParameList = ctx.WDFParamEnvlopes.ToList();
                    int id = 1;
                    if (ParameList.Count > 0)
                    {
                        ParameList.ForEach(item =>
                        {
                            if (int.Parse(item.WaveDefParamID) > id)
                            {
                                id = int.Parse(item.WaveDefParamID);
                            }
                        });
                    }
                    ParamEnvlope = new WaveDefParam_Envlope()
                    {
                        WaveDefParamID = (id + 1).ToString(),
                        WaveDefParamName = _entity.WaveDefinitionName,
                        EnvBandWidth = _entity.EnvBandWidth,
                        EnvFiterFreq = _entity.EnvFiterFreq,
                        SampleLength = _entity.SampleLength
                    };
                    ctx.WDFParamEnvlopes.Add(ParamEnvlope);
                    ctx.SaveChanges();
                }
                WaveDefinition EnvlopeWaveDef = new WaveDefinition()
                {
                    WindTurbineID = _entity.WindTurbineID,
                    MeasDefinitionID = _entity.MeasDefinitionID,
                    MeasLocationID = _entity.MeasLocationID,
                    WaveDefinitionID = _entity.WaveDefinitionID,
                    WaveDefinitionName = _entity.WaveDefinitionName,
                    WaveDefParamID = ParamEnvlope.WaveDefParamID,
                    WaveFormType = _entity.WaveFormType
                };
                ctx.MDFWaveDefinitions.Attach(EnvlopeWaveDef);
                ctx.Entry(EnvlopeWaveDef).State = entityState;
                ctx.SaveChanges();
            }
            DauManagement.UpdateMeasDefVersion(_entity.WindTurbineID);
        }

        /// <summary>
        /// 修改阶次包络波形定义
        /// </summary>
        /// <param name="_measDef_OrderEnv"></param>
        public static void EditWaveDef_Envlope(WaveDef_Envlope _entity)
        {
            EditWaveDef_Envlope(_entity, EntityState.Modified);
        }

        /// <summary>
        /// 获取高频包络波形定义列表
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_mdfId"></param>
        /// <returns></returns>
        public static List<WaveDef_Envlope> GetWaveDefByTruId_Envlope(string _turID)
        {
            List<WaveDef_Envlope> list = new List<WaveDef_Envlope>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDefinition> WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.WaveFormType == EnumWaveFormType.WDF_Envelope).ToList();
                List<WaveDefParam_Envlope> paramTime = ctx.WDFParamEnvlopes.ToList();
                WaveDefList.ForEach(item =>
                {
                    list.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return list;
        }

        /// <summary>
        /// 获取波形定义实体
        /// </summary>
        /// <param name="_defId"></param>
        public static WaveDef_Envlope GetWaveDefById_Envlope(string _turID, string _wdfId)
        {
            WaveDef_Envlope waveDefEnvlope = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefinition waveDef = ctx.MDFWaveDefinitions.FirstOrDefault(item => item.WindTurbineID == _turID && item.WaveDefinitionID == _wdfId && item.WaveFormType == EnumWaveFormType.WDF_Envelope);
                var ParameEnvlope = ctx.WDFParamEnvlopes.Find(waveDef.WaveDefParamID);
                waveDefEnvlope = waveDef.ConvertTo(ParameEnvlope);
            }
            return waveDefEnvlope;
        }

        public static List<WaveDef_Envlope> GetWaveDefByMeasdId_Envlope(string _turID, string measd)
        {
            List<WaveDef_Envlope> waveDefEnvlope = new();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<WaveDefinition> waveDef = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == measd && item.WaveFormType == EnumWaveFormType.WDF_Envelope).ToList();
                List<WaveDefParam_Envlope> paramTime = ctx.WDFParamEnvlopes.ToList();
                waveDef.ForEach(item =>
                {
                    waveDefEnvlope.Add(item.ConvertTo(paramTime.Find(param => param.WaveDefParamID == item.WaveDefParamID)));
                });
            }
            return waveDefEnvlope;
        }
        /// <summary>
        /// 获取转速波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measDefId"></param>
        /// <returns></returns>
        public static List<WaveDef_RotSpd> GetWaveDefListRotSpd(string _turID, string _measDefId)
        {
            List<WaveDef_RotSpd> wdfList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfList = ctx.MDFWaveDefRotSpds.Where(item => item.WindTurbineID == _turID && item.MeasDefinitionID == _measDefId).ToList();
            }
            return wdfList;
        }
        /// <summary>
        /// 获取机组下的转速波形定义
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static List<WaveDef_RotSpd> GetWaveDefListRotSpdByTrubineID(string _turID)
        {
            List<WaveDef_RotSpd> wdfList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                wdfList = ctx.MDFWaveDefRotSpds.Where(item => item.WindTurbineID == _turID && item.WindTurbineID == _turID).ToList();
            }
            return wdfList;
        }

        /// <summary>
        /// 获取测量定义等的主键ID
        /// </summary>
        /// <param name="enumSequenceType"></param>
        /// <returns></returns>
        public static int GetNewMeaID(EnumSequenceType enumSequenceType)
        {
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence seq = ctx.SysIDSequence.Find(enumSequenceType);
                seq.LastNumber += 1;
                ctx.SysIDSequence.Attach(seq);
                ctx.Entry(seq).State = EntityState.Modified;
                ctx.SaveChanges();
                return seq.LastNumber;
            }
        }

        public static List<MeasDefinition> GetMeasDefListByTurIdOverload(string _turbineId)
        {
            List<MeasDefinition> mdfModels = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                mdfModels = ctx.MeasDefinitions.Where(item => item.WindTurbineID == _turbineId).ToList().OrderBy(t=>int.Parse(t.MeasDefinitionID)).ToList();
                if (mdfModels != null)
                {
                    mdfModels.ForEach(item =>
                    {
                        item.ProcessDefList = GetMDFWorkCondLocListByMeasDefId(item.WindTurbineID, item.MeasDefinitionID);
                        item.RotSpdWaveDefList = GetWaveDefListRotSpd(item.WindTurbineID, item.MeasDefinitionID);
                        item.SVMWaveDefinition = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(item.WindTurbineID, item.MeasDefinitionID);
                        item.WaveDefList = GetWaveDefByTurId(item.WindTurbineID, item.MeasDefinitionID);

                        item.WaveDefVoltageCurrentList = GetVoltageCurrentWaveDefByTurId(item.WindTurbineID, item.MeasDefinitionID);

                        // 特征值
                        item.VibEigenValueConf = EigenValueManage.GetMdfTimeDomainEvConf(item.WindTurbineID, item.MeasDefinitionID);
                        item.ProcessSuperviseDefList = EigenValueManage.GetEigenValueProcess(item.WindTurbineID, item.MeasDefinitionID);

                        item.ModbusWaveDefList = MeasDefinitionManagement.GetModbusWaveDefByMeasdId(item.WindTurbineID, item.MeasDefinitionID);

                        // 触发采集
                        item.TriggerRules = TriggerManager.GetMeasTriggerRuleDefs(item.WindTurbineID, item.MeasDefinitionID);
                    });
                }
            }
            return mdfModels;
        }

        public static void AddMeaDefition(List<MeasDefinition> meaDefinitionList)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                // 添加测量定义
                ctx.MeasDefinitions.AddRange(meaDefinitionList);
                ctx.SaveChanges();
                meaDefinitionList.ForEach(item =>
                {
                    ctx.MDFWaveDefRotSpds.AddRange(item.RotSpdWaveDefList);
                    ctx.MDFWorkConditions.AddRange(item.ProcessDefList);
                    ctx.MDFWaveDefinitions.AddRange(item.WaveDefList);

                    ctx.MdfWaveDefVoltageCurrents.AddRange(item.WaveDefVoltageCurrentList);
                    /*ctx.SVMWaveDefinitions.AddRange(item.SVMWaveDefinition);*/
                    ctx.MeasSolutions.AddRange(item.SolutionList);
                    //res.AddRange(item.SolutionList);

                    //触发采集
                    if (item.TriggerRules != null  && item.TriggerRules.Count > 0)
                    {
                        ctx.TriggerRuleDefs.AddRange(item.TriggerRules);
                        if (item.TriggerRules.Count > 0)
                        {
                            foreach(var k in item.TriggerRules)
                            {
                                ctx.TriggerProcess.AddRange(k.SupervisedVariables);
                                
                                ctx.ExecuteMdfs.AddRange(k.ExecuteMdfs);

                                if(k.TriggerTime != null)
                                {
                                    ctx.TriggerTimes.Add(k.TriggerTime);
                                }
                            }
                        }
                    }

                    //特征值
                    if(item.ProcessSuperviseDefList != null && item.ProcessSuperviseDefList.Count > 0)
                    {
                        foreach (var t in item.ProcessSuperviseDefList)
                        {
                            t.EvId = 0; // 清空 evid 的值
                        }
                        ctx.ProcessEvConfs.AddRange(item.ProcessSuperviseDefList);

                        
                    }

                    if(item.VibEigenValueConf!=null&& item.VibEigenValueConf.Count>0)
                    {
                        foreach (var t in item.VibEigenValueConf)
                        {
                            t.EvId = 0; // 清空 evid 的值
                        }
                        ctx.TimeDomainEvConfs.AddRange(item.VibEigenValueConf);
                    }
                    //ctx.ProcessEvConfs.AddRange(item.ProcessSuperviseDefList);
                    //ctx.TimeDomainEvConfs.AddRange(item.VibEigenValueConf);
                    ctx.SaveChanges();
                });
                ctx.SaveChanges();
            }
        }

        public static List<WaveDefinition> GetWaveDefByTurId(string turID, string _wdfId)
        {
            List<WaveDefinition> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.MDFWaveDefinitions.Where(item => item.WindTurbineID == turID
                    && item.MeasDefinitionID == _wdfId).ToList().OrderBy(t=>int.Parse(t.WaveDefinitionID)).ToList();
            }
            return WaveDefList;
        }

        public static List<WaveDef_Modbus> GetModbusWaveDefByTurId(string turID, int deviceDI,string measlocID)
        {
            List<WaveDef_Modbus> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.WDFModbusDefs.Where(item => item.WindTurbineID == turID
                    && item.ModbusDeviceID == deviceDI && item.MeasLocationID == measlocID).ToList().OrderBy(t => int.Parse(t.WaveDefinitionID)).ToList();
            }
            return WaveDefList;
        }

        public static List<WaveDef_Modbus> GetModbusWaveDefByMeasdId(string turID, string measdid)
        {
            List<WaveDef_Modbus> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.WDFModbusDefs.Where(item => item.WindTurbineID == turID
                    && item.MeasDefinitionID == measdid ).ToList().OrderBy(t => int.Parse(t.WaveDefinitionID)).ToList();
            }
            return WaveDefList;
        }

        public static WaveDef_Modbus GetModbusWaveDefByWaveID(string turID, string waveID)
        {
            WaveDef_Modbus WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.WDFModbusDefs.FirstOrDefault(item => item.WindTurbineID == turID
                    && item.WaveDefinitionID == waveID);
            }
            return WaveDefList;
        }

        public static List<WaveDef_VoltageCurrent> GetVoltageCurrentWaveDefByTurId(string turID, string _wdfId)
        {
            List<WaveDef_VoltageCurrent> WaveDefList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                WaveDefList = ctx.MdfWaveDefVoltageCurrents.Where(item => item.WindTurbineID == turID
                    && item.MeasDefinitionID == _wdfId).ToList().OrderBy(t => int.Parse(t.WaveDefinitionID)).ToList();
            }
            return WaveDefList;
        }


        public static List<MeasSolution> GetMeasSolution(string WindTurbineID)
        {
            List<MeasSolution> res = new List<MeasSolution>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.MeasSolutions.Where(t => t.WindTurbineID == WindTurbineID).ToList();
            }
            return res;
        }

        /// <summary>
        /// 批量删除测量方案（原子性操作）
        /// </summary>
        /// <param name="windTurbineID">机组ID</param>
        /// <param name="measSolutionIDs">测量方案ID列表</param>
        public static void BatchDeleteMeasSolutions(string windTurbineID, List<int> measSolutionIDs)
        {
            if (measSolutionIDs == null || measSolutionIDs.Count == 0)
                return;

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var solutions = ctx.MeasSolutions.Where(t =>
                            t.WindTurbineID == windTurbineID &&
                            measSolutionIDs.Contains(t.MeasSolutionID)).ToList();

                        if (solutions.Any())
                        {
                            ctx.MeasSolutions.RemoveRange(solutions);
                        }

                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量添加测量方案（原子性操作）
        /// </summary>
        /// <param name="solutions">测量方案列表</param>
        public static void BatchAddMeasSolutions(List<MeasSolution> solutions)
        {
            if (solutions == null || solutions.Count == 0)
                return;

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        ctx.MeasSolutions.AddRange(solutions);
                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 编辑单个测量方案（原子性操作）
        /// </summary>
        /// <param name="windTurbineID">机组ID</param>
        /// <param name="measSolutionID">测量方案ID</param>
        /// <param name="solutions">新的测量方案列表</param>
        public static void EditMeasSolution(string windTurbineID, int measSolutionID, List<MeasSolution> solutions)
        {
            if (solutions == null || solutions.Count == 0)
                return;

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // 删除原有的测量方案
                        var existingSolutions = ctx.MeasSolutions.Where(t =>
                            t.MeasSolutionID == measSolutionID &&
                            t.WindTurbineID == windTurbineID).ToList();

                        if (existingSolutions.Any())
                        {
                            ctx.MeasSolutions.RemoveRange(existingSolutions);
                        }

                        // 添加新的测量方案
                        ctx.MeasSolutions.AddRange(solutions);
                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 获取下一个测量方案ID
        /// </summary>
        /// <returns></returns>
        public static int GetNextMeasSolutionID()
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                if (ctx.MeasSolutions.Count() > 0)
                {
                    return ctx.MeasSolutions.Max(t => t.MeasSolutionID) + 1;
                }
                else
                {
                    return 1;
                }
            }
        }
    }
}
