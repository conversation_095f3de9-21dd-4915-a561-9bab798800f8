﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 机组运行状态监测
    /// </summary>
    public class MonitorManager
    {
        /// <summary>
        /// 机组运行日志
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BaseTableModel GetTurbineRunLogTable(string turbineID)
        {
            DateTime endTime = DateTime.Now.AddDays(1);
            DateTime beginTime = DateTime.Now.AddDays(-31);
            List<WindTurbineRunLog> logList = LogManagement.GetTurbineRunLogByTurID(turbineID,beginTime,endTime);
            BaseTableModel tableModel = CreateTurbineRunLogTable(logList);
            return tableModel;
        }

        public BaseTableModel GetTurbineRunLogByTime(string turbineID, DateTime beginTime, DateTime endTime)
        {
            List<WindTurbineRunLog> logList = LogManagement.GetTurbineRunLogByTurID(turbineID,beginTime,endTime);
            BaseTableModel tableModel = CreateTurbineRunLogTable(logList);
            return tableModel;
        }

        public static List<WindTurbineRunLog> GetTurbineRunLogByTimeV2(string turbineID, DateTime beginTime, DateTime endTime)
        {
            List<WindTurbineRunLog> logList = LogManagement.GetTurbineRunLogByTurID(turbineID, beginTime, endTime);
            return logList;
        }

        private BaseTableModel CreateTurbineRunLogTable(List<WindTurbineRunLog> logList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "MonitorLog";
            logList.Reverse();//数据反转
            List<Rows> rowList = new List<Rows>();
            //int logCount = 0;
            //if (logList.Count > 1000)
            //{
            //    logCount = 1000;
            //}
            //else {
            //    logCount = logList.Count;
            //}
            for (int i = 0; i < logList.Count; i++)
            {
                Rows rows = new Rows();
                rows.cells = CreateTurbineRunLogTableCell(logList[i]);
                rowList.Add(rows);
            }
            tableModel.rows = rowList.ToArray();
            return tableModel;
        }

        private Cell[] CreateTurbineRunLogTableCell(WindTurbineRunLog log)
        {
            List<Cell> cellList = new List<Cell>();
            //机组ID
            //Cell cell1 = new Cell();
            //cell1.displayValue = log.WindTurbineID.ToString();
            //cellList.Add(cell1);
            //状态
            Cell cell2 = new Cell();
            cell2.displayValue = AppFramework.Utility.EnumHelper.GetDescription(log.AlarmDegree);
            cellList.Add(cell2);
            //详细
            Cell cell3 = new Cell();
            cell3.displayValue = log.LogTitle;
            cellList.Add(cell3);
            //时间
            Cell cell4 = new Cell();
            cell4.displayValue = log.EventTime.ToString("yyyy-MM-dd HH:mm:ss");
            cellList.Add(cell4);

            return cellList.ToArray();
        }

        public string GetTurbineMonitorCount(string windParkId, string turbineID)
        {
            WindTurbineRunLog runLog = LogManagement.GetTurbineOneRunLogByTurID(turbineID);
            //无最新数据时。
            if (runLog == null)
            {
                return string.Empty;
            }
            string runStutus = Convert.ToInt32(runLog.AlarmDegree).ToString() //AlarmType.GetAlarmTypeByDegree((int)runLog.AlarmDegree).AlarmTypeName 
                + "|" + runLog.LogTitle;
            return runStutus;
        }
    }
}