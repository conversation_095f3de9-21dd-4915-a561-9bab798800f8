﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.4" />
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AppFrameWork.Utility">
      <HintPath>..\..\AppNetCore\AppFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DAUEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUFacadeBase">
      <HintPath>..\..\AppNetCore\CMSFramework.DAUFacadeBase.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DevTreeEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DevTreeEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Logger">
      <HintPath>..\..\AppNetCore\CMSFramework.Logger.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDataEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.MeasDataEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDefEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.MeasDefEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Repository">
      <HintPath>..\..\AppNetCore\CMSFramework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.TypeDef">
      <HintPath>..\..\AppNetCore\CMSFramework.TypeDef.dll</HintPath>
    </Reference>
    <Reference Include="log4net">
      <HintPath>..\..\AppNetCore\log4net.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="DAUE6X\CMSFramework.DAUFacade.E6X.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DAUE6X\Werida.Core.1413x.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="WindDAQConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="RSConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TSMConfigDAU_E.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\WTCMSLive.BusinessModel\WTCMSLive.BusinessModel\WTCMSLive.BusinessModel.csproj" />
  </ItemGroup>

</Project>
