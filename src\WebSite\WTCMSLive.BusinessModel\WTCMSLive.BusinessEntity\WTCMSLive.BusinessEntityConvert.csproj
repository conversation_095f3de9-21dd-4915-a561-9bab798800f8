﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{17C285A6-9E4B-4079-986D-C22FD28D234B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WTCMSLive.BusinessEntityConvert</RootNamespace>
    <AssemblyName>WTCMSLive.BusinessEntityConvert</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AppFramework.ServiceBus">
      <HintPath>..\..\..\..\20_AppArchitecture\05_Release\V1.1.0\AppFramework.ServiceBus.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WTCMSLive.BusinessEntity, Version=2.1.2014.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.DAU, Version=2.0.2013.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.DAU.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.DevTree, Version=2.0.2013.5, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.DevTree.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.MeasData, Version=2.1.2014.7, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.MeasData.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.MeasDefinition, Version=2.0.2013.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.MeasDefinition.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.SVM, Version=1.1.2013.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.SVM.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.TurbineCondition, Version=2.0.2014.4, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.TurbineCondition.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.BusinessEntity.WindTurbineModel, Version=2.0.2014.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.BusinessEntity.WindTurbineModel.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.Entity">
      <HintPath>..\..\DLL\WTCMSLive.Entity.dll</HintPath>
    </Reference>
    <Reference Include="WTCMSLive.IDALService, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\WTCMSLive.IDALService.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ConvertEntityBusinessToDACommon.cs" />
    <Compile Include="ConvertEntityDAToBusinessCommon.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WaveDefParamConvert.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.AlarmStatus\ConvertBusinessEntityToDARTStatus.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.DAU\ConvertEntityBusinessToDADAU.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.DAU\ConvertEntityDAToBusinessDAU.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.Device\ConvertEntityBusinessToDADEV.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.Device\ConvertEntityDAToBusinessDEV.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.MeasData\ConvertEntityBusinessToDAData.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.MeasData\ConvertEntityDAToBusinessData.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.MeasData\EVDataTrendMeasEvent.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.MeasDefinition\ConvertEntityBusinessToDAMDF.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.MeasDefinition\ConvertEntityDAToBusinessMDF.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.SVM\ConvertEntityBusinessToDASVM.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.SVM\ConvertEntityDAToBusinessSVM.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.WindTurbineModel\ConvertEntityBusinessToDAWTM.cs" />
    <Compile Include="WTCMSLive.BusinessEntity.WindTurbineModel\ConvertEntityDAToBusinessWTM.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="WTCMSLive.BusinessEntity.AlarmStatus\ConvertDAToBusinessEntityRTStatus.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>