import{u as ne,C as ie,W as le}from"./table-DznTy2O5.js";import{O as ce}from"./index-BKL_RKUZ.js";import{W as pe}from"./index-C1FH5xdO.js";import{S as ue,as as de,at as ye,au as he,av as me,aw as be,ax as fe,ay as ge,az as ve,aA as Se,aB as we,aC as Te,aD as Ce,r as h,u as Ie,X as De,j as N,w as Me,f as I,d as g,o as m,c as M,b as A,i as Ae,F as Re,e as Oe,g as V,t as ke,m as b,ar as $}from"./index-BedJHPLx.js";import{b as R,g as O,f as _e}from"./tools-DC78Tda0.js";import{u as Be}from"./devTree-Dgv5CE1u.js";import{S as Fe}from"./ActionButton-FpgTQOJj.js";import{D as qe,a as Ge}from"./index-BPV4rCOw.js";import{B as Le}from"./index-CTEgH1Bv.js";import{M as xe}from"./index-BD0EDEeZ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./styleChecker-D6uzjM95.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const Pe=ue("configMainControl",{state:()=>({mCSInfoList:[],mCSDataList:[],workFromMcsOptions:[],stateRegisterTypeOptions:[],registerStorageOptions:[],byteArrayTypeOptions:[]}),actions:{reset(){this.$reset()},async fetchMCSGetMCSInfoList(s){try{const e=await Ce(s);return this.mCSInfoList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCS(s){try{return await Te(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchEditMCS(s){try{return await we(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCS(s){try{return await Se(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetMCSGetMCSData(s){try{const e=await ve(s);return this.mCSDataList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCSRegister(s){try{return await ge(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddEditMCSRegister(s){try{return await fe(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCSRegister(s){try{return await be(s)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetWorkFromMcs(s){try{const e=await me(s);let d=R(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.workFromMcsOptions=d,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetStateRegisterTypes(s){try{const e=await he(s);let d=R(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.stateRegisterTypeOptions=d,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetRegisterStorages(s){try{const e=await ye(s);let d=R(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.registerStorageOptions=d,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetByteArrayTypes(s){try{const e=await de(s);let d=R(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.byteArrayTypeOptions=d,e}catch(e){throw console.error("操作失败！",e),e}},reset(){this.$reset()}}}),We={class:"border"},Ke={key:1,class:"nodata"},Ee={key:2},ot={__name:"mainControl",setup(s){const e=Pe(),d=Be(),z=ne(),D=(t={idForm:!1})=>[{title:"寄存器地址",dataIndex:"channelNumber",columnWidth:180,formItemWidth:y,isrequired:!0,validateRules:O({title:"寄存器地址",type:"number"}),columnOperate:{type:"number"}},{title:"工况测量位置",dataIndex:"paramMeaning",columnWidth:160,formItemWidth:y,inputType:"select",selectOptions:[],labelInValue:!0,isrequired:!0},{title:"寄存器类型",dataIndex:"registerType",columnWidth:110,formItemWidth:y,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:e.stateRegisterTypeOptions},...t.idForm?{}:{customRender:({text:r,record:n})=>{const o=e.stateRegisterTypeOptions.find(i=>i.value==n.registerType);return o?o.label:r}}},{title:"数据存储方式",dataIndex:"registerStorageType",columnWidth:110,formItemWidth:y,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:e.registerStorageOptions},...t.idForm?{}:{customRender:({text:r,record:n})=>{const o=e.registerStorageOptions.find(i=>i.value==n.registerStorageType);return o?o.label:r}}},{title:"对齐方式",dataIndex:"byteArrayType",columnWidth:100,formItemWidth:y,inputType:"select",selectOptions:[],isrequired:!0,headerOperations:{filters:e.byteArrayTypeOptions},...t.idForm?{}:{customRender:({text:r,record:n})=>{const o=e.byteArrayTypeOptions.find(i=>i.value==n.byteArrayType);return o?o.label:r}}},{title:"字节交换",dataIndex:"byteSwap",columnWidth:120,formItemWidth:y,isrequired:!0,headerOperations:{filters:[{text:"是",value:1},{text:"否",value:0}]},initValue:0,inputType:"radio",selectOptions:[{label:"是",value:1},{label:"否",value:0}],...t.idForm?{}:{customRender:({text:r,record:n})=>r?"是":"否"}},{title:"转换系数",dataIndex:"coeff",formItemWidth:y,validateRules:O({title:"转换系数",type:"number"})}],q=async()=>{(!e.byteArrayTypeOptions||!e.byteArrayTypeOptions.length)&&await e.fetchGetByteArrayTypes()},G=Ie();let y=320;const k=h(!1),C=h(""),u=h(""),v=h(""),c=h(G.params.id),S=h({}),f=h([]),L=h(D()),_=h(!1),a=De({baseInfo:{},tableDatas:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),j=async t=>{v.value&&await z.fetchDevTreedDevicelist({windParkID:v.value,useTobath:!0})},U=N(()=>[{label:"主控IP",value:a.baseInfo.mcsip||""},{label:"端口",value:a.baseInfo.mcsPort||""}]),x=async()=>{const t=await e.fetchMCSGetMCSInfoList({windParkID:v.value,turbineID:c.value});t&&t.length>0?a.baseInfo=t[0]:a.baseInfo={}},B=async t=>{k.value=!0,await q(),await W(),await K(),P(),L.value=D(),v.value&&x(),k.value=!1},P=async t=>{if(v.value){const r=await e.fetchGetMCSGetMCSData({windTurbineID:c.value});a.tableDatas=r}},H=()=>{let t=d.findAncestorsWithNodes(c.value);t&&t.length&&t.length>1&&(v.value=t[t.length-2].id)};Me(()=>G.params.id,async t=>{t&&(e.reset(),c.value=t,H(),await j(),B())},{immediate:!0});const X=N(()=>u.value==="batchAdd"?"1200px":"600px"),J=()=>{u.value="editInfo",C.value="编辑主控信息",f.value=re,S.value=a.baseInfo,F()},F=()=>{_.value=!0},w=t=>{_.value=!1,f.value=[],S.value={},u.value="",C.value=""},Q=async()=>{(!e.workFromMcsOptions||!e.workFromMcsOptions.length)&&await e.fetchGetWorkFromMcs({trubineId:c.value})},W=async()=>{(!e.stateRegisterTypeOptions||!e.stateRegisterTypeOptions.length)&&await e.fetchGetStateRegisterTypes()},K=async()=>{(!e.registerStorageOptions||!e.registerStorageOptions.length)&&await e.fetchGetRegisterStorages()},E=async()=>{const t=f.value;await Q(),await W(),await K(),await q(),t[1].selectOptions=e.workFromMcsOptions,t[2].selectOptions=e.stateRegisterTypeOptions,t[3].selectOptions=e.registerStorageOptions,t[4].selectOptions=e.byteArrayTypeOptions},Y=async t=>{const{title:r,operateType:n}=t;u.value=n,C.value="添加"+r.split("列表")[0],f.value=D({idForm:!0}),await E(),F()},Z=async t=>{const{tableKey:r,selectedkeys:n,deleteInRow:o,record:i}=t;if(!t||!n||!n.length)return;let l=[];if(o)l.push({measLocProcessID:i.measLocProcessID,windTurbineID:c.value});else for(let p=0;p<n.length;p++){let oe=n[p].split("&&");l.push({measLocProcessID:oe[1],windTurbineID:c.value})}const T=await e.fetchBatchDeleteMCSRegister({sourceData:l,targetTurbineIds:a.batchApplyData});T&&T.code===1?(P(),a.bathApplyResponse1=T.batchResults||{},b.success("删除成功")):b.error("删除失败:"+T.msg)},ee=async t=>{const{rowData:r,tableKey:n,title:o,operateType:i}=t;u.value=i,C.value="编辑"+o.split("列表")[0],S.value={...r,paramMeaning:{label:r.paramMeaning,value:r.measLocProcessID}};let l=[...D({idForm:!0})];l[1].disabled=!0,f.value=l,await E(),F()},te=async t=>{if(u.value==="editInfo"){const o=await e.fetchBatchEditMCS([{...t,WindTurbineID:c.value}]);o&&o.code===1?(x(),w(),b.success("提交成功")):b.error("提交失败:"+o.msg);return}let r={...t,windTurbineID:c.value,measLocProcessID:S.value.paramMeaning.value,paramMeaning:S.value.paramMeaning.label,coeff:t.coeff!==void 0&&t.coeff!==null&&parseFloat(t.coeff)||0,byteArrayType:t.byteArrayType!==void 0&&t.byteArrayType!==null&&parseInt(t.byteArrayType)||0};const n=await e.fetchBatchAddEditMCSRegister({sourceData:[r],targetTurbineIds:a.batchApplyData});n&&n.code===1?(B(),a.bathApplyResponse1=n.batchResults||{},w(),b.success("提交成功")):b.error("提交失败:"+n.msg)},ae=async t=>{let r={windTurbineID:c.value},o=_e(t,r,{paramMeaning:{label:"paramMeaning",value:"measLocProcessID"}});o&&o.length>0&&(o=o.map(l=>({...l,coeff:l.coeff!==void 0&&l.coeff!==null&&parseFloat(l.coeff)||0,byteArrayType:l.byteArrayType!==void 0&&l.byteArrayType!==null&&parseInt(l.byteArrayType)||0})));let i=await e.fetchBatchAddMCSRegister({sourceData:o,targetTurbineIds:a.batchApplyData});i&&i.code===1?(B(),a.bathApplyResponse1=i.batchResults||{},w(),b.success("提交成功")):b.error("提交失败:"+i.msg)},re=[{title:"主控IP",dataIndex:"mcsip",formItemWidth:y,validateRules:O({type:"ip",title:"主控IP",required:!0})},{title:"端口",dataIndex:"mcsPort",formItemWidth:y,validateRules:O({type:"port",title:"端口",required:!0})}],se=async t=>{t.type&&t.type=="close"?(a.batchApplyData=[],a.batchApplyKey="",a[`bathApplyResponse${t.key}`]={}):(a.batchApplyData=t.turbines,a.batchApplyKey=t.key)};return $("deviceId",c),$("bathApplySubmit",se),(t,r)=>{const n=Le,o=Ge,i=qe,l=xe,T=Fe;return m(),I(T,{spinning:k.value,size:"large"},{default:g(()=>[(m(),M("div",{key:c.value},[A(ie,{tableTitle:"主控信息",defaultCollapse:!0,batchApply:!1},{rightButtons:g(()=>[A(n,{type:"primary",onClick:r[0]||(r[0]=p=>J()),disabled:!(a.baseInfo&&a.baseInfo.mcsip&&a.baseInfo.mcsip!=="")},{default:g(()=>r[1]||(r[1]=[V(" 编辑 ",-1)])),_:1,__:[1]},8,["disabled"])]),content:g(()=>[Ae("div",We,[A(i,{column:4,size:"small"},{default:g(()=>[(m(!0),M(Re,null,Oe(U.value,p=>(m(),I(o,{label:p.label,key:p.label},{default:g(()=>[V(ke(p.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),a.baseInfo&&a.baseInfo.mcsip&&a.baseInfo.mcsip!==""?(m(),I(le,{key:0,ref:"table",size:"default",borderLight:a.batchApplyKey=="1",bathApplyResponse:a.bathApplyResponse1,"table-key":"1","table-title":"主控数据寄存器列表","table-columns":L.value,noPagination:!0,"table-operate":["delete","edit","batchDelete","batchAdd"],recordKey:p=>`${p.channelNumber}&&${p.measLocProcessID}`,"table-datas":a.tableDatas,onAddRow:Y,onDeleteRow:Z,onEditRow:ee},null,8,["borderLight","bathApplyResponse","table-columns","recordKey","table-datas"])):(m(),M("div",Ke," 请添加主控!")),A(l,{maskClosable:!1,width:X.value,open:_.value,title:C.value,footer:"",onCancel:w},{default:g(()=>[u.value==="add"||u.value==="editInfo"||u.value==="edit"?(m(),I(ce,{key:0,onCancelForm:w,titleCol:f.value,initFormData:S.value,onSubmit:te},null,8,["titleCol","initFormData"])):u.value==="batchAdd"?(m(),I(pe,{key:1,ref:"table",size:"default","table-key":"0","table-columns":f.value,"table-operate":["copyUp","delete"],"table-datas":[],"noRepeat-Keys":["channelNumber","paramMeaning"],"noCopyUp-keys":["measLocProcessID","paramMeaning"],onSubmit:ae,removeDuplicateKeys:["paramMeaning"],onCancel:w},null,8,["table-columns"])):(m(),M("div",Ee))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{ot as default};
