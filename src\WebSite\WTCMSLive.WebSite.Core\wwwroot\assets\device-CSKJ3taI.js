import{u as ee,W as te}from"./table-DznTy2O5.js";import{O as ae}from"./index-BKL_RKUZ.js";import{r as s,u as ne,j as F,h as oe,w as le,f as A,d,o as D,i as B,b as p,c as q,q as re,F as se,g as C,p as N,t as V,m as h}from"./index-BedJHPLx.js";import{u as ie}from"./jfDeviceManage-DLlM3C01.js";import{g as ue}from"./tools-DC78Tda0.js";import{_ as de,p as ce,o as pe,s as b}from"./useWebSocket-DEdJh53k.js";import{u as fe}from"./devTree-Dgv5CE1u.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as me}from"./ActionButton-FpgTQOJj.js";import{B as ge}from"./index-CTEgH1Bv.js";import{M as Ce}from"./index-BD0EDEeZ.js";import"./styleChecker-D6uzjM95.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const f=320,he={__name:"device",setup(be){const c=ie(),z=ee(),L=fe(),y=s(!1),k=ne(),I=s(""),g=s(""),x=s({}),r=s([]);s([]);const _=s([]),S=s(k.params.id),u=s(!1),$=s([]),T=s(""),U=s(!1),R=F(()=>g.value==="useMeasurementDefinition"?"1200px":"600px"),J=F(()=>g.value==="otherConfig"?"part":"horizontal"),v=(t,n)=>[{title:"机组名称",dataIndex:"windTurbineName",labelInValue:!n,columnWidth:100,inputType:"select",selectOptions:z.deviceOptions,disabled:n,isrequired:!0,formItemWidth:f},{title:"采集器类型",dataIndex:"dauType",hasChangeEvent:!0,columnWidth:140,inputType:"select",isrequired:!0,selectOptions:c.dAUTypeList,isdisplay:!n,formItemWidth:f,headerOperations:{filters:[]}},{title:"DAU IP地址",dataIndex:"ip",columnWidth:150,formItemWidth:f,columnOperate:{type:"ip"},validateRules:ue({type:"ip",title:"IP地址",required:!0})},{title:"服务器地址",dataIndex:"port",columnWidth:70,formItemWidth:f},{title:"在离线状态",dataIndex:"dauOnOffStatus",otherColumn:!0,columnWidth:120,formItemWidth:f,headerOperations:{filters:[{text:"在线",value:2},{text:"离线",value:1}]}}],m=s(v()),W=async()=>{u.value=!0;let t=await c.fetchGetDAUList({windTurbineID:S.value,WindParkId:T.value});r.value=t,u.value=!1,m.value=v()},M=()=>{let t=L.findAncestorsWithNodes(S.value);t&&t.length&&t.length>1&&(T.value=t[t.length-2].id)};oe(()=>{M(),W()}),le(()=>k.params.id,t=>{c.reset(),S.value=t,M(),W()});const j=async(t,n)=>{u.value=!0;let o=r.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),a=await c.fetchStartAcquisition(o);u.value=!1,a&&a.code===1?(m.value=[...v(),{title:"采集状态",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:a.data[e.ip]}:e)):h.error("操作失败")},E=async(t,n)=>{u.value=!0;let o=r.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),a=await c.fetchStopAcquisition(o);u.value=!1,a&&a.code===1?(m.value=[...v(),{title:"停止采集",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:a.data[e.ip]}:e)):h.error("操作失败")},G=async(t,n)=>{u.value=!0;let o=r.value.filter(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)),a=await c.fetchSetMeasureDefinition(o);u.value=!1,a&&a.code===1?(m.value=[...v(),{title:"测量定义下发",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>t.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:a.data[e.ip]}:e)):h.error("操作失败")},H=async(t,n)=>{g.value=n,I.value="推送配置",$.value=t,m.value=[...v()],_.value=ce(),O()},Q=async(t,n)=>{g.value=n,I.value="高级参数配置",$.value=t,_.value=pe(),m.value=[...v()],O()},O=()=>{y.value=!0},P=t=>{y.value=!1,_.value=[],x.value={},g.value="",I.value=""},X=async t=>{u.value=!0;let o=r.value.filter(e=>$.value.includes(`${e.windTurbineID}&&${e.dauID}`)).map(e=>({...e,...t})),a=null;g.value==="pushConfig"?a=await c.fetchJfSetSFTPConfig(o):g.value==="otherConfig"&&(a=await c.fetchJfSetAdvancedParameters(o)),u.value=!1,a?(h.success("操作成功"),P()):h.error("操作失败")},Y=async t=>{const n=await b.startConnection("/Hubs/ServerPerformanceHub");if(U.value=n,n){b.onReceiveMessage("ProgressMonitoringStarted",a=>{console.log("进度启动结果",a)}),b.onReceiveMessage("ProgressUpdate",a=>{console.log("进度更新",a)}),b.onReceiveMessage("CurrentProgressUpdate",a=>{console.log("当前进度响应",a)});const o={parkID:T.value,daus:t};b.sendMessage("StartProgressMonitoring",o)}else h.error("连接失败")},Z=async(t,n)=>{let o=r.value.filter(a=>t.includes(`${a.windTurbineID}&&${a.dauID}`));await c.fetchGetWaveFormData(o),m.value=[...v(),{title:"录波数据进度",dataIndex:"process",otherColumn:!0,columnWidth:120,formItemWidth:f}],Y(o),r.value=r.value.map(a=>t.includes(`${a.windTurbineID}&&${a.dauID}`)?{...a,process:100}:a)};return(t,n)=>{const o=ge,a=de,e=Ce,K=me;return D(),A(K,{spinning:u.value,size:"large"},{default:d(()=>[B("div",null,[p(te,{ref:"table",size:"default","table-key":"0","table-title":"设备列表","table-columns":m.value,recordKey:l=>`${l.windTurbineID}&&${l.dauID}`,"table-datas":r.value,noBatchApply:!0,selectedRows:!0},{rightButtons:d(({selectedRowKeys:l})=>[p(o,{type:"primary",onClick:i=>j(l,"startCollection"),disabled:!l.length},{default:d(()=>n[0]||(n[0]=[C(" 启动采集 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"]),p(o,{onClick:i=>E(l,"stopCollection"),disabled:!l.length},{default:d(()=>n[1]||(n[1]=[C(" 停止采集 ",-1)])),_:2,__:[1]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:i=>G(l,"useMeasurementDefinition"),disabled:!l.length},{default:d(()=>n[2]||(n[2]=[C(" 测量定义下发 ",-1)])),_:2,__:[2]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:i=>H(l,"pushConfig"),disabled:!l.length},{default:d(()=>n[3]||(n[3]=[C(" 推送配置 ",-1)])),_:2,__:[3]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:i=>Z(l,"getObtainWaveform"),disabled:!l.length},{default:d(()=>n[4]||(n[4]=[C(" 获取录波数据 ",-1)])),_:2,__:[4]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:i=>Q(l,"otherConfig"),disabled:!l.length},{default:d(()=>n[5]||(n[5]=[C(" 高级参数配置 ",-1)])),_:2,__:[5]},1032,["onClick","disabled"])]),otherColumn:d(({record:l,text:i,column:w})=>[w.dataIndex==="dauOnOffStatus"?(D(),q(se,{key:0},[B("span",{class:N([i==2?"green":"gray","circle"])},null,2),C(" "+V(i==1?"离线":"在线"),1)],64)):w.dataIndex==="process"&&i&&i!==""?(D(),A(a,{key:1,type:"circle",size:30,"stroke-color":{"0%":"#108ee9","100%":"#87d068"},percent:90})):w.dataIndex==="operateStatus"?(D(),q("span",{key:2,class:N([i?"greenText":"redtext"])},V(i?"成功":"失败"),3)):re("",!0)]),_:1},8,["table-columns","recordKey","table-datas"]),p(e,{maskClosable:!1,width:R.value,open:y.value,title:I.value,footer:"",destroyOnClose:!0,onCancel:P},{default:d(()=>[p(ae,{titleCol:_.value,initFormData:x.value,formlayout:J.value,onSubmit:X},null,8,["titleCol","initFormData","formlayout"])]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},qe=ve(he,[["__scopeId","data-v-c3f295f0"]]);export{qe as default};
