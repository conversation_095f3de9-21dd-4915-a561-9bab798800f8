﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// Modbus通道DTO
    /// </summary>
    public class ModbusChannelDTO
    {
        public int ModbusDeviceID { get; set; }
        public string WindTurbineID { get; set; }
        public int ChannelNumber { get; set; }

        public string MeasLocationID { get; set; }
        public string? Description { get; set; }
        public string? MeasLocationName { get; set; }
    }

    /// <summary>
    /// 编辑Modbus通道DTO
    /// </summary>
    public class EditModbusChannelDTO
    {
        public string WindTurbineID { get; set; }
        public int ModbusDeviceID { get; set; }
        public int ChannelNumber { get; set; }
        public string? MeasLocationID { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 批量删除Modbus通道DTO
    /// </summary>
    public class BatchDeleteModbusChannelDTO
    {
        public List<ModbusChannelIdentifier> Channels { get; set; } = new List<ModbusChannelIdentifier>();
    }

    /// <summary>
    /// Modbus通道标识符
    /// </summary>
    public class ModbusChannelIdentifier
    {
        public string WindTurbineID { get; set; }
        public int ModbusDeviceID { get; set; }
        public int ChannelNumber { get; set; }

        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// Modbus通道列表查询响应DTO
    /// </summary>
    public class ModbusChannelListDTO
    {
        public string WindTurbineID { get; set; }
        public int ModbusDeviceID { get; set; }
        public int ChannelNumber { get; set; }
        public string? MeasLocationID { get; set; }
        public string? MeasLocationName { get; set; }
        public string? Description { get; set; }
        public int ModbusDevType { get; set; }
        public string ModbusDevTypeName { get; set; }

        public string? ModbusUnitID { get; set; }

        public string? ModbusDeviceName { get; set; }
    }
}
