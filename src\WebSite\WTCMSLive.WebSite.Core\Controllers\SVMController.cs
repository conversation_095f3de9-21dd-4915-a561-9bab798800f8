﻿using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Mvc;

namespace WTCMSLive.WebSite.Controllers
{
    public class SVMController : Controller
    {
        // 王岩 2015年6月16日 16:31:01
        // 晃度仪配置

        #region 风场相关
        /// <summary>
        /// 添加晃度仪信息
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Name"></param>
        /// <param name="MAddress"></param>
        /// <param name="SVMAcqType"></param>
        /// <param name="AcquisitionFrequency"></param>
        /// <param name="SampleLength"></param>
        /// <returns></returns>
        public bool AddSVM(string ID, string Name, string MAddress, string SVMAcqType)
        {
            SVMUnit _svm = new SVMUnit();
            _svm.AssocWindTurbineID = ID;
            _svm.SVMName = Name;
            _svm.ModbusAddress = MAddress;
            _svm.SVMID = MAddress;
            try
            {
                //生产晃动测量位置信息
                List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(ID);
                WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "机舱" || i.ComponentName == "塔筒");
                if (myComponent == null)
                {
                    return false;
                }
                _svm.ComponentID = myComponent.ComponentID;
                SVMManagement.AddSVM(_svm);
                //SVMManagement.AutoAddSVMMeasLoc(ID, myComponent.ComponentName);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = ID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("增加_机组SVM({0})", ID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddSVM]增加机组SVM失败", ex);
                return false;
            }
            return true;
        }
        /// <summary>
        /// 编辑晃度仪信息
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Name"></param>
        /// <param name="MAddress"></param>
        /// <param name="SVMAcqType"></param>
        /// <param name="AcquisitionFrequency"></param>
        /// <param name="SampleLength"></param>
        /// <returns></returns>
        public bool EditSVM(string ID, string Name, string MAddress, string SVMAcqType)
        {
            //SVMUnit _svm = SVMManagement.GetSVMById(ID);
            //_svm.AssocWindTurbineID = ID;
            //_svm.SVMName = Name;
            //_svm.ModbusAddress = MAddress;
            //_svm.SVMID = MAddress;
            //try
            //{
            //    SVMManagement.EditSVMInfo(_svm);
            //    #region
            //    LogEntity logEntity = new LogEntity();
            //    logEntity.LogDB = ConstDefine.UserManagementLog;
            //    logEntity.LogTime = DateTime.Now;
            //    logEntity.NodeID = ID.ToString();
            //    logEntity.UserName = Request.Cookies["WindCMSUserName"];
            //    logEntity.OperationDescription
            //        = string.Format("编辑_机组SVM({0})", ID);
            //    LogManagement.UserlogWrite(logEntity);
            //    #endregion
            //}
            //catch (Exception ex)
            //{
            //    CMSFramework.Logger.Logger.LogErrorMessage("[EditSVM]编辑机组SVM失败", ex);
            //    return false;
            //}

            try
            {
                //SVMManagement.EditSVMInfo(_svm);
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    //删除
                    SVMUnit _svm = ctx.SVMUnits.FirstOrDefault(p => p.AssocWindTurbineID == ID);
                    if (_svm == null) //如果机组下没晃度仪信息，就返回添加失败
                        return false;
                    ctx.SVMUnits.Remove(_svm);

                    //_svm.AssocWindTurbineID = turbineID;
                    //_svm.SVMName = AssocWindTurbineName;
                    //_svm.ModbusAddress = ModbusAddress;
                    //_svm.SVMID = ModbusAddress;

                    //添加
                    ctx.SVMUnits.Add(new SVMUnit()
                    {
                        AssocWindTurbineID = ID,
                        SVMID = MAddress,
                        ModbusAddress = MAddress,
                        SVMName = Name,
                        ComponentID = _svm.ComponentID,
                        SVMCode = _svm.SVMCode,
                        SVMRegisterList = _svm.SVMRegisterList,
                        InstallSurfaceDirection = _svm.InstallSurfaceDirection,
                        InterfaceDirection = _svm.InterfaceDirection,
                        SerialPortName = _svm.SerialPortName,
                        SVMSoftwareVersion = _svm.SVMSoftwareVersion
                    });
                    //ctx.Entry(_svm).State = System.Data.Entity.EntityState.Modified;
                    ctx.SaveChanges();

                    // 添加晃度仪寄存器
                    List<MeasLoc_SVM> measloc_svmList = SVMManagement.GetNotUsedMeasLoc_SVMList(ID);
                    List<SVMRegister> RegisterList = new List<SVMRegister>();
                    foreach (MeasLoc_SVM measloc in measloc_svmList)
                    {
                        SVMRegister register = new SVMRegister();
                        register.AssocWindTurbineID = ID;
                        register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(measloc);
                        register.ComponentID = measloc.ComponentID;
                        register.SVMMeasLocId = measloc.MeasLocationID;
                        register.RegisterType = (int)measloc.ParamType;
                        register.SVMID = MAddress;
                        RegisterList.Add(register);
                    }
                    SVMManagement.AddSVMRegister(RegisterList);
                };
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = ID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑_SVM信息({0})", ID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditSVMInformation]编辑SVM信息失败 ", ex);
                return false;
            }
            return true;
        }
        /// <summary>
        /// 删除晃度仪信息
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        public string DeleteSVM(string ID,string ComponentID)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                SVMUnit svmUnit = SVMManagement.GetSVMById(ID);
                if (WaveDefinitionManagement.IsExitSVMWaveDef(svmUnit.AssocWindTurbineID) == false)
                {
                    // 删除晃度仪并删除绑定机组下的晃度仪测量位置
                    SVMManagement.DeleteSVM(ID, ComponentID);
                }
                else
                {
                    message = string.Format(message, 0, "请先删除晃度波形定义！");
                    return "{" + message + "}";
                }
                message = string.Format(message, 1, "");
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = ID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_机组SVM({0})", ID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteSVM]删除机组SVM失败", ex);
                message = string.Format(message, 0, "删除机组SVM失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }

        #endregion

        #region 风机相关
        /// <summary>
        /// 编辑机组下SVM信息
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="AssocWindTurbineName"></param>
        /// <param name="ModbusAddress"></param>
        /// <param name="AssocDAUIP"></param>
        /// <param name="SVMAcqType"></param>
        /// <param name="AcquisitionFrequency"></param>
        /// <param name="SampleLength"></param>
        /// <param name="TrendSaveInterval"></param>
        /// <returns></returns>
        public bool EditSVMInformation(string turbineID, string AssocWindTurbineName, string ModbusAddress, string AssocDAUIP, string SVMAcqType, int trendSaveInterval)
        {
            //SVMUnit _svm = SVMManagement.GetSVMById(turbineID);
            //if (_svm == null) //如果机组下没晃度仪信息，就返回添加失败
            //    return false;
            
            // wangyan 晃度仪分离波形数据的时候，注释掉存储间隔
            //_svm.TrendSaveInterval = 0;
            try
            {
                //SVMManagement.EditSVMInfo(_svm);
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    //删除
                    SVMUnit _svm = ctx.SVMUnits.FirstOrDefault(p => p.AssocWindTurbineID == turbineID);
                    if (_svm == null) //如果机组下没晃度仪信息，就返回添加失败
                        return false;
                    ctx.SVMUnits.Remove(_svm);

                    //_svm.AssocWindTurbineID = turbineID;
                    //_svm.SVMName = AssocWindTurbineName;
                    //_svm.ModbusAddress = ModbusAddress;
                    //_svm.SVMID = ModbusAddress;

                    //添加
                    ctx.SVMUnits.Add(new SVMUnit() { 
                        AssocWindTurbineID = turbineID,
                        SVMID = ModbusAddress,
                        ModbusAddress = ModbusAddress,
                        SVMName = AssocWindTurbineName,
                        ComponentID = _svm.ComponentID,
                        SVMCode = _svm.SVMCode,
                        SVMRegisterList= _svm.SVMRegisterList,
                        InstallSurfaceDirection = _svm.InstallSurfaceDirection,
                        InterfaceDirection = _svm.InterfaceDirection,
                        SerialPortName = _svm.SerialPortName,
                        SVMSoftwareVersion= _svm.SVMSoftwareVersion
                    });
                    //ctx.Entry(_svm).State = System.Data.Entity.EntityState.Modified;
                    ctx.SaveChanges();

                    // 添加晃度仪寄存器
                    List<MeasLoc_SVM> measloc_svmList = SVMManagement.GetNotUsedMeasLoc_SVMList(turbineID);
                    List<SVMRegister> RegisterList = new List<SVMRegister>();
                    foreach (MeasLoc_SVM measloc in measloc_svmList)
                    {
                        SVMRegister register = new SVMRegister();
                        register.AssocWindTurbineID = turbineID;
                        register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(measloc);
                        register.ComponentID = measloc.ComponentID;
                        register.SVMMeasLocId = measloc.MeasLocationID;
                        register.RegisterType = (int)measloc.ParamType;
                        register.SVMID = ModbusAddress;
                        RegisterList.Add(register);
                    }
                    SVMManagement.AddSVMRegister(RegisterList);
                };
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = turbineID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑_SVM信息({0})", turbineID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditSVMInformation]编辑SVM信息失败 ", ex);
                return false;
            }
            return true;
        }
        /// <summary>
        /// 获取没有绑定寄存器的晃动测量位置列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetSVMRegister(string turbineID)
        {
            List<MeasLoc_SVM> measloc_svmList = SVMManagement.GetNotUsedMeasLoc_SVMList(turbineID);
            return measloc_svmList.OrderBy(item => item.OrderSeq).ToJson();
        }
        /// <summary>
        /// 添加晃度仪寄存器信息
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="SVMeasLocId"></param>
        /// <param name="SVMUnitID"></param>
        /// <returns></returns>
        public string AddSVMRegister(string turbineID, string SVMeasLocId, string SVMUnitID)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                if (string.IsNullOrEmpty(SVMeasLocId))
                {
                    return "{" + string.Format(Message, 0, "没有选择晃动测量位置信息！") + "}";
                }
                else
                {
                    List<SVMRegister> registerList = new List<SVMRegister>();
                    List<MeasLoc_SVM> svmMeasLocList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                    foreach (string data in SVMeasLocId.Split(','))
                    {
                        SVMRegister register = new SVMRegister();
                        register.AssocWindTurbineID = SVMUnitID;
                        MeasLoc_SVM measloc = svmMeasLocList.Find(svm => svm.MeasLocationID == data);
                        register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(measloc);
                        register.ComponentID = measloc.ComponentID;
                        register.AssocWindTurbineID = turbineID;
                        register.SVMMeasLocId = data;
                        register.RegisterType = (int)measloc.ParamType;
                        registerList.Add(register);
                    }
                    SVMManagement.AddSVMRegister(registerList);
                    Message = string.Format(Message, 1, "");
                }
            }
            catch (Exception myex)
            {
                Message = string.Format(Message, 0, myex.Message);
            }
            return "{" + Message + "}";
        }
        #endregion

        #region 晃度仪监测页面

        public ActionResult SVMonitor(string windParkID)
        {
            if (User.Identity.IsAuthenticated == false)
            {
                ViewBag.status = "relogin";
                return RedirectToAction("Login", "Home");
            }
            //if (turbineID != null)
            //{
            //    return RedirectToAction("SVMChannelSetting", new { WindParkID = windParkID, TurbineID = turbineID });
            //}
            //判断角色权限
            string userName = Request.Cookies["WindCMSUserName"];
            //ViewData["roleAdd"] = RoleManagement.IsFunOperation(userName, ModuleName.DAUManage, FunctionName.Add);
            //ViewData["roleEdit"] = RoleManagement.IsFunOperation(userName, ModuleName.DAUManage, FunctionName.Edit);
            //ViewData["roleDel"] = RoleManagement.IsFunOperation(userName, ModuleName.SVMDetail, FunctionName.Delete);
            //bool edit = (bool)ViewData["roleEdit"];

            ViewData["ParkID"] = windParkID;
            DAUManager dauManager = new DAUManager();
            //ViewData["SVMInfoTable"] = dauManager.GetDAUList(edit, windParkID).ToJson();
            ViewData["WindTurbineName"] = dauManager.GetTurbineDropDown(windParkID).ToJson();
            if (Request.IsAjaxRequest())
            {
                return PartialView();
            }
            //使用ViewBag.pageType此处会出错，因此改用ViewData["pageType"]
            ViewData["pageType"] = "view";
            WindPark windPark = DevTreeManagement.GetWindPark(windParkID);
            if (windPark == null)
            {
                ViewData["NodePath"] = Utility.ToJson(new object[]
                {
                    new []{Resources.Message.CompanyName,"/DeviceOverView/Index"}
                });
            }
            else
            {
                ViewData["NodePath"] = Utility.ToJson(new object[]
                {
                    new []{Resources.Message.CompanyName,"/DeviceOverView/Index"},
                    new []{windPark.WindParkName,"/WindTurbine/SVMonitor/"+windParkID}
                });
            }
            ViewData["info"] = windParkID;
            TreeManager treeManager = new TreeManager();
            ViewData["leftTree"] = treeManager.GetTreeModel().ToJson();
            return View();
        }
        /// <summary>
        /// 获取晃度仪状态
        /// </summary>
        /// <param name="windParkID"></param>
        /// <returns></returns>
        public string GetSVMonitorList(string windParkID)
        {
            List<SVMUnit> svmList = SVMManagement.GetSVMUnitListByWindParkID(windParkID);
            List<AlarmStatus_MeasLocSVM> svmMeasLocList = SVMManagement.GetAllSVMeasLocStateList(windParkID);
            List<SVMonitorEntity> svmMonitorEntity = new List<SVMonitorEntity>();
            svmList.ForEach(item =>
            {
                AlarmStatus_MeasLocSVM svmLoc = svmMeasLocList.FindAll(loc => loc.WindTurbineID == item.AssocWindTurbineID).OrderByDescending(loc => loc.AlarmUpdateTime).FirstOrDefault();
                if (svmLoc != null)
                {
                    svmMonitorEntity.Add(new SVMonitorEntity()
                    {
                        WindTurbineName = item.SVMName,
                        windparkid=windParkID,
                        WindTurbineId=item.AssocWindTurbineID,
                        Modbus = item.ModbusAddress,
                        AlarmState = AppFramework.Utility.EnumHelper.GetDescription(svmLoc.AlarmDegree),
                        AlarmDate = svmLoc.AlarmUpdateTime.ToString("yyyy-MM-dd HH:mm:ss")
                    });
                }
            });
            return svmMonitorEntity.ToJson();
        }
        
        public ActionResult SVMonitorDetail(string windParkID, string turbineID)
        {
            //if (string.IsNullOrEmpty(windParkID))
            //{
            //    List<WindPark> myParkList = DevTreeManagement.GetWindParkList();
            //    if (myParkList.Count > 0)
            //    {
            //        windParkID = myParkList.OrderBy(i => i.WindParkID).FirstOrDefault().WindParkID;
            //    }
            //}
            //判断角色权限
            ViewData["author"] = "show";
            ViewData["ParkID"] = windParkID;
            //DAUManager dauManager = new DAUManager();
            //ViewData["DAUInfoTable"] = dauManager.GetDAUList(windParkID).ToJson();
            //使用ViewBag.pageType此处会出错，因此改用ViewData["pageType"]
            ViewData["pageType"] = "view";
            WindPark windPark = DevTreeManagement.GetWindPark(windParkID);
            ViewData["WindTurbineName"] = windPark.WindTurbineList.Find(item => item.WindTurbineID == turbineID).WindTurbineName;
            List<AlarmStatus_MeasLocSVM> svmMeasLocList = SVMManagement.GetSVMeasLocStateListByWindTurbine(turbineID);
            AlarmStatus_MeasLocSVM svmLoc = svmMeasLocList.FindAll(loc => loc.WindTurbineID == turbineID).OrderByDescending(loc => loc.AlarmUpdateTime).ThenByDescending(loc => loc.AlarmDegree).FirstOrDefault();
            if (svmLoc != null)
            {
                ViewData["AlarmUpdateTime"] = svmLoc.AlarmUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
                ViewData["AlarmDegree"] = AppFramework.Utility.EnumHelper.GetDescription(svmLoc.AlarmDegree);
            }
            if (windPark == null)
            {
                ViewData["NodePath"] = Utility.ToJson(new object[]
            {
                new []{Resources.Message.CompanyName,"/DeviceOverView/Index"}
            });
            }
            else
            {
                ViewData["NodePath"] = Utility.ToJson(new object[]
            {
                new []{Resources.Message.CompanyName,"/DeviceOverView/Index"},
                new []{windPark.WindParkName,"/WindTurbine/SVMonitorDetail/"+windParkID}
            });
            }
            TreeManager treeManager = new TreeManager();
            ViewData["leftTree"] = treeManager.GetTreeModel().ToJson();
            ViewData["info"] = windParkID + "/" + turbineID;
            return View();
        }

        public string GetSVMeasLocMonitorDetailList(string turbineID)
        {
            List<AlarmDefinition> alarmList = AlarmDefinitionManage.GetAlarmDefListByTurID(turbineID);
            //获取实时晃度特征值数据
            List<EigenValueData_SVM> dataList = RealTimeDataManage.GetSVMRTEVDataListByTurID(turbineID);
            //获取晃度仪测量位置
            List<MeasLoc_SVM> measLocList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID).OrderBy(item => item.OrderSeq).ToList();
            //获取晃度仪状态
            List<AlarmStatus_MeasLocSVM> svmMeasLocList = SVMManagement.GetSVMeasLocStateListByWindTurbine(turbineID);
            //获取工况数据
            List<WorkingConditionData> workConditionList = RealTimeDataManage.GetAllWorkingCondDataListByTurbineID(turbineID);
            List<SVMonitorMeasLocEntity> svmonitorMeasLocEntity = new List<SVMonitorMeasLocEntity>();
            measLocList.ForEach(item =>
            {
                AlarmStatus_MeasLocSVM svmLoc = svmMeasLocList.FindAll(loc => loc.MeasLocationID == item.MeasLocationID).OrderByDescending(loc => loc.AlarmUpdateTime).FirstOrDefault();
                if (svmLoc != null)
                {
                    EigenValueData_SVM svmEigen = dataList.Where(eigen => eigen.MeasLocationID == item.MeasLocationID && eigen.AcquisitionTime == svmLoc.AlarmUpdateTime && eigen.AlarmDegree == svmLoc.AlarmDegree).FirstOrDefault();
                    if (svmEigen != null)
                    {
                        WorkingConditionData workCondition = workConditionList.Where(work => work.MeasDefinitionID == svmEigen.MeasDefinitionID).FirstOrDefault();
                        if (workCondition != null)
                        {
                            //获取晃度测量位置下的报警定义阈值
                            AlarmDefinition alarmDef = alarmList.Find(alarm => alarm.MeasLocationID == item.MeasLocationID && alarm.WorkConParameter == (short)workCondition.Param_Type_Code);
                            if (alarmDef != null)
                            {
                                List<AlarmDefThreshold> alarmDefThreshold = alarmDef.AlarmDefThresholdGroup;
                                if (alarmDefThreshold != null && alarmDefThreshold.Count > 0)
                                {
                                    SVMonitorMeasLocEntity svmEntity = new SVMonitorMeasLocEntity()
                                    {
                                        alarmValue = (double)alarmDefThreshold.Find(def => def.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm).ThresholdValue,
                                        warnValue = (double)alarmDefThreshold.Find(def => def.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning).ThresholdValue,
                                        eigenValue = svmEigen.Eigen_Value,
                                        EigenName = AppFramework.Utility.EnumHelper.GetDescription(svmEigen.EigenValueType),
                                        MeasLocName = item.MeasLocName,
                                        UnitName = "°"
                                    };
                                    if (item.MeasLocName.IndexOf("加速度") > -1)
                                    {
                                        svmEntity.alarmValue = svmEntity.alarmValue * 1000;
                                        svmEntity.eigenValue = svmEntity.eigenValue * 1000;
                                        svmEntity.warnValue = svmEntity.warnValue * 1000;
                                        svmEntity.UnitName = "mg";
                                    }
                                    svmEntity.alarmValue = Math.Round(svmEntity.alarmValue, 2);
                                    svmEntity.eigenValue = Math.Round(svmEntity.eigenValue, 2);
                                    svmEntity.warnValue = Math.Round(svmEntity.warnValue, 2);
                                    //设置仪表盘量程
                                    double minvalue = Math.Min(Math.Min(svmEntity.alarmValue, svmEntity.eigenValue), svmEntity.warnValue);
                                    double maxvalue = Math.Max(Math.Max(svmEntity.alarmValue, svmEntity.eigenValue), svmEntity.warnValue);
                                    double alarm_range = svmEntity.alarmValue - svmEntity.warnValue;
                                    minvalue= Math.Round(svmEntity.warnValue-alarm_range, 2);
                                    maxvalue = Math.Round(svmEntity.alarmValue + alarm_range, 2);
                                    if (svmEntity.eigenValue < svmEntity.warnValue && (svmEntity.warnValue - svmEntity.eigenValue) > alarm_range)
                                    {
                                        minvalue = svmEntity.eigenValue - (svmEntity.warnValue - svmEntity.eigenValue);
                                    }
                                    if (svmEntity.eigenValue > svmEntity.alarmValue && (svmEntity.eigenValue - svmEntity.alarmValue) > alarm_range)
                                    {
                                        maxvalue = svmEntity.eigenValue + (svmEntity.eigenValue - svmEntity.alarmValue);
                                    }
                                    svmEntity.minvalue = minvalue;
                                    svmEntity.maxvalue = maxvalue;
                                    double sumValue = svmEntity.alarmValue + svmEntity.maxvalue + svmEntity.warnValue;
                                    svmEntity.EigenValueList = new List<List<string>>(){
                                      new List<string>()
                                      {
                                          ((svmEntity.warnValue-svmEntity.minvalue)/(Math.Abs(svmEntity.maxvalue)+Math.Abs(minvalue))).ToString("0.00"),
                                          "#228b22"
                                      },
                                      new List<string>()
                                      {
                                          //((svmEntity.alarmValue+svmEntity.eigenValue+svmEntity.warnValue)- minvalue-maxvalue).ToString("0.00"),
                                          ((svmEntity.alarmValue-svmEntity.minvalue)/(Math.Abs(svmEntity.maxvalue)+Math.Abs(minvalue))).ToString("0.00"),
                                          "#FB0"
                                      },
                                      new List<string>()
                                      {
                                          "1",
                                          "#ff4500"
                                      }
                                    };
                                    svmonitorMeasLocEntity.Add(svmEntity);
                                }
                            }
                        }
                    }
                }
            });
            string MonitorEntity = svmonitorMeasLocEntity.ToJson();
            return MonitorEntity;
        }

        #endregion
    }
}
