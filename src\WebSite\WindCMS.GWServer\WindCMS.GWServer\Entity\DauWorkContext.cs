﻿using System.ComponentModel;
using CMSFramework.BusinessEntity;
using Newtonsoft.Json;
using WTCMSLive.DAUFacade;

namespace WindCMS.GWServer.Entity;

public class DauWorkContext : IDauWorkContext
{
    public DauWorkContext()
    {
        PumpSetting = new PumpDataSetting();
        Token = CancellationToken.None;
    }

    private DateTime logTime;
    private DateTime daqStartTime;

    [JsonIgnore]
    public IDAUFacade DauFacade { get; set; }

    [JsonIgnore]
    public IDauMeasDataPump? DataPump { get; set; }

    public List<MeasDefinition> MDFList { get; set; }

    /// <summary>
    /// 所有启用的测量定义 MDFList是本次采集需要使用到的测量定义，MDFList是AllEnableMdf子集
    /// </summary>
    public List<MeasDefinition> AllEnableMdf { get; set; }

    public WindTurbine DauTurbine { get; set; }

    public WindDAU? Dau { get; set; }

    public List<SVMUnit> SvmUnitList { get; set; }

    public List<OilUnit> OilUnitList { get; set; }

    public MCS MainCtrlSys { get; set; }

    public PumpDataSetting PumpSetting { get; set; }

    /// <summary>
    /// 任务令牌
    /// </summary>
    public CancellationToken Token { get; set; }

    /// <summary>
    /// DAU 工作状态
    /// </summary>
    public EnumDauWorkState WorkState { get; set; }
}

/// <summary>
/// DAU工作状态
/// </summary>
public enum EnumDauWorkState
{
    [Description("空闲状态")]
    Idle =1, 

    [Description("采集状态")]
    Daqing = 2,

    [Description("异常状态")]
    Error = 3
}