using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// DAU配置队列服务接口
    /// </summary>
    public interface IDAUConfigQueueService
    {
        /// <summary>
        /// 将DAU配置数据加入队列
        /// </summary>
        /// <param name="config">DAU配置数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task EnqueueAsync(DAUConfigDTO config, CancellationToken cancellationToken = default);

        /// <summary>
        /// 从队列中取出DAU配置数据
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>DAU配置数据</returns>
        ValueTask<DAUConfigDTO> DequeueAsync(CancellationToken cancellationToken = default);
    }
}

