﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel.TIM;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using System.Diagnostics;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace WTCMSLive.WebSite.Core.Models
{
    public class TurbineExprotManager
    {

        public static TemplateTurbineModel? TurbineConfigExport(string WindTurbineID)
        {
            try
            {
                TemplateTurbineModel tur = new TemplateTurbineModel();

                // 机组设备树信息
                var turInfo = DevTreeManagement.GetAllWindTurbineOverLoad(WindTurbineID);
                //var turInfo = DevTreeManagement.GetWindTurbine(tt.WindTurbineID);

                tur.WindParkID = turInfo.WindParkID;
                tur.WindTurbineID = turInfo.WindTurbineID;
                tur.WindTurbineCode = turInfo.WindTurbineCode;
                tur.WindTurbineModel = turInfo.WindTurbineModel;
                tur.WindTurbineName = turInfo.WindTurbineName;
                tur.OperationalDate = turInfo.OperationalDate;
                tur.MinWorkingRotSpeed = turInfo.MinWorkingRotSpeed;
                tur.ConfigVersion = turInfo.ConfigVersion;

                tur.DevWindPark = turInfo.DevWindPark;
                tur.WTurbineModel = turInfo.WTurbineModel;
                tur.VibMeasLocList = turInfo.VibMeasLocList;
                tur.DevMeasLocRotSpds = turInfo.DevMeasLocRotSpds;
                tur.ProcessMeasLocList = turInfo.ProcessMeasLocList;
                tur.TurComponentList = turInfo.TurComponentList;
                tur.MeasLocSVMList = turInfo.MeasLocSVMList;

                tur.VoltageCurrentMeasLocList = turInfo.VoltageCurrentMeasLocList;

                // 主控信息
                tur.Mcs = DAUMCS.GetMCSByTurbineId(WindTurbineID);
                tur.MCSChannelStateParamList = DAUMCS.GetMCSChannelStateListByTurID(WindTurbineID);
                tur.MCSChannelValueParamList = DAUMCS.GetMCSChannelValueListByTurID(WindTurbineID);

                // 采集单元
                tur.WindDAUList = DAUSManageModel.GetDAUListById(WindTurbineID);

                // 测量定义
                tur.MeasDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurIdOverload(WindTurbineID);
                tur.MeasDefinition_ExList = MeasDefinitionManagement.GetMeasdefinitionEXListByTurID(WindTurbineID);

                List<MeasSolution> measSolutionlist = MeasDefinitionManagement.GetMeasSolution(WindTurbineID);

                tur.MeasDefinitionList.ForEach(m =>
                {
                    m.Mdf_Ex = tur.MeasDefinition_ExList.FirstOrDefault(k => k.MeasDefinitionID == m.MeasDefinitionID);
                    m.SolutionList = measSolutionlist.Where(k => k.MeasDefinitionID == m.MeasDefinitionID).ToList();

                    m.WaveDefList.ForEach(ww =>
                    {

                        if (ww.WaveFormType == EnumWaveFormType.WDF_Envelope)
                        {
                            if (m.WaveDefList_Envelope.FirstOrDefault(_wdfe => _wdfe.WaveDefParamID == ww.WaveDefinitionID) == null)
                            {
                                m.WaveDefList_Envelope.Add(MeasDefinitionManagement.GetWaveDefById_Envlope(ww.WindTurbineID, ww.WaveDefinitionID));
                            }

                        }
                        else if (ww.WaveFormType == EnumWaveFormType.WDF_Time)
                        {
                            if (m.WaveDefList_Time.FirstOrDefault(_wtime => _wtime.WaveDefParamID == ww.WaveDefParamID) == null)
                            {
                                m.WaveDefList_Time.Add(MeasDefinitionManagement.GetWaveDefById_Time(ww.WindTurbineID, ww.WaveDefinitionID));
                            }

                        }
                    });
                });

                // modbus设备

                //添加modbusUnit表
                tur.modbusDefslist = TIMManagement.GetModbusunitList(WindTurbineID);
                //服务表
                tur.serialServerslist = TIMManagement.GetSerialServer(WindTurbineID);
                tur.serialPorts = TIMManagement.GetSerialPortParam(WindTurbineID);
                //timcalibration表添加
                tur.timUnitList = TIMManagement.GetTimUnit(WindTurbineID);

                //油液添加
                tur.oilUnitList = TIMManagement.GetOilconfigByturID(WindTurbineID);


                tur.modbusDefs = TIMManagement.GetModbusDef(WindTurbineID);



                // svmunit
                tur.svmuList = SVMManagement.GetSVMUnitListByWindTurbineID(WindTurbineID);
                tur.svmRegisterList = SVMManagement.GetSVMRegisterByturbineID(WindTurbineID);
                //SVMManagement.AddSVMRegister(svmDataList);
                //测试开始
                tur.svmMeasDataList = SVMManagement.GetMeasLoc_SVMListByTurID(WindTurbineID);

                tur.waveDef_SVMsList = SVMManagement.GetWaveDef_sData(WindTurbineID);



                //tur.oilAnalyzeConfigs = GetOilAnalyseSettingLists(tt.WindTurbineID);

                //保存超声螺栓预紧力配置
                tur.ultrasonicList = DauManagement.GetUltrasonicChannelConfigByTurId(WindTurbineID);

                // 获取报警配置
                //获取报警定义
                tur.AlarmDefinitions = AlarmDefinitionManage.GetAlarmDefListByTurID(WindTurbineID);

                // 获取sftp config和高级配置
                tur.dauSftpConfigs = DauManagement.GetDAUSftpConfigByTurbineID(WindTurbineID);
                tur.dauExtendConfigs = DauManagement.GetDauExtendConfig(WindTurbineID);

                return tur;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static bool TurbineConfigImport(TemplateTurbineModel templateTurbineModel)
        {
            try
            {
                // 读取配置文件，开始写入
                // 先写入风场
                WindPark _pakr = DevTreeManagement.GetWindParkByParkID(templateTurbineModel.WindParkID);
                if(_pakr == null )
                {
                    DevTreeManagement.AddWindPark(templateTurbineModel.DevWindPark);
                }

                // 1. 删除旧机组
                WindTurbine _windTurbine = DevTreeManagement.GetWindTurbine(templateTurbineModel.WindTurbineID);
                if (_windTurbine != null)
                {
                    DevTree.DeleteTurbineByID(_windTurbine.WindTurbineID);

                }

                // 2. 写入新配置

                // 1) 设备树信息
                WindTurbine newTurbine = new WindTurbine()
                {
                    WindParkID = templateTurbineModel.WindParkID,
                    WindTurbineCode = templateTurbineModel.WindTurbineCode,
                    WindTurbineID = templateTurbineModel.WindTurbineID,
                    WindTurbineName = templateTurbineModel.WindTurbineName,
                    WindTurbineModel = templateTurbineModel.WindTurbineModel,
                    OperationalDate = templateTurbineModel.OperationalDate.ToLocalTime(),
                    MinWorkingRotSpeed = templateTurbineModel.MinWorkingRotSpeed,
                    TurComponentList = templateTurbineModel.TurComponentList,
                    DevMeasLocRotSpds = templateTurbineModel.DevMeasLocRotSpds,
                    VibMeasLocList = templateTurbineModel.VibMeasLocList,
                    ProcessMeasLocList = templateTurbineModel.ProcessMeasLocList,
                    ConfigVersion = templateTurbineModel.ConfigVersion,

                    VoltageCurrentMeasLocList = templateTurbineModel.VoltageCurrentMeasLocList,
                };

                if (newTurbine.VibMeasLocList.Count > 0)
                {
                    foreach (var t in newTurbine.VibMeasLocList)
                    {
                        t.DevTurComponent = null;
                    }
                }

                if (newTurbine.VoltageCurrentMeasLocList.Count > 0)
                {
                    foreach (var t in newTurbine.VoltageCurrentMeasLocList)
                    {
                        t.DevTurComponent = null;
                    }
                }
                DevTreeManagement.AddWindTurbine_ManagerOverLoad(newTurbine);

                // 2) 采集单元
                if (templateTurbineModel.WindDAUList != null && templateTurbineModel.WindDAUList.Count > 0)
                {
                    foreach (WindDAU dau in templateTurbineModel.WindDAUList)
                    {
                        dau.MeasDefVersion = 0;
                        dau.DAUMeasDefVersion = 0;
                        DauManagement.AddDAU(dau);
                    }
                }

                // 3) z主控信息
                if (templateTurbineModel.Mcs != null)
                {
                    DAUMCS.AddMCS(templateTurbineModel.Mcs);
                    if (templateTurbineModel.MCSChannelStateParamList != null && templateTurbineModel.MCSChannelStateParamList.Count > 0)
                    {
                        DAUMCS.AddMCSChannelState(templateTurbineModel.MCSChannelStateParamList);
                    }

                    if (templateTurbineModel.MCSChannelValueParamList != null && templateTurbineModel.MCSChannelValueParamList.Count > 0)
                    {
                        DAUMCS.AddMCSChannelValue(templateTurbineModel.MCSChannelValueParamList);
                    }
                }

                // 4) 保存测量定义
                if (templateTurbineModel.MeasDefinitionList != null && templateTurbineModel.MeasDefinitionList.Count > 0)
                {
                    // 保存波形参数

                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {

                        //var maxTime = ctx.WDFParamTimes.AsEnumerable().Max(_dd =>int.Parse(_dd.WaveDefParamID));
                        var maxTime = ctx.WDFParamTimes
                                        .Select(_dd => new { Value = _dd.WaveDefParamID })
                                        .AsEnumerable()
                                        .Select(_dd => int.TryParse(_dd.Value, out var num) ? num : 0)
                                        .DefaultIfEmpty()
                                        .Max();
                        var maxEnvlope = ctx.WDFParamEnvlopes.Select(_dd => new { Value = _dd.WaveDefParamID })
                                        .AsEnumerable()
                                        .Select(_dd => int.TryParse(_dd.Value, out var num) ? num : 0)
                                        .DefaultIfEmpty()
                                        .Max();

                        int _maxTime = Convert.ToInt32(maxTime);
                        int _maxEnvlope = Convert.ToInt32(maxEnvlope);

                        Dictionary<string, string> measIDMap = new Dictionary<string, string>();
                        templateTurbineModel.MeasDefinitionList.ForEach(mm =>
                        {
                            var oldMeasDefinitionID = mm.MeasDefinitionID;
                            // 重写测量定义id
                            int measDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.MeasDefinitionID);
                            measIDMap.Add(mm.MeasDefinitionID, measDefinitionID.ToString());

                            var _ex = templateTurbineModel.MeasDefinition_ExList.FirstOrDefault(mdfex => mdfex.MeasDefinitionID == mm.MeasDefinitionID);
                            if (_ex != null) { _ex.MeasDefinitionID = measDefinitionID.ToString(); }

                            //item.ProcessDefList = GetMDFWorkCondLocListByMeasDefId(item.WindTurbineID, item.MeasDefinitionID);
                            //item.RotSpdWaveDefList = GetWaveDefListRotSpd(item.WindTurbineID, item.MeasDefinitionID);
                            //item.SVMWaveDefinition = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(item.WindTurbineID, item.MeasDefinitionID);
                            //item.WaveDefList = GetWaveDefByTurId(item.WindTurbineID, item.MeasDefinitionID);

                            //// 特征值
                            //item.VibEigenValueConf = EigenValueManage.GetMdfTimeDomainEvConf(item.WindTurbineID, item.MeasDefinitionID);
                            //item.ProcessSuperviseDefList = EigenValueManage.GetEigenValueProcess(item.WindTurbineID, item.MeasDefinitionID);

                            //// 触发采集
                            //item.TriggerRules = TriggerManager.GetMeasTriggerRuleDefs(item.WindTurbineID, item.MeasDefinitionID);
                            if (mm.RotSpdWaveDefList != null && mm.RotSpdWaveDefList.Count > 0)
                            {
                                for (int i = 0; i < mm.RotSpdWaveDefList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.RotSpdWaveDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                }
                            }

                            if (mm.SVMWaveDefinition != null && mm.SVMWaveDefinition.Count > 0)
                            {
                                for (int i = 0; i < mm.SVMWaveDefinition.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.SVMWaveDefinition[i].MeasDefinitionID = measDefinitionID.ToString();
                                }
                            }
                            if (mm.WaveDefList != null && mm.WaveDefList.Count > 0)
                            {
                                for (int i = 0; i < mm.WaveDefList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    var oldWaveDefid = mm.WaveDefList[i].WaveDefinitionID;
                                    var newWaveDefid = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID);
                                    mm.WaveDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                    mm.WaveDefList[i].WaveDefinitionID = newWaveDefid.ToString();

                                    var _evconfigs = mm.VibEigenValueConf.Where(t => t.WaveDefinitionID == oldWaveDefid);
                                    foreach (var e in _evconfigs)
                                    {
                                        e.WaveDefinitionID = newWaveDefid.ToString();
                                    }

                                }
                            }

                            if (mm.WaveDefVoltageCurrentList != null && mm.WaveDefVoltageCurrentList.Count > 0)
                            {
                                for (int i = 0; i < mm.WaveDefVoltageCurrentList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    var oldWaveDefid = mm.WaveDefVoltageCurrentList[i].WaveDefinitionID;
                                    var newWaveDefid = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID);
                                    mm.WaveDefVoltageCurrentList[i].MeasDefinitionID = measDefinitionID.ToString();
                                    mm.WaveDefVoltageCurrentList[i].WaveDefinitionID = newWaveDefid.ToString();

                                    var _evconfigs = mm.VibEigenValueConf.Where(t => t.WaveDefinitionID == oldWaveDefid);
                                    foreach (var e in _evconfigs)
                                    {
                                        e.WaveDefinitionID = newWaveDefid.ToString();
                                    }

                                }
                            }

                            if (mm.VibEigenValueConf != null && mm.VibEigenValueConf.Count > 0)
                            {
                                for (int i = 0; i < mm.VibEigenValueConf.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.VibEigenValueConf[i].MeasDefinitionID = measDefinitionID.ToString();
                                    //mm.VibEigenValueConf[i].EvId = 0;
                                }
                            }
                            if (mm.ProcessSuperviseDefList != null && mm.ProcessSuperviseDefList.Count > 0)
                            {
                                for (int i = 0; i < mm.ProcessSuperviseDefList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.ProcessSuperviseDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                }
                            }
                            if (mm.TriggerRules != null && mm.TriggerRules.Count > 0)
                            {
                                for (int i = 0; i < mm.TriggerRules.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.TriggerRules[i].MeasDefinitionID = measDefinitionID.ToString();

                                    if (mm.TriggerRules[i].ExecuteMdfs != null && mm.TriggerRules[i].ExecuteMdfs.Count > 0)
                                    {
                                        var _exMdf = mm.TriggerRules[i].ExecuteMdfs.Where(tre => tre.MeasDefinitionID == mm.MeasDefinitionID).ToList();
                                        if (_exMdf.Count > 0)
                                        {
                                            for (int pp = 0; pp < _exMdf.Count; pp++)
                                            {
                                                // 修改指定字段的值
                                                _exMdf[pp].MeasDefinitionID = measDefinitionID.ToString();
                                            }
                                        }
                                    }
                                }
                            }


                            if (mm.ProcessDefList != null && mm.ProcessDefList.Count > 0)
                            {
                                for (int i = 0; i < mm.ProcessDefList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.ProcessDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                }
                            }

                            if (mm.SolutionList != null && mm.SolutionList.Count > 0)
                            {
                                for (int i = 0; i < mm.SolutionList.Count; i++)
                                {
                                    // 修改指定字段的值
                                    mm.SolutionList[i].MeasDefinitionID = measDefinitionID.ToString();
                                }

                            }

                            if (templateTurbineModel.waveDef_SVMsList != null && templateTurbineModel.waveDef_SVMsList.Count > 0)
                            {
                                var _svmWave = templateTurbineModel.waveDef_SVMsList.Where(_wSvm => _wSvm.MeasDefinitionID == mm.MeasDefinitionID).ToList();
                                if (_svmWave.Count > 0)
                                {
                                    for (int i = 0; i < _svmWave.Count; i++)
                                    {
                                        // 修改指定字段的值
                                        _svmWave[i].MeasDefinitionID = measDefinitionID.ToString();
                                        _svmWave[i].WaveDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID).ToString();
                                    }
                                }
                            }

                            mm.MeasDefinitionID = measDefinitionID.ToString();

                            if (mm.WaveDefList_Envelope != null && mm.WaveDefList_Envelope.Count > 0)
                            {
                                mm.WaveDefList_Envelope.ForEach(en =>
                                {
                                    var _wdflist = mm.WaveDefList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                                    var ParameEnvlope = ctx.WDFParamEnvlopes.FirstOrDefault(wdfenv => wdfenv.EnvBandWidth == en.EnvBandWidth && wdfenv.EnvFiterFreq == en.EnvFiterFreq && wdfenv.SampleLength == en.SampleLength);
                                    if (ParameEnvlope != null)
                                    {

                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = ParameEnvlope.WaveDefParamID);

                                    }
                                    else
                                    {
                                        _maxEnvlope++;
                                        en.WaveDefParamID = _maxEnvlope.ToString();
                                        ctx.WDFParamEnvlopes.Add(new WaveDefParam_Envlope()
                                        {

                                            EnvBandWidth = en.EnvBandWidth,
                                            EnvFiterFreq = en.EnvFiterFreq,
                                            SampleLength = en.SampleLength,
                                            WaveDefParamID = en.WaveDefParamID,
                                            WaveDefParamName = en.WaveDefinitionName,
                                        });
                                        ctx.SaveChanges();

                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                                    }
                                });
                            }


                            if (mm.WaveDefList_Time != null && mm.WaveDefList_Time.Count > 0)
                            {
                                mm.WaveDefList_Time.ForEach(en =>
                                {
                                    var _wdflist = mm.WaveDefList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                                    var WDFParamTime = ctx.WDFParamTimes.FirstOrDefault(wdfenv => wdfenv.LowerLimitFreqency == en.LowerLimitFreqency && wdfenv.UpperLimitFreqency == en.UpperLimitFreqency && wdfenv.SampleLength == en.SampleLength);
                                    if (WDFParamTime != null)
                                    {

                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = WDFParamTime.WaveDefParamID);

                                    }
                                    else
                                    {
                                        _maxTime++;
                                        en.WaveDefParamID = _maxTime.ToString();
                                        ctx.WDFParamTimes.Add(new WaveDefParam_Time()
                                        {

                                            LowerLimitFreqency = en.LowerLimitFreqency,
                                            UpperLimitFreqency = en.UpperLimitFreqency,
                                            SampleLength = en.SampleLength,
                                            WaveDefParamID = en.WaveDefParamID,
                                            WaveDefParamName = en.WaveDefinitionName,
                                        });
                                        ctx.SaveChanges();

                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                                    }
                                });
                            }

                            //if (mm.WaveDefVoltageCurrentList != null && mm.WaveDefVoltageCurrentList.Count > 0)
                            //{
                            //    mm.WaveDefVoltageCurrentList.ForEach(en =>
                            //    {
                            //        var _wdflist = mm.WaveDefVoltageCurrentList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                            //        var WDFParamTime = ctx.WDFParamTimes.FirstOrDefault(wdfenv => wdfenv.LowerLimitFreqency == en.LowerLimitFreqency && wdfenv.UpperLimitFreqency == en.UpperLimitFreqency && wdfenv.SampleLength == en.SampleLength);
                            //        if (WDFParamTime != null)
                            //        {

                            //            _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = WDFParamTime.WaveDefParamID);

                            //        }
                            //        else
                            //        {
                            //            _maxTime++;
                            //            en.WaveDefParamID = _maxTime.ToString();
                            //            ctx.WDFParamTimes.Add(new WaveDefParam_Time()
                            //            {

                            //                //LowerLimitFreqency = en.LowerLimitFreqency,
                            //                //UpperLimitFreqency = en.UpperLimitFreqency,
                            //                //SampleLength = en.SampleLength,
                            //                WaveDefParamID = en.WaveDefParamID,
                            //                WaveDefParamName = en.WaveDefinitionName,
                            //            });
                            //            ctx.SaveChanges();

                            //            _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                            //        }
                            //    });
                            //}

                            // modbusdef measid重写
                            if (templateTurbineModel.modbusDefs != null && templateTurbineModel.modbusDefs.Count > 0)
                            {
                                var modbusDefs = templateTurbineModel.modbusDefs.Where(_def => _def.MeasDefinitionID == oldMeasDefinitionID);
                                foreach (var _mdf in modbusDefs)
                                {
                                    _mdf.MeasDefinitionID = measDefinitionID.ToString();
                                }
                            }

                        });
                        templateTurbineModel.MeasDefinitionList.ForEach(pk =>
                        {
                            if (pk.TriggerRules != null && pk.TriggerRules.Count > 0)
                            {
                                for (int i = 0; i < pk.TriggerRules.Count; i++)
                                {
                                    if (pk.TriggerRules[i].ExecuteMdfs != null && pk.TriggerRules[i].ExecuteMdfs.Count > 0)
                                    {
                                        var _exMdf = pk.TriggerRules[i].ExecuteMdfs.ToList();
                                        if (_exMdf.Count > 0)
                                        {
                                            for (int pp = 0; pp < _exMdf.Count; pp++)
                                            {
                                                // 修改指定字段的值
                                                if (measIDMap.ContainsKey(_exMdf[pp].MeasDefinitionID))
                                                {
                                                    _exMdf[pp].MeasDefinitionID = measIDMap[_exMdf[pp].MeasDefinitionID];
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        });

                    }
                    MeasDefinitionManagement.AddMeaDefition(templateTurbineModel.MeasDefinitionList);

                }

                if (templateTurbineModel.MeasDefinition_ExList != null && templateTurbineModel.MeasDefinition_ExList.Count > 0)
                {
                    MeasDefinitionManagement.AddMeaDefitionEX(templateTurbineModel.MeasDefinition_ExList);
                }

                // 5) 保存报警
                if (templateTurbineModel.AlarmDefinitions != null && templateTurbineModel.AlarmDefinitions.Count > 0)
                {
                    AlarmDefinitionManage.AddAlarmDefinition(templateTurbineModel.AlarmDefinitions);
                }

                if (templateTurbineModel.modbusDefslist != null && templateTurbineModel.modbusDefslist.Count > 0)
                {
                    templateTurbineModel.modbusDefslist.ForEach(m => { TIMManagement.AddModbusunitAndChannel(m); });
                }


                //服务表
                if (templateTurbineModel.serialServerslist != null && templateTurbineModel.serialServerslist.Count > 0)
                {
                    templateTurbineModel.serialServerslist.ForEach(mm => { TIMManagement.AddSerialServer(mm); });
                }

                if(templateTurbineModel.serialPorts != null && templateTurbineModel.serialPorts.Count > 0)
                {
                    TIMManagement.AddSerialPortParam(templateTurbineModel.serialPorts);
                }

                //timcalibration表添加
                if (templateTurbineModel.timUnitList != null && templateTurbineModel.timUnitList.Count > 0)
                {
                    templateTurbineModel.timUnitList.ForEach(mm =>
                    {
                        mm.CalibAngleUpdateTime = mm.CalibAngleUpdateTime.ToLocalTime();
                        TIMManagement.AddTimUnit(mm);
                    });
                }

                if (templateTurbineModel.modbusDefs != null && templateTurbineModel.modbusDefs.Count > 0)
                {
                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    {
                        ctx.ModbusDefs.AddRange(templateTurbineModel.modbusDefs);
                        ctx.SaveChanges();
                    }
                }


                //油液添加

                if (templateTurbineModel.oilUnitList != null && templateTurbineModel.oilUnitList.Count > 0)
                {
                    TIMManagement.AddOilconfig(templateTurbineModel.oilUnitList);
                }

                // svmunit

                if (templateTurbineModel.svmuList != null && templateTurbineModel.svmuList.Count > 0)
                {
                    templateTurbineModel.svmuList.ForEach(mm => { SVMManagement.AddSVM(mm); });
                }


                if (templateTurbineModel.svmRegisterList != null && templateTurbineModel.svmRegisterList.Count > 0)
                {
                    SVMManagement.AddSVMRegister(templateTurbineModel.svmRegisterList);
                }
                //SVMManagement.AddSVMRegister(svmDataList);
                //测试开始
                if (templateTurbineModel.svmMeasDataList != null && templateTurbineModel.svmMeasDataList.Count > 0)
                {
                    SVMManagement.AddSVMMeasLoc(templateTurbineModel.WindTurbineID, templateTurbineModel.svmMeasDataList);
                }

                if (templateTurbineModel.waveDef_SVMsList != null && templateTurbineModel.waveDef_SVMsList.Count > 0)
                {
                    SVMManagement.AddSVMWaveDef(templateTurbineModel.waveDef_SVMsList);
                }

                //if (item.oilAnalyzeConfigs != null && item.oilAnalyzeConfigs.Count > 0)
                //{
                //    AddOilAnalyseSettingLists(item.oilAnalyzeConfigs);
                //}

                if (templateTurbineModel.ultrasonicList?.Count > 0)
                {
                    templateTurbineModel.ultrasonicList.ForEach(mm => { DauManagement.AddUltrasonic(mm); });
                }


                // 添加sftp配置
                if (templateTurbineModel.dauSftpConfigs != null && templateTurbineModel.dauSftpConfigs.Count > 0)
                {
                    DauManagement.AddDAUSFTPConfig(templateTurbineModel.dauSftpConfigs);
                }

                // 添加dauextendconfig配置
                if (templateTurbineModel.dauExtendConfigs != null && templateTurbineModel.dauExtendConfigs.Count > 0)
                {
                    DauManagement.AddDauExtendConfig(templateTurbineModel.dauExtendConfigs);
                }

                return true;
            }catch(Exception ex) {
                return false;
            }
        }
    }
}
