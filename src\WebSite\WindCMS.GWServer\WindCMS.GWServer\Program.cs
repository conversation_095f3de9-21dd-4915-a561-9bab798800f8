﻿using System.IO.Compression;
using WindCMS.GWServer.ListenerServer;

namespace WindCMS.GWServer
{
    internal class Program
    {
        static void Main(string[] args)
        {
            var thread = new Thread(() =>
            {
                TcpListenerServer server = TcpListenerServer.Instance;
                server.StartAccept();
            });
            thread.Start();

            var tcpListenerAgent = TcpListenerServer.GetTcpListenerAgent();
            while (tcpListenerAgent == null)
            {
                tcpListenerAgent = TcpListenerServer.GetTcpListenerAgent();
                Thread.Sleep(1000);
            }

            var dauConfigExtension = tcpListenerAgent.DauConfigExtension;
            
            // 设置是否换标记
            dauConfigExtension.EquipmentWarranty = false;
            tcpListenerAgent.ListenerWriteHandler.SetDauEquipmentWarranty();
            
            // 设置采集策略
            dauConfigExtension.CvmWaveformAcquisitionInterval = 1;
            dauConfigExtension.CvmEigenvalueAcquisitionInterval = 1;
            dauConfigExtension.BvmWaveformAcquisitionInterval = 1;
            dauConfigExtension.BvmEigenvalueAcquisitionInterval = 1;
            dauConfigExtension.TvmWaveformAcquisitionInterval = 1;
            dauConfigExtension.TvmEigenvalueAcquisitionInterval = 1;
            dauConfigExtension.RecordedDays = 1;
            dauConfigExtension.EnableRecordedDays = true;
            dauConfigExtension.AcquisitionDelayInterval = 1;
            dauConfigExtension.TimeSynchronizationServiceIp = "*******";
            dauConfigExtension.TimeSynchronizationServicePort = 123;
            tcpListenerAgent.ListenerWriteHandler.SetDauCollectionStrategyRequest();
            
            // 设置推送信息
            dauConfigExtension.SftpServiceIpAddress = "*******";
            dauConfigExtension.SftPort = 22;
            dauConfigExtension.SftpUserName = "root";
            dauConfigExtension.SftpPassword = "forlinx";
            dauConfigExtension.CvmSftpPushPath = "/home/<USER>/GwTempData/CMSDATA";
            dauConfigExtension.BvmSftpPushPath = "/home/<USER>/GwTempData/BMSDATA";
            dauConfigExtension.TvmSftpPushPath = "/home/<USER>/GwTempData/TMSDATA";
            tcpListenerAgent.ListenerWriteHandler.SetDauPushParameter();
        }
        
        public static void Zip(string sourceDir, string destZip)
        {
            using (FileStream zipToOpen = new FileStream(destZip, FileMode.Create))
            using (ZipArchive archive = new ZipArchive(zipToOpen, ZipArchiveMode.Create))
            {
                foreach (string file in Directory.GetFiles(sourceDir, "wtlivedb.db", SearchOption.AllDirectories))
                {
                    string relativePath = Path.GetRelativePath(sourceDir, file);
                    archive.CreateEntryFromFile(file, relativePath);
                }
            }
        }
        
        public static void Unzip(string zipPath, string extractPath)
        {
            Directory.CreateDirectory(extractPath);
            ZipFile.ExtractToDirectory(zipPath, extractPath, overwriteFiles: true);
        }
    }
}
