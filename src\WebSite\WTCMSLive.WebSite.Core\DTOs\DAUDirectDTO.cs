﻿using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Core.DTOs
{
    public class DAUDirectDTO
    {
        public string? WindTurbineID { get; set; }
        public string? DauID { get; set; }

        public string? IP { get; set; }

        public int Port { get; set; }
        public string? WindParkID { get; set; }
    }

    /// <summary>
    /// 精简的SFTP目录节点DTO（用于构建树形结构）
    /// </summary>
    public class SftpDirectoryNodeDTO
    {
        /// <summary>
        /// 目录名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 完整路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 是否为目录
        /// </summary>
        public bool IsDirectory { get; set; }

        /// <summary>
        /// 文件大小（仅文件有效）
        /// </summary>
        public long? Size { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// 子项（目录和文件）
        /// </summary>
        public List<SftpDirectoryNodeDTO> Children { get; set; } = new List<SftpDirectoryNodeDTO>();

        /// <summary>
        /// 当前节点下的文件总数（递归统计）
        /// </summary>
        public int TotalFileCount { get; set; }

    }

    /// <summary>
    /// SQLite vibdata表数据响应DTO
    /// </summary>
    public class VibDataResponseDTO
    {
        /// <summary>
        /// 表结构信息
        /// </summary>
        public List<VibDataColumnDTO> Columns { get; set; } = new List<VibDataColumnDTO>();

        /// <summary>
        /// 表数据
        /// </summary>
        public List<Dictionary<string, object?>> Data { get; set; } = new List<Dictionary<string, object?>>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// vibdata表列信息DTO
    /// </summary>
    public class VibDataColumnDTO
    {
        /// <summary>
        /// 列名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 是否允许为空
        /// </summary>
        public bool IsNullable { get; set; }

        /// <summary>
        /// 是否为主键
        /// </summary>
        public bool IsPrimaryKey { get; set; }
    }

    public class DAUSFTPConfigDTO: DAUDirectDTO
    {
        public string? SftpAddress { get; set; }

        public string? SftpPort { get; set; }

        public string? SftpUsername { get; set; }

        public string? SftpPassword { get; set; }

        //public string? SftpPushPath { get; set; }

        public string? CvmPushPath { get; set; }

        public string? BvmPushPath { get; set; }

        public string? TvmPushPath { get; set; }
    }

    public class AdvancedParameterDTO : DAUDirectDTO
    {
        /// <summary>
        /// 对时服务器IP
        /// </summary>
        public string? TimeServerIP { get; set; }
        public ushort? TimeServerPort { get; set; }
        public ushort? RecordedDays { get; set; }
        public bool? EnableRecordedDays { get; set; }
    }
}
