﻿using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using AppFramework.Utility;
using AppFramework.IDUtility;

namespace WTCMSLive.WebSite.Models
{
    //机组配置信息相关
    public class TurbineConfigManager
    {
        #region 获取晃度测量位置名称
        public BaseTableModel GetMeasLoc_SVMList(string _SVMID)
        {
            List<MeasLoc_SVM> SvmList = SVMManagement.GetMeasLoc_SVMListByTurID(_SVMID);
            List<WindTurbineComponent> complist = DevTreeManagement.GetComListByTurbineId(_SVMID);
            
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "MeasLoc_RotSpd_Table";
            List<Rows> rowsList = new List<Rows>();
            //foreach (EnumSVMParamType svmdata in Enum.GetValues(typeof(EnumSVMParamType)))
            //{
            //    MeasLoc_SVM svm = SvmList.Find(item => item.MeasLocName == AppFramework.Utility.EnumHelper.GetDescription(svmdata));
                
            //    if (svm != null)
            //    {
            //        WindTurbineComponent comp = complist.FirstOrDefault(k => k.ComponentID == svm.ComponentID);
            //        Dictionary<string, string> list = CodeProvide.GetSectionDic(comp.ComponentName);

            //        SVMRegister svmRegister = SVMManagement.GetSVMRegisterByMeasLocId(svm.MeasLocationID);
            //        Rows row = new Rows();
            //        row.cells = GetMeasLoc_SVMListCell(svm, svmRegister, comp, list);
            //        rowsList.Add(row);
            //    }
            //}
            SvmList.ForEach(item =>
            {
                WindTurbineComponent comp = complist.FirstOrDefault(k => k.ComponentID == item.ComponentID);
                Dictionary<string, string> list = CodeProvide.GetSectionDic(comp.ComponentName);

                SVMRegister svmRegister = SVMManagement.GetSVMRegisterByMeasLocId(item.MeasLocationID);
                Rows row = new Rows();
                row.cells = GetMeasLoc_SVMListCell(item, svmRegister, comp, list);
                rowsList.Add(row);
            });

            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }
        private Cell[] GetMeasLoc_SVMListCell(MeasLoc_SVM measLoc_svm, SVMRegister svmRegister, WindTurbineComponent comp, Dictionary<string, string> dic)
        {
            List<Cell> cellList = new List<Cell>();
            //晃度仪测量位置名称 	
            Cell cell0 = new Cell();
            cell0.displayValue = measLoc_svm.MeasLocName;
            // 部件
            Cell cell6 = new Cell();
            cell6.displayValue = comp.ComponentName;
            // 部件
            Cell cell7 = new Cell();
            cell7.displayValue = dic.FirstOrDefault(k=>k.Value == measLoc_svm.SectionName).Key;
            //方向
            Cell cell1 = new Cell();
            cell1.displayValue = EnumHelper.GetDescription(measLoc_svm.ParamType);
            //数据类型 	
            //Cell cell2 = new Cell();
            //cell2.displayValue = EnumHelper.GetDescription(measLoc_svm.ParamType);
            //寄存器地址
            Cell cell3 = new Cell();
            if (svmRegister == null)
            {
                cell3.displayValue = "";
            }
            else {
                cell3.displayValue = svmRegister.SVMRegisterAdr;
            }
            cellList.Add(cell0);
            cellList.Add(cell1);
            cellList.Add(cell6);
            cellList.Add(cell7);
            //cellList.Add(cell2);
            cellList.Add(cell3);
            return cellList.ToArray();
        }
        #endregion

        #region 取得主控配置列表
        ///<summary>
        /// </summary>
        /// 增加按风场筛选参数 wangy 2015年6月25日 
        public BaseTableModel GetMCSInfoList(bool edit, string windParkID)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindTurbine> mcsManagerlist = DevTreeManagement.GetTurbinesListByWindParkId(windParkID);
            List<MCS> mcsList = DAUMCS.GetMCSByWindParkId(windParkID);
            tableModel.tableName = "MCSInfoTable";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < mcsManagerlist.Count; i++)
            {
                WindTurbine mcsManager=mcsManagerlist[i];
                MCS mcs = mcsList.Find(item=>item.WindTurbineID==mcsManager.WindTurbineID);
                if (mcs == null)
                    continue;
                Rows cells = new Rows();
                cells.cells = CreateMCSManagerListTableCell(mcsManager, mcs, edit);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        private Cell[] CreateMCSManagerListTableCell(WindTurbine mcsManager,MCS mcs, bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //机组ID
            Cell cell01 = new Cell();
            cell01.displayValue = mcsManager.WindTurbineID;
            //机组名称
            Cell cell02 = new Cell();
            cell02.displayValue = mcsManager.WindTurbineName;
            //主控IP
            Cell cell03 = new Cell();
            cell03.displayValue = mcs.MCSIP;
            //端口号
            Cell cell04 = new Cell();
            cell04.displayValue = mcs.MCSPort.ToString();
            //编辑
            Cell cell05 = new Cell();
            cell05.type = "btn";
            cell05.displayValue = "编辑";
            cell05.onclick = "editMCS('" + mcsManager.WindTurbineID + "')";
            cell05.style = "btn btn-primary btn-sm btnEdit";
            cellList.Add(cell01);
            cellList.Add(cell02);
            cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            return cellList.ToArray();
        }

        //主控数据寄存器列表
        public BaseTableModel GetMCSDataInfoTable(string windTurbineID)
        {
            WindTurbine windTurbineMCS = DevTreeManagement.GetAllWindTurbine(windTurbineID);
            List<MCSChannelValueParam> McsChannelValueList = DAUMCS.GetMCSChannelValueListByTurID(windTurbineID);

            // 兼容之前数据
            McsChannelValueList.ForEach(i =>
            {
                if (i.ParamMeaning == "发电机转速")
                    i.ParamMeaning = "发电机转速(MCS)";
            });

            List<MCSChannelValueParam> McsChannelValueOrderList = new List<MCSChannelValueParam>();
            //排序工况测量位置列表
            List<MeasLoc_Process> processList = new List<MeasLoc_Process>();
            foreach (string processName in DevTree.GetMeasLocProcessOrderSeqList())
            {

                MCSChannelValueParam measLocProcess = McsChannelValueList.Find(item => item.ParamMeaning == processName);
                if (measLocProcess != null)
                {
                    McsChannelValueOrderList.Add(measLocProcess);
                }
            }
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "MCSDataInfoTable";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < McsChannelValueOrderList.Count; i++)
            {
                MCSChannelValueParam McsChannelValue=McsChannelValueOrderList[i];
                MeasLoc_Process process = windTurbineMCS.ProcessMeasLocList.Find(item => item.MeasLocationID == McsChannelValue.MeasLocProcessID);
                Rows cells = new Rows();
                cells.cells = CreateMCSDataInfoTableCell(McsChannelValue, process);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        private Cell[] CreateMCSDataInfoTableCell(MCSChannelValueParam McsChannelValue,MeasLoc_Process process)
        {
            List<Cell> cellList = new List<Cell>();
            //寄存器地址
            Cell cell01 = new Cell();
            cell01.displayValue = McsChannelValue.ChannelNumber;
            //参数含义
            Cell cell02 = new Cell();
            cell02.displayValue = McsChannelValue.ParamMeaning;
            //工况测量位置
            Cell cell03 = new Cell();
            cell03.displayValue = process.MeasLocName;
            //寄存器类型
            Cell cell04 = new Cell();
            cell04.displayValue = EnumHelper.GetDescription((EnumMCSChannelType)McsChannelValue.RegisterType);
            //数据存储方式
            Cell cell05 = new Cell();
            cell05.displayValue = EnumHelper.GetDescription((EnumMCSChannelStorageType)McsChannelValue.RegisterStorageType);
            //对齐方式
            Cell cell06 = new Cell();
            cell06.displayValue = EnumHelper.GetDescription((EnumMCSChannelByteArrayType)McsChannelValue.ByteArrayType); 
            //转换系数
            Cell cell07 = new Cell();
            cell07.displayValue = McsChannelValue.Coeff.ToString();
            //工程单位
            Cell cell08 = new Cell();
            cell08.displayValue ="";//工程单位不知道取哪个数据

            cellList.Add(cell01);
            cellList.Add(cell02);
            cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            cellList.Add(cell06);
            cellList.Add(cell07);
            cellList.Add(cell08);

            Cell cell09 = new Cell();
            cell09.type = "btn";
            cell09.displayValue = "编辑";
            cell09.onclick = "editMCS(this,'data','" + McsChannelValue.MeasLocProcessID + "')";
            cell09.style = "btn btn-primary btn-sm btnEdit";
            cellList.Add(cell09);

            Cell cell10 = new Cell();
            cell10.type = "btn";
            cell10.displayValue = "删除";
            cell10.onclick = "DeleteMcs(this,'" + McsChannelValue.WindTurbineID + "','" + McsChannelValue.MeasLocProcessID + "','data')";
            cell10.style = "btn btn-danger btn-sm btnDelete";
            cellList.Add(cell10);

            return cellList.ToArray();
        }
        //主控状态寄存器列表
        public BaseTableModel GetMCSRegisterState(string windTurbineID)
        {
            WindTurbine windTurbineMCS = DevTreeManagement.GetAllWindTurbine(windTurbineID);
            List<MCSChannelStateParam> McsChannelStateParam = DAUMCS.GetMCSChannelStateListByTurID(windTurbineID);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "MCSStatusInfoTable";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < McsChannelStateParam.Count; i++)
            {
                MCSChannelStateParam McsChannelValue=McsChannelStateParam[i];
                MeasLoc_Process process = windTurbineMCS.ProcessMeasLocList.Find(item => item.MeasLocationID == McsChannelValue.MeasLocProcessID);
                Rows cells = new Rows();
                cells.cells = CreateMCSRegisterStateCell(McsChannelValue, process.MeasLocName);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        private Cell[] CreateMCSRegisterStateCell(MCSChannelStateParam McsChannelState, string MeasLocProcessName)
        {
            List<Cell> cellList = new List<Cell>();
            //寄存器地址
            Cell cell01 = new Cell();
            cell01.displayValue = McsChannelState.ChannelNumber;
            //参数含义
            Cell cell02 = new Cell();
            cell02.displayValue = McsChannelState.ParamMeaning;
            //工况测量位置
            Cell cell03 = new Cell();
            cell03.displayValue = MeasLocProcessName;
            //寄存器类型
            Cell cell04 = new Cell();
            cell04.displayValue = EnumHelper.GetDescription((EnumMCSChannelType)McsChannelState.RegisterType);
            //判断模式
            Cell cell05 = new Cell();
            cell05.displayValue = EnumHelper.GetDescription((EnumMCSChannelJudgeType)McsChannelState.JudgeType);
            //状态值
            Cell cell06 = new Cell();
            //string b = "";
            //for (int j = 0; j < McsChannelState.StateValues.Count; j++)
            //{
            //    short a = McsChannelState.StateValues[j];
            //    a.ToString();
            //    b = b + a + ",";
            //}
            cell06.displayValue = string.Join(",", McsChannelState.StateValues);
            cellList.Add(cell01);
            cellList.Add(cell02);
            cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            cellList.Add(cell06);

            Cell cell09 = new Cell();
            cell09.type = "btn";
            cell09.displayValue = "编辑";
            cell09.onclick = "editMCS(this,'state','" + McsChannelState.MeasLocProcessID + "')";
            cell09.style = "btn btn-primary btn-sm btnEdit";
            cellList.Add(cell09);

            Cell cell10 = new Cell();
            cell10.type = "btn";
            cell10.displayValue = "删除";
            cell10.onclick = "DeleteMcs(this,'" + McsChannelState.WindTurbineID + "','" + McsChannelState.MeasLocProcessID + "','state')";
            cell10.style = "btn btn-danger btn-sm btnDelete";
            cellList.Add(cell10);

            return cellList.ToArray();
        }
        #endregion

        #region 振动测量位置名称
        public BaseTableModel GetVibMeasLocationList(string turbineID, bool edit)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "VibTable";
            List<Rows> rowsList = new List<Rows>();
            //获取振动测量位置列表
            List<MeasLoc_Vib> MeasLoc_Vib = DevTreeManagement.GetVibMeasLocationByTurId(turbineID).OrderBy(item=>item.OrderSeq).ToList();
            for (int j = 0; j < MeasLoc_Vib.Count; j++)
            {
                Rows row = new Rows();
                row.cells = GetMeasLoc_VibListCell(MeasLoc_Vib[j], edit);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        private Cell[] GetMeasLoc_VibListCell(MeasLoc_Vib measLoc_vib,bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //振动测量位置名称 	
            Cell cell0 = new Cell();
            cell0.displayValue = measLoc_vib.MeasLocName;
            //所属部件名称 	
            Cell cell1 = new Cell();
            cell1.displayValue = measLoc_vib.DevTurComponent.ComponentName;
            //截面
            Cell cell2 = new Cell();
            cell2.displayValue = measLoc_vib.SectionName;
            //方向
            Cell cell3 = new Cell();
            cell3.displayValue = measLoc_vib.Orientation;
            //变速比
            Cell cell4 = new Cell();
            cell4.displayValue = measLoc_vib.GearRatio.ToString();
            //振动测量位置id
            Cell cell5 = new Cell();
            cell5.displayValue = measLoc_vib.MeasLocationID;
            //转速测量位置id
            Cell cell8 = new Cell();
            cell8.displayValue = "";// measLoc_vib.MeasLocationID;

            //编辑
          /*  Cell cell6 = new Cell();
            if (edit)
            {
                cell6.type = "btn";
                cell6.displayValue = "编辑";
                cell6.onclick = "editVib('" + measLoc_vib.MeasLocationID + "','" + measLoc_vib.ComponentID + "')";
                cell6.style = "btn btn-primary btn-sm";
            }
            else { cell6.displayValue = "-"; }*/
            //删除
            Cell cell7 = new Cell();
            cell7.type = "btn";
            cell7.displayValue = " 删除";
            cell7.onclick = "deleteVib('" + measLoc_vib.MeasLocationID + "',this)";
            cell7.style = "btn btn-danger btn-sm btnDelete";

            cellList.Add(cell0);
            cellList.Add(cell1);
            cellList.Add(cell2);
            cellList.Add(cell3);
            cellList.Add(cell4);
            cellList.Add(cell5);
            cellList.Add(cell8);
            //cellList.Add(cell6);
            cellList.Add(cell7);
            return cellList.ToArray();
        }
        #endregion

        #region 电流电压测量位置

        public BaseTableModel GetProcessMeasLocationList(string turbineID, bool edit)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "VibTable";
            List<Rows> rowsList = new List<Rows>();
            //获取振动测量位置列表
            List<MeasLoc_VoltageCurrent> MeasLoc_Vib = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToList();
            for (int j = 0; j < MeasLoc_Vib.Count; j++)
            {
                Rows row = new Rows();
                row.cells = GetMeasLoc_ProcessListCell(MeasLoc_Vib[j], edit);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        private Cell[] GetMeasLoc_ProcessListCell(MeasLoc_VoltageCurrent measLoc_vib, bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //振动测量位置名称 	
            Cell cell0 = new Cell();
            cell0.displayValue = measLoc_vib.MeasLocName;
            //所属部件名称 	
            Cell cell1 = new Cell();
            cell1.displayValue = measLoc_vib.DevTurComponent.ComponentName;
            //截面
            Cell cell2 = new Cell();
            cell2.displayValue = measLoc_vib.SectionName;
            //方向
            Cell cell3 = new Cell();
            cell3.displayValue = measLoc_vib.Orientation;
            //振动测量位置id
            Cell cell5 = new Cell();
            cell5.displayValue = measLoc_vib.MeasLocationID;
            //转速测量位置id
            Cell cell8 = new Cell();
            cell8.displayValue = "";// measLoc_vib.MeasLocationID;

            //编辑
            /*  Cell cell6 = new Cell();
              if (edit)
              {
                  cell6.type = "btn";
                  cell6.displayValue = "编辑";
                  cell6.onclick = "editVib('" + measLoc_vib.MeasLocationID + "','" + measLoc_vib.ComponentID + "')";
                  cell6.style = "btn btn-primary btn-sm";
              }
              else { cell6.displayValue = "-"; }*/
            //删除
            Cell cell7 = new Cell();
            cell7.type = "btn";
            cell7.displayValue = " 删除";
            cell7.onclick = "deleteProcessMeasLoc('" + measLoc_vib.MeasLocationID + "',this)";
            cell7.style = "btn btn-danger btn-sm btnDelete";

            cellList.Add(cell0);
            cellList.Add(cell1);
            cellList.Add(cell2);
            cellList.Add(cell3);
            cellList.Add(cell5);
            cellList.Add(cell8);
            //cellList.Add(cell6);
            cellList.Add(cell7);
            return cellList.ToArray();
        }

        #endregion
    }
}
