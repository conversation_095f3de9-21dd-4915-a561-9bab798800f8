﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 诊断,维护记录相关操作类
    /// </summary>
    public class DiagnosisManager
    {
        
        public string GetNewDiagnosisLog(string turbineID)
        {
            List<TurbineDiagnosisReport> diagList = DiagnosisReportManagement.GetDiagnosisReportListByTurId(turbineID);
            string log = string .Empty;
            if (diagList.Count > 0)
            {
                TurbineDiagnosisReport report = diagList.OrderByDescending(item => item.DiagnosisTime).FirstOrDefault();
                if (!string.IsNullOrEmpty(report.AnalyzeConclusion))
                {
                    TurbineDiagnoses objDiagnoses = TurbineDiagnoses.FromXmlString(report.AnalyzeConclusion);
                    TurbineDiagnosesItem StateInfo = objDiagnoses.TurbineDiagnosesItems.OrderByDescending(item => item.MalfunctionStateDegree).First();
                    if (StateInfo != null)
                    {
                        log = "诊断级别：" + FormatString(StateInfo.MalfunctionStateDesc);
                    }
                }
                else
                {
                    log = "无最新数据";
                }
            }
            else
            {
                log = "无最新数据";
            }
            return log;
        }
        #region 诊断维修报告结合
        public BaseTableModel GetNewDiagnosisAssignmentByID(string turbineID,out string status)
        {
            DiagnosisAssignment diagAssignment = AlrmRecordManagement.GetDiagnosisAssignmentByID(turbineID);
            //创建Table
            BaseTableModel tableModel = new BaseTableModel();
            if (diagAssignment == null)
            {
                status = "无诊断";
                return tableModel;
            }
            status = diagAssignment.AssignmentStatus.ToString();
            tableModel.tableName = "diagAssignmentTable";
            List<DiagnosisHandleLog> dataList = diagAssignment.DiagnosisHandleLogs;

            List<Rows> rowsList = new List<Rows>();
            for (int i = 0; i < dataList.Count; i++)
            {
                Rows row = new Rows();
                row.cells = GetDiagAssignmentTableCell(dataList[i]);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;

        }

        private Cell[] GetDiagAssignmentTableCell(DiagnosisHandleLog diagnosisHandleLog)
        {
            List<Cell> cells = new List<Cell>();
            Cell cell1 = new Cell();
            cell1.displayValue = diagnosisHandleLog.CreateTime.ToString();
            cells.Add(cell1);
            Cell cell2 = new Cell();
            cell2.displayValue = diagnosisHandleLog.WorkDescription;
            cells.Add(cell2);
            Cell cell3 = new Cell();
            cell3.displayValue = diagnosisHandleLog.HandleUser;
            cells.Add(cell3);

            return cells.ToArray();
        }

        
        /// <summary>
        /// 取的诊断维修记录
        /// </summary>
        /// <param name="turbineID">机组id</param>
        /// <param name="count">显示条数</param>
        /// <returns></returns>
        public BaseTableModel GetDiagnosisLog(string turbineID, int count)
        {
            
            //诊断报告
            List<TurbineDiagnosisReport> diagList = DiagnosisReportManagement.GetDiagnosisReportListByTurId(turbineID);
            //维修报告
            List<TurbineMaintainReport> maintainList = DiagnosisReportManagement.GetMaintainReportListByTurId(turbineID);
            //没有处理显示条数页面,因为只有一个table
            //ToDo
            BaseTableModel tableModel = CreateDiagAndMaintainTable(diagList, maintainList);
            return tableModel;
        }

        private BaseTableModel CreateDiagAndMaintainTable(List<TurbineDiagnosisReport> diagList, List<TurbineMaintainReport> maintainList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "diagLog";
            if (diagList.Count == 0)
            {
                tableModel.rows = tableModel.addEmptyRow(7);
                return tableModel;
            }
            List<Rows> rowsList = new List<Rows>();
            foreach (TurbineDiagnosisReport log in diagList)
            {
                Rows rows = new Rows();
                rows.cells = CreateDiagTableCell(log);
                rowsList.Add(rows);
            }
            foreach (TurbineMaintainReport maintainLog in maintainList)
            {
                Rows row = new Rows();
                row.cells = CreateMaintainTableCell(maintainLog);
                rowsList.Add(row);
            }
            //排序，前台做成后根据时间排序。
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        private Cell[] CreateMaintainTableCell(TurbineMaintainReport maintainLog)
        {
            List<Cell> cells = new List<Cell>();
            //机组ID
            Cell cell01 = new Cell();
            cell01.displayValue = maintainLog.WindTurbineID;
            cells.Add(cell01);
            //故障等级
            Cell cell2 = new Cell();
            cell2.displayValue = string.Empty;
            cells.Add(cell2);

            //诊断结论
            Cell cell3 = new Cell();
            cell3.displayValue = "";
            cells.Add(cell3);

            //维修建议&处理描述
            Cell cell4 = new Cell();
            cell4.displayValue = maintainLog.Advice;
            cell4.title = "处理描述";
            cells.Add(cell4);

            //时间
            Cell cell5 = new Cell();
            cell5.displayValue = maintainLog.HandleTime.ToString();
            cells.Add(cell5);
           
            //报告人
            Cell cell6 = new Cell();
            cell6.displayValue = maintainLog.HandleUser;
            cells.Add(cell6);

            //有无报告
            Cell cell7 = new Cell();
            if (maintainLog.WordReport!=null)
            {
                cell7.displayValue = "有报告";
            }
            else { cell7.displayValue = "暂无报告"; }
            cells.Add(cell7);

            return cells.ToArray();
        }


        private Cell[] CreateDiagTableCell(TurbineDiagnosisReport log)
        {
            List<Cell> cells = new List<Cell>();

            //机组ID
            Cell cell01 = new Cell();
            cell01.displayValue = log.WindTurbineID;
            cells.Add(cell01);
            //故障等级
            Cell cell2 = new Cell();
            cell2.displayValue = "--";// log. .MalfunctionStateDegree.ToString();
            cells.Add(cell2);
            //诊断结论
            Cell cell3 = new Cell();
            cell3.displayValue = log.AnalyzeConclusion;
            cells.Add(cell3);

            //维修建议&处理描述
            Cell cell4 = new Cell();
            cell4.displayValue = log.Advice;
            cell4.title = "处理建议";
            cells.Add(cell4);

            //时间
            Cell cell5 = new Cell();
            cell5.displayValue = log.DiagnosisTime.ToString();
            cells.Add(cell5);

            //报告人
            Cell cell6 = new Cell();
            cell6.displayValue = log.DiagnosisUser;
            cells.Add(cell6);

            //有无报告
            Cell cell7 = new Cell();
            if (log.WordReport!=null)
            {
                cell7.displayValue = "有报告";
            }
            else { cell7.displayValue = "暂无报告"; }
            cells.Add(cell7);

            return cells.ToArray();
        }

        #endregion 诊断维修报告合并

        #region 诊断维修报告分离
        /// <summary>
        /// 诊断报告
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public BaseTableModel GetDiagAssignmentTable(string turbineID, List<AlarmStatus_Turbine> statusRTList)
        {
            List<TurbineDiagnosisReport> diagList = DiagnosisReportManagement.GetDiagnosisReportListByTurId(turbineID).OrderByDescending(item=>item.DiagnosisTime).ToList();
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "diagLog";
            List<Rows> rowsList = new List<Rows>();
            int diagListCount = 0;
            if (diagList.Count > 1000)
            {
                diagListCount = 1000;
            }
            else
            {
                diagListCount = diagList.Count;
            }
            for (int i = 0; i < diagListCount; i++)
            {
                Rows row = new Rows();
                DeviceRTAlarmStatus statusRT = statusRTList.Find(item => item.DevSegmentID == diagList[i].WindTurbineID);
                row.cells = CreateDiagAssignmentTableCell(diagList[i], statusRT);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }
        #region 风场下的诊断信息

        public BaseTableModel GetDiagAssignmentTable(List<TurbineDiagnosisReport> diagList, List<AlarmStatus_Turbine> statusRTList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "diagLog";
            List<Rows> rowsList = new List<Rows>();
            int diagListCount = 0;
            if (diagList.Count > 1000)
            {
                diagListCount = 1000;
            }
            else
            {
                diagListCount = diagList.Count;
            }
            for (int i = 0; i < diagListCount; i++)
            {
                Rows row = new Rows();
                DeviceRTAlarmStatus statusRT = statusRTList.Find(item => item.DevSegmentID == diagList[i].WindTurbineID);
                row.cells = CreateDiagAssignmentTableCell(diagList[i], statusRT);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        public BaseTableModel GetMaintainTable(List<TurbineMaintainReport> maintainList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "diagLog";
            List<Rows> rowsList = new List<Rows>();
            int maintainCount = 0;
            if (maintainList.Count > 1000)
            {
                maintainCount = 1000;
            }
            else
            {
                maintainCount = maintainList.Count;
            }
            for (int i = 0; i < maintainCount; i++)
            {
                Rows row = new Rows();
                row.cells = CreateMaintainTableCells(maintainList[i]);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        #endregion

        private Cell[] CreateDiagAssignmentTableCell(TurbineDiagnosisReport turbineDiagnosisReport, DeviceRTAlarmStatus statusRT)
        {
            List<Cell> cellList = new List<Cell>();
            WindTurbine myWind = DevTreeManagement.GetWindTurbine(turbineDiagnosisReport.WindTurbineID);
            //机组ID
            Cell cell0 = new Cell();
            cell0.displayValue = myWind.WindTurbineName;// turbineDiagnosisReport.AlarmTime.ToString();
            //cell0.type = "link";
            //cell0.href = string.Format("/WindTurbine/ShowMaintenanceRecords/{0}/{1}", myWind.WindParkID, turbineDiagnosisReport.WindTurbineID);
            cellList.Add(cell0);
            //故障等级
            Cell cell01 = new Cell();
            cell01.displayValue = statusRT == null ? "未知" : AppFramework.Utility.EnumHelper.GetDescription(statusRT.AlarmDegree);
            //cell01.color = cell01.displayValue == "正常" ? "rgb(8, 207, 45)" : cell01.displayValue == "危险" ? "red" : cell01.displayValue == "报警" ? "yellow" : "";
            cellList.Add(cell01);
            //诊断时间
            Cell cell2 = new Cell();
            cell2.displayValue = turbineDiagnosisReport.DiagnosisTime.ToString();
            cellList.Add(cell2);
            //诊断结论
            Cell cell3 = new Cell();
            if (!string.IsNullOrEmpty(turbineDiagnosisReport.AnalyzeConclusion))
            {
                TurbineDiagnoses objDiagnoses = TurbineDiagnoses.FromXmlString(turbineDiagnosisReport.AnalyzeConclusion);
                foreach (TurbineDiagnosesItem item in objDiagnoses.TurbineDiagnosesItems)
                {
                    cell3.displayValue += "<span style='background:" + item.htmlColor + "'>" + item.ComponentName + "</span>" + "：" + FormatString(item.DiagnosesText) + "<br />";
                }
                TurbineDiagnosesItem StateInfo = objDiagnoses.TurbineDiagnosesItems.OrderByDescending(item => item.MalfunctionStateDegree).First();
                if (StateInfo != null)
                {
                    cell01.displayValue = StateInfo.MalfunctionStateDesc;
                    cell01.color = StateInfo.htmlColor;
                }
            }
            else
            {
                cell3.displayValue = "";
            }
            cellList.Add(cell3);
            //处理建议
            Cell cell4 = new Cell();
            if (!string.IsNullOrEmpty(turbineDiagnosisReport.Advice))
            {
                cell4.displayValue = string.Join("<br />", GetAdviceList(turbineDiagnosisReport.Advice));
            }
            else
            {
                cell4.displayValue = "";
            }
            cellList.Add(cell4);
            //报告人
            Cell cell5 = new Cell();
            cell5.displayValue = turbineDiagnosisReport.DiagnosisUser;
            cellList.Add(cell5);
            //查看诊断报告
            Cell cell6 = new Cell();
            double StringDate = turbineDiagnosisReport.DiagnosisTime.ToOADate();
            cell6.displayValue = turbineDiagnosisReport.WordReport != null ? "<a target='_blank' href='/WindTurbine/Report/" + StringDate + "/" + turbineDiagnosisReport.WindTurbineID + "'>查看</a>" : "无";
            cellList.Add(cell6);
            return cellList.ToArray();
        }

        public List<string> GetAdviceList(string _adviceXmlString)
        {
            List<string> addviceList = new List<string>();

            if (!string.IsNullOrEmpty(_adviceXmlString))
            {
                // 反序列化 处理建议
                foreach (string item in XmlSerializationHelper<List<string>>.FromXmlString(_adviceXmlString))
                {
                    // 格式化字符串，检查建议条目末尾时候存在句号
                    addviceList.Add(FormatString(item));
                }
            }
            return addviceList;
        }

        /// <summary>
        /// 检查字符串最后是否有句号，没有则添加
        /// </summary>
        /// <param name="p"></param>
        private string FormatString(string _str)
        {
            if (_str == null || _str == "") return _str;

            _str.Trim();

            string tempstr = _str.Substring(_str.Length - 1);

            if (tempstr != "。")
            {
                _str += "。";
            }

            return _str;
        }

        /// <summary>
        /// 机组维修报告
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public BaseTableModel GetMaintainTable(string turbineID)
        {
            List<TurbineMaintainReport> maintainList = DiagnosisReportManagement.GetMaintainReportListByTurId(turbineID).OrderByDescending(item => item.HandleTime).ToList();
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "diagLog";
            List<Rows> rowsList = new List<Rows>();
            int maintainCount = 0;
            if (maintainList.Count > 1000)
            {
                maintainCount = 1000;
            }
            else {
                maintainCount = maintainList.Count;
            }
            for (int i = 0; i < maintainCount; i++)
            {
                Rows row = new Rows();
                row.cells = CreateMaintainTableCells(maintainList[i]);
                rowsList.Add(row);
            }
            tableModel.rows = rowsList.ToArray();
            return tableModel;
        }

        private Cell[] CreateMaintainTableCells(TurbineMaintainReport turbineMaintainReport)
        {
            List<Cell> cellList = new List<Cell>();
            WindTurbine myWind = DevTreeManagement.GetWindTurbine(turbineMaintainReport.WindTurbineID);
            //机组ID
            Cell cell00 = new Cell();
            cell00.displayValue = myWind.WindTurbineName;// turbineDiagnosisReport.AlarmTime.ToString();
            //cell00.type = "link";
            //cell00.href = string.Format("/WindTurbine/ShowMaintenanceRecords/{0}/{1}", myWind.WindParkID, turbineMaintainReport.WindTurbineID);
            cellList.Add(cell00);
            //报告时间
            Cell cell1 = new Cell();
            cell1.displayValue = turbineMaintainReport.HandleTime.ToString();
            cellList.Add(cell1);
            //处理描述
            Cell cell2 = new Cell();
            cell2.displayValue = turbineMaintainReport.Advice.ToString();
            cellList.Add(cell2);
            //报告人
            Cell cell3 = new Cell();
            cell3.displayValue = turbineMaintainReport.HandleUser;
            cellList.Add(cell3);
            return cellList.ToArray();
        }
        #endregion 诊断维修报告分离
    }
}