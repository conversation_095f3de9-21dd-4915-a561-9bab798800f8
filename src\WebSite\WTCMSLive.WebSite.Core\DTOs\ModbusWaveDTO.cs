namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 编辑Modbus波形DTO
    /// </summary>
    public class EditModbusWaveDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public decimal? SampleRate { get; set; }
        public decimal? SampleLength { get; set; }
        public List<string>? evLists { get; set; }

        public string? WaveDefinitionName { get; set; }
    }


    /// <summary>
    /// Modbus波形标识符
    /// </summary>
    public class ModbusWaveIdentifier
    {
        public string WindTurbineID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
    }

    /// <summary>
    /// Modbus波形查询响应DTO
    /// </summary>
    public class ModbusWaveListDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public string MeasLocationName { get; set; }
        public string WaveDefinitionName { get; set; }
        public string? ModbusUnitID { get; set; }
        public float SampleRate { get; set; }
        public int SampleLength { get; set; }
        public int SingleType { get; set; }
        public string SingleTypeName { get; set; }
        public List<string> EigenValues { get; set; } = new List<string>();
    }

    /// <summary>
    /// SVM波形查询响应DTO
    /// </summary>
    public class SVMWaveListDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public string MeasLocationName { get; set; }
        public string WaveDefinitionName { get; set; }
        public float SampleRate { get; set; }
        public int SampleLength { get; set; }
        public int ParamType { get; set; }
        public string ParamTypeName { get; set; }
        public List<string> EigenValues { get; set; } = new List<string>();
    }

    /// <summary>
    /// 统一的波形查询响应DTO
    /// </summary>
    public class UnifiedWaveListDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public string MeasLocationName { get; set; }
        public string WaveDefinitionName { get; set; }
        public string? ModbusUnitID { get; set; }
        public float SampleRate { get; set; }
        public int SampleLength { get; set; }
        public string WaveType { get; set; } // "Modbus" 或 "SVM"
        public int? SingleType { get; set; } // Modbus波形的信号类型
        public string? SingleTypeName { get; set; } // Modbus波形的信号类型名称
        public int? ParamType { get; set; } // SVM波形的参数类型
        public string? ParamTypeName { get; set; } // SVM波形的参数类型名称
        public List<string> EigenValues { get; set; } = new List<string>();
        public int? ModbusDeviceID { get; set; }

        public string ModbusDeviceName { get; set; }
    }
}
