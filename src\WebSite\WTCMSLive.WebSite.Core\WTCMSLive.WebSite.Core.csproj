﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="fonts\**" />
    <Compile Remove="Scripts\**" />
    <Content Remove="fonts\**" />
    <Content Remove="Scripts\**" />
    <EmbeddedResource Remove="fonts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="fonts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="log4net" Version="3.0.4" />
    <PackageReference Include="Microsoft.Data.Sqlite.Core" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite.Core" Version="6.0.36" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
    <PackageReference Include="MySql.EntityFrameworkCore" Version="6.0.33" />
    <PackageReference Include="EPPlus" Version="7.1.1" />
    <PackageReference Include="protobuf-net.Core" Version="3.2.46" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="SSH.NET" Version="2025.0.0" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="7.0.0" />
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.1" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
    <PackageReference Include="TinyPinyin.Net" Version="1.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.15.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WindCMS.GWServer\WindCMS.GWServer\WindCMS.GWServer.csproj" />
    <ProjectReference Include="..\WTCMSLive.BusinessModel\WTCMSLive.BusinessModel\WTCMSLive.BusinessModel.csproj" />
    <ProjectReference Include="..\WTCMSLive.BusinessModel\WTCMSLive.UserRoleManagement\WTCMSLive.UserRoleManagement.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AppFramework.Analysis">
      <HintPath>..\AppNetCore\AppFramework.Analysis.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.IDUtility">
      <HintPath>..\AppNetCore\AppFramework.IDUtility.dll</HintPath>
      <Private></Private>
    </Reference>
    <Reference Include="AppFrameWork.Utility">
      <HintPath>..\AppNetCore\AppFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.ConfigEntities">
      <HintPath>..\AppNetCore\CMSFramework.ConfigEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DataStorageLogic">
      <HintPath>..\AppNetCore\CMSFramework.DataStorageLogic.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUEntities">
      <HintPath>..\AppNetCore\CMSFramework.DAUEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUFacadeBase">
      <HintPath>..\AppNetCore\CMSFramework.DAUFacadeBase.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUMonitoringEntities">
      <HintPath>..\AppNetCore\CMSFramework.DAUMonitoringEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DevTreeEntities">
      <HintPath>..\AppNetCore\CMSFramework.DevTreeEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DiagnosisEntities">
      <HintPath>..\AppNetCore\CMSFramework.DiagnosisEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DomainEntities">
      <HintPath>..\AppNetCore\CMSFramework.DomainEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Base">
      <HintPath>..\AppNetCore\CMSFramework.EF.Base.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Config">
      <HintPath>..\AppNetCore\CMSFramework.EF.Config.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DAU">
      <HintPath>..\AppNetCore\CMSFramework.EF.DAU.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DAUMonitoring">
      <HintPath>..\AppNetCore\CMSFramework.EF.DAUMonitoring.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DevTree">
      <HintPath>..\AppNetCore\CMSFramework.EF.DevTree.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Diagnosis">
      <HintPath>..\AppNetCore\CMSFramework.EF.Diagnosis.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.MeasData">
      <HintPath>..\AppNetCore\CMSFramework.EF.MeasData.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.MeasDef">
      <HintPath>..\AppNetCore\CMSFramework.EF.MeasDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Monitoring">
      <HintPath>..\AppNetCore\CMSFramework.EF.Monitoring.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Overview">
      <HintPath>..\AppNetCore\CMSFramework.EF.Overview.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.POCO">
      <HintPath>..\AppNetCore\CMSFramework.EF.POCO.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.System">
      <HintPath>..\AppNetCore\CMSFramework.EF.System.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EigenValue.Storage">
      <HintPath>..\AppNetCore\CMSFramework.EigenValue.Storage.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EigenValueDef">
      <HintPath>..\AppNetCore\CMSFramework.EigenValueDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.FSDB">
      <HintPath>..\AppNetCore\CMSFramework.FSDB.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Logger">
      <HintPath>..\AppNetCore\CMSFramework.Logger.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDataEntities">
      <HintPath>..\AppNetCore\CMSFramework.MeasDataEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDataStorage">
      <HintPath>..\AppNetCore\CMSFramework.MeasDataStorage.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDefEntities">
      <HintPath>..\AppNetCore\CMSFramework.MeasDefEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MonitoringEntities">
      <HintPath>..\AppNetCore\CMSFramework.MonitoringEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.OverviewEntities">
      <HintPath>..\AppNetCore\CMSFramework.OverviewEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.SystemEntities">
      <HintPath>..\AppNetCore\CMSFramework.SystemEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Trigger">
      <HintPath>..\AppNetCore\CMSFramework.Trigger.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.TypeDef">
      <HintPath>..\AppNetCore\CMSFramework.TypeDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Utility">
      <HintPath>..\AppNetCore\CMSFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="CMSFrameworkEX.Permission">
      <HintPath>..\AppNetCore\CMSFrameworkEX.Permission.dll</HintPath>
    </Reference>
    <Reference Include="Nhgdb">
      <HintPath>..\AppNetCore\Nhgdb.dll</HintPath>
    </Reference>
    <Reference Include="Nhgdb.EntityFrameworkCore.HGDB">
      <HintPath>..\AppNetCore\Nhgdb.EntityFrameworkCore.HGDB.dll</HintPath>
    </Reference>
    <Reference Include="WindCMS.SupervisoryEV">
      <HintPath>..\AppNetCore\WindCMS.SupervisoryEV.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="App_GlobalResources\Message.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Message.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="App_GlobalResources\Message.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Message.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Update="AlarmThresholdConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DbVersionDifferencesConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="EigenTrendConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="EigenValueConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="EigenValueConfig_DAU.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="EigenValueConfig_OEM.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Output\output.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SplitSettings.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Template\wtlivedb.db">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Template\wtlivedbtrend.db">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TowALarmConfig.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Uploads\" />
    <Folder Include="wwwroot\" />
  </ItemGroup>

</Project>
