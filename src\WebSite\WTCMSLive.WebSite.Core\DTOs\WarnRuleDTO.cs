using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 报警规则基础DTO
    /// </summary>
    public class WarnRuleBaseDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        [Required]
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 测量位置ID
        /// </summary>
        [Required]
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 特征值ID
        /// </summary>
        [Required]
        public string EigenValueID { get; set; }

        /// <summary>
        /// 工况参数类型
        /// </summary>
        [Required]
        public short WorkConParameter { get; set; }

        /// <summary>
        /// 下限值
        /// </summary>
        public double? LowerLimitValue { get; set; }

        /// <summary>
        /// 上限值
        /// </summary>
        public double? UpperLimitValue { get; set; }

        /// <summary>
        /// 注意值（正向）
        /// </summary>
        public double? WarnValue { get; set; }

        /// <summary>
        /// 报警值（正向）
        /// </summary>
        public double? AlarmValue { get; set; }

        /// <summary>
        /// 反向注意值
        /// </summary>
        public double? ReverseWarnValue { get; set; }

        /// <summary>
        /// 反向报警值
        /// </summary>
        public double? ReverseAlarmValue { get; set; }

        /// <summary>
        /// 测量位置类型 (0: 晃动/油液, 1: 振动, 2: 应变, 3: 间隙, 4: 超声)
        /// </summary>
        [Required]
        public int MeasLocType { get; set; }
    }

    /// <summary>
    /// 新增报警规则DTO
    /// </summary>
    public class AddWarnRuleDTO : WarnRuleBaseDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// 是否应用到所有同型号机组
        /// </summary>
        public bool ApplyToAll { get; set; } = false;

        /// <summary>
        /// 工况参数字符串 (格式: "功率:100,300$转速:100,200")
        /// </summary>
        public string? WorkConditionParams { get; set; }
    }

    /// <summary>
    /// 批量新增报警规则请求DTO
    /// </summary>
    public class BatchAddWarnRuleRequestDTO
    {
        /// <summary>
        /// 报警规则列表
        /// </summary>
        [Required]
        public List<AddWarnRuleDTO> WarnRules { get; set; }
    }

    /// <summary>
    /// 编辑报警规则DTO
    /// </summary>
    public class EditWarnRuleDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        [Required]
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 阈值组ID
        /// </summary>
        [Required]
        public string ThresholdGroup { get; set; }

        /// <summary>
        /// 注意值（正向）
        /// </summary>
        public double? WarnValue { get; set; }

        /// <summary>
        /// 报警值（正向）
        /// </summary>
        public double? AlarmValue { get; set; }

        /// <summary>
        /// 反向注意值
        /// </summary>
        public double? ReverseWarnValue { get; set; }

        /// <summary>
        /// 反向报警值
        /// </summary>
        public double? ReverseAlarmValue { get; set; }

        /// <summary>
        /// 是否应用到所有同型号机组
        /// </summary>
        public bool ApplyToAll { get; set; } = false;

    }

    /// <summary>
    /// 删除报警规则项DTO
    /// </summary>
    public class DeleteWarnRuleItemDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        [Required]
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 测量位置ID
        /// </summary>
        [Required]
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 特征值ID
        /// </summary>
        [Required]
        public string EigenValueID { get; set; }

        /// <summary>
        /// 工况参数类型
        /// </summary>
        [Required]
        public short WorkConParameter { get; set; }

        /// <summary>
        /// 阈值组ID
        /// </summary>
        [Required]
        public string ThresholdGroup { get; set; }
    }

    /// <summary>
    /// 批量删除报警规则请求DTO
    /// </summary>
    public class BatchDeleteWarnRuleRequestDTO
    {
        /// <summary>
        /// 要删除的报警规则列表
        /// </summary>
        [Required]
        public List<DeleteWarnRuleItemDTO> WarnRules { get; set; }
    }

    /// <summary>
    /// 操作结果DTO
    /// </summary>
    public class WarnRuleOperationResultDTO
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 成功处理的数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败处理的数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 详细结果列表
        /// </summary>
        public List<string> Details { get; set; } = new List<string>();
    }

    /// <summary>
    /// 测量位置信息DTO
    /// </summary>
    public class MeasLocationInfoDTO
    {
        /// <summary>
        /// 测量位置ID
        /// </summary>
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 测量位置名称
        /// </summary>
        public string MeasLocName { get; set; }

        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        public string ComponentID { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int OrderSeq { get; set; }

        /// <summary>
        /// 测量位置类型 (0: 晃动/油液, 1: 振动, 2: 应变, 3: 间隙, 4: 超声)
        /// </summary>
        public int MeasLocType { get; set; }

        /// <summary>
        /// 截面名称（用于SVM）
        /// </summary>
        public string SectionName { get; set; }

        /// <summary>
        /// 参数类型（用于SVM）
        /// </summary>
        public int? ParamType { get; set; }
    }

    /// <summary>
    /// 特征值信息DTO
    /// </summary>
    public class EigenValueInfoDTO
    {
        /// <summary>
        /// 特征值ID
        /// </summary>
        public string EigenValueID { get; set; }

        /// <summary>
        /// 特征值名称
        /// </summary>
        public string EigenValueName { get; set; }

        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 工程单位名称
        /// </summary>
        public string EngUnitName { get; set; }

        /// <summary>
        /// 特征值类型（用于SVM）
        /// </summary>
        public int? EigenValueType { get; set; }
    }
}
