﻿using CMSFramework.BusinessEntity;
using CMSFramework.Repository;
using WindCMS.GWServer.DauWork;

namespace WindCMS.GWServer;

public class DauContextDataProvider : IDauContextDataProvider, IDAUMdfVerUpdater
{
    private readonly IDAUTreeRepository _dauRepository = DAUTreeRepository.Instance;
    private readonly IDevTreeRepository _devRepository = DevTreeRepository.Instance;
    private readonly IMDFTreeRepository _mdfRepository = MDFTreeRepository.Instance;

    private static DauContextDataProvider? _provider;

    public static DauContextDataProvider Single => _provider ??= new DauContextDataProvider();

    private DauContextDataProvider()
    {
    }

    public List<WindTurbine> AllWindTurbineList => _devRepository.GetAllWindTurbine();

    public List<MeasDefinition> AllMDFList => _mdfRepository.GetAllMDF();

    public List<SVMUnit> AllSVMUnitList => _dauRepository.GetAllSVMUnit();

    public List<OilUnit> AllOilUnitList => _dauRepository.GetAllOilUnit();

    public List<MCS> AllMcsList => _dauRepository.GetAllMCS();

    
    public void OnDAUUpdateMDF(WindDAU dau)
    {
        // 更新DAU里的测量定义版本
        DAUTreeRepository.Instance.UpdateDAU(dau);

        // 更新测量定义缓存
        MDFTreeRepository.Instance.UpdateMdfCache();
    }
}