using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 批量删除测量方案DTO
    /// </summary>
    public class BatchDeleteMeasSolutionDTO
    {
        public string WindTurbineID { get; set; }
        public List<int> MeasSolutionIDs { get; set; }
    }

    /// <summary>
    /// 批量添加测量方案DTO
    /// </summary>
    public class BatchAddMeasSolutionDTO
    {
        public string WindTurbineID { get; set; }
        public List<MeasSolutionAddItem> Solutions { get; set; }
    }

    /// <summary>
    /// 测量方案添加项
    /// </summary>
    public class MeasSolutionAddItem
    {
        public string WindTurbineID { get; set; }
        public string MeasSolutionName { get; set; }
        public string MeasSolutionType { get; set; }
        public List<string> MeasDefinitionIDList { get; set; }
        public int DaqInterval { get; set; }
        public int WaveInterval { get; set; }
        public int EigenInterval { get; set; }
        //public string TriggeringCondition { get; set; }
        //public string TriggeringOperator { get; set; }
        //public int TriggeringNum { get; set; }
    }

    /// <summary>
    /// 单个测量方案编辑DTO
    /// </summary>
    public class EditMeasSolutionDTO
    {
        public string WindTurbineID { get; set; }
        public int MeasSolutionID { get; set; }
        public string MeasSolutionName { get; set; }
        public string MeasSolutionType { get; set; }
        public List<string> MeasDefinitionIDList { get; set; }
        public int DaqInterval { get; set; }
        public int WaveInterval { get; set; }
        public int EigenInterval { get; set; }
        //public string TriggeringCondition { get; set; }
        //public string TriggeringOperator { get; set; }
        //public int TriggeringNum { get; set; }
    }

    /// <summary>
    /// 测量方案响应DTO
    /// </summary>
    public class MeasSolutionResponseDTO
    {
        public int MeasSolutionID { get; set; }
        public string MeasSolutionName { get; set; }
        public string MeasSolutionType { get; set; }
        public string WindTurbineID { get; set; }
        public int DaqInterval { get; set; }
        public int WaveInterval { get; set; }
        public int EigenInterval { get; set; }
        //public string TriggeringCondition { get; set; }
        //public string TriggeringOperator { get; set; }
        //public double TriggeringNum { get; set; }
        public List<MeasDefinitionInfo> MeasDefinitions { get; set; }
    }


    public class MeasSolutionDelDTO
    {
        public int MeasSolutionID { get; set; }
        public string WindTurbineID { get; set; }

    }
    /// <summary>
    /// 测量定义信息
    /// </summary>
    public class MeasDefinitionInfo
        {
            public string Key { get; set; }  // MeasDefinitionName
            public string Value { get; set; } // MeasDefinitionID
        }
}
