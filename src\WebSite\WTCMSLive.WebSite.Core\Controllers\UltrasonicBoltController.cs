﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using WTCMSLive.BusinessModel.TIM;
using Microsoft.AspNetCore.Mvc;
using static Org.BouncyCastle.Math.EC.ECCurve;
using System.Text.Json;

namespace WTCMSLive.WebSite.Controllers
{
    public class UltrasonicBoltController : Controller
    {
        //
        // GET: /UltrasonicBolt/

        public ActionResult Index(string windParkID, string turbineID)
        {
            ViewBag.info = windParkID + "/" + turbineID;
            string author = "show";
            bool editFlg = false;
            //判断角色权限
            string userName = Request.Cookies["WindCMSUserName"];
            User user = UserManagement.GetUserById(userName);
            if (user != null && user.UserRole != null)
            {
                //roleID = 1 超级管理员 roleID=2系统管理员
                if (user.UserRole.RoleID == "1")
                {
                    editFlg = true;
                    author = "show";
                }
            }
            ViewBag.canEdit = editFlg;
            ViewBag.WindTurbineID = turbineID;
            ViewBag.WindParkID = windParkID;

            ViewData["author"] = author;
            ViewData["roleAdd"] = RoleManagement.IsFunOperation(userName, ModuleName.DAUManage, FunctionName.Add);
            ViewData["roleEdit"] = RoleManagement.IsFunOperation(userName, ModuleName.DAUManage, FunctionName.Edit);
            ViewData["roleDel"] = RoleManagement.IsFunOperation(userName, ModuleName.DAUManage, FunctionName.Delete);


            //采集单元信息
            var daulist = DAUSManageModel.GetDAUListById(turbineID);
            if (daulist.Count > 0)
            {
                var _dau = daulist.FirstOrDefault(k => k.DAUType == EnumDAUType.Ultrasonic || k.DAUType == EnumDAUType.IfastUltrasonic);
                if(_dau != null)
                {
                    ViewData["DeviceIP"] = _dau.IP; 
                    ViewData["DeviceName"] = _dau.DAUName;
                    ViewData["DeviceID"] = _dau.DauID;
                }
              
            }
            ViewData["DeviceList"] = daulist.Where(k => k.DAUType == EnumDAUType.Ultrasonic || k.DAUType == EnumDAUType.IfastUltrasonic).ToJson();


            if (Request.IsAjaxRequest())
            {
                return PartialView();
            }
            ViewData["pageType"] = "view";
            ViewData["NodePath"] = Utility.ToJson(new object[]
            {
                new []{Resources.Message.CompanyName,"/OverViewPage/Index"},
            });
            ViewData["info"] = "company";
            if (Request.IsAjaxRequest())
            {
                ViewBag.pageType = "partialView";
                return PartialView();
            }
            return View();
        }
        /// <summary>
        /// 添加设备
        /// </summary>
        /// <param name="name"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        public string AddDevice(string windParkID, string turbineID, string name, string ip)
        {
            try
            {
                // 获取DAU
                WindDAU _dau = DAUSManageModel.GetBoltDevice(turbineID);
                if (_dau == null)
                {
                    _dau = new WindDAU();
                    _dau.DauID = (DAUSManageModel.GetLastDauID(turbineID) + 1).ToString();
                    _dau.TrendSaveInterval = 0;
                    _dau.WaveSaveInterval = 0;
                    _dau.DataAcquisitionInterval = 0;
                    _dau.WindTurbineID = turbineID;
                    _dau.DAUName = name;
                    _dau.IP = ip;
                    _dau.IsAvailable = true;
                    _dau.WindParkID = windParkID;
                    _dau.DAUType = EnumDAUType.Ultrasonic;
                    DauManagement.AddDAU(_dau);
                }
                else
                {
                    _dau.IP = ip;
                    _dau.DAUName = name;
                    DauManagement.EditDAUInfo(_dau);
                }

                return "Success";
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }


        public string GetFlangeList(string WindTurbineID, string DAUID)
        {
     /*       if (DAUID == "") {
                return "";
            }*/

            //List<DAUChannelV2> List = DauManagement.GetDAUById(WindTurbineID).DAUChannelList;

            WindDAU windDauData = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (windDauData==null) {
               return new List<WindDAU>().ToJson();
            }
            else 
          {
                // 获取机组信息
                WindTurbine windTurbine = DevTreeManagement.GetWindTurbine(WindTurbineID);

            List<DAUChannelV2> List = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID).DAUChannelList;

            TIMManagement.UpdateMeasDefVersion(WindTurbineID);

            List<MeasLoc_Vib> measLocList_Vib = DevTreeManagement.GetVibMeasLocationByTurId(WindTurbineID);

            List<UltrasonicChannelConfig> ultrasonicList = DauManagement.GetUltrasonicChannelConfigByTurId(WindTurbineID, DAUID);

                List<UltrasonicBoltParam> ultrasonicBoltParams = DauManagement.GetUltrasonicBoltParamByParkId(windTurbine.WindParkID);

            List<UltrasonicChannelConfigModel> ListTemp = new List<UltrasonicChannelConfigModel>();
            foreach (MeasLoc_Vib data in measLocList_Vib.OrderBy(item => item.OrderSeq))
            {
                DAUChannelV2 daudata = List.Find(item => item.MeasLocVibID == data.MeasLocationID);

                
                if (daudata != null)
                {
                    UltrasonicChannelConfigModel Channeldata = new UltrasonicChannelConfigModel()
                    {
                        ChannelNumber = daudata.ChannelNumber,
                        Coeff_a = daudata.Coeff_a,
                        WindTurbineID = daudata.WindTurbineID,
                        MinBiasVolt = daudata.MinBiasVolt,
                        MaxBiasVolt = daudata.MaxBiasVolt,
                        MeasLocVibID = daudata.MeasLocVibID,
                        MeasLocVibName = measLocList_Vib.Find(item => item.MeasLocationID == daudata.MeasLocVibID).MeasLocName,
                        SectionName = measLocList_Vib.Find(item => item.MeasLocationID == daudata.MeasLocVibID).SectionName,
                    };

                    UltrasonicChannelConfig ultra = ultrasonicList.FirstOrDefault(k => k.ChannelNumber == daudata.ChannelNumber);
                    if (ultra != null)
                    {
                            //Channeldata.PreloadCalCoeffs = ultra.PreloadCalCoeffs;
                            //Channeldata.PreloadCalCoeffs = ultra.PreloadCalCoeffs;
                            Channeldata.StandardFilePath = string.IsNullOrEmpty(ultra.StandardFilePath) ? "未上传": "已上传";
                        //Channeldata.TempCalibCoeff = ultra.TempCalibCoeff;
                        Channeldata.PreloadUpperLimmit = ultra.PreloadUpperLimmit;
                        Channeldata.PreloadLowerLimmit = ultra.PreloadLowerLimmit;
                        Channeldata.DispatcherID = ultra.DispatcherID;
                        Channeldata.DispatcherChannelID = ultra.DispatcherChannelID;
                        Channeldata.PreloadCalCoeffs = "";

                            var ulparm = ultrasonicBoltParams.FirstOrDefault(t => t.BoltModel == ultra.BoltModel && t.WindTurbineModel == windTurbine.WindTurbineModel);
                            if (ulparm != null)
                            {
                                //string output = "";
                                //if (!string.IsNullOrEmpty(ulparm.PreloadCalCoeffs))
                                //{
                                //    string[] parts = ulparm.PreloadCalCoeffs.Split(',');

                                //    for (int i = 0; i < parts.Length; i++)
                                //    {
                                //        int decimalIndex = parts[i].IndexOf(".");
                                //        if (decimalIndex != -1)
                                //        {
                                //            // 截取小数点后8位，如果不足8位则保留全部
                                //            parts[i] = parts[i].Substring(0, decimalIndex + 9);
                                //        }
                                //    }

                                //    // 重新拼接字符串
                                //    output = String.Join(",", parts);
                                //}
                                Channeldata.PreloadCalCoeffs = ulparm.PreloadCalCoeffs;
                                Channeldata.TempCalibCoeff = ulparm.TempCalibCoeff;
                            }

                    }
                    else 
                    {
                        Channeldata.PreloadCalCoeffs = "0";
                    }



                    ListTemp.Add(Channeldata);
                }


            }
            return ListTemp.ToJson();
           }
        }


        public bool EditFlange(string turbineID,string dauID,int ChannelNumber, IFormFile StandardFilePath, string PreloadCalCoeffs,float TempCalibCoeff,float PreloadUpperLimmit,float PreloadLowerLimmit)
        {
            string p1 = Request.Form["StandardFilePath"]; //普通参数获取
            var a = Request;
            var multipartFile = Request.Form.Files["StandardFilePath"];

            List<UltrasonicChannelConfig> ullist = DauManagement.GetUltrasonicChannelConfigByTurId(turbineID, dauID);
            UltrasonicChannelConfig ultra = ullist.FirstOrDefault(p => p.ChannelNumber == ChannelNumber);

            if (ultra != null)
            {
                //ultra.StandardFilePath = StandardFilePath;
                ultra.PreloadCalCoeffs = PreloadCalCoeffs;
                ultra.TempCalibCoeff = TempCalibCoeff;
                ultra.PreloadLowerLimmit = PreloadLowerLimmit;
                ultra.PreloadUpperLimmit = PreloadUpperLimmit;

                DauManagement.EditUltrasonic(ultra);
               
                return true;
            }

            return false;
        }

        [HttpPost]
        public bool EditFlange()
        {
            string dauID = Request.Form["dauID"];
            string turbineID = Request.Form["turbineID"];
            int ChannelNumber = Convert.ToInt32(Request.Form["channelNum"]);
            var multipartFile = Request.Form.Files["standardFile"];

            var isall = Request.Form["isall"];
            var PreloadCalCoeffs = Request.Form["preloadCalCoeffs"];
            var TempCalibCoeff = Convert.ToDecimal(Request.Form["tempCalibCoeff"]);
            var PreloadLowerLimmit = Convert.ToSingle(Request.Form["preloadLowerLimmit"]);
            var PreloadUpperLimmit = Convert.ToSingle(Request.Form["preloadUpperLimmit"]);
            var DispatcherID = Convert.ToInt32(Request.Form["dispatcherID"]);

            var DispatcherChannelID = Convert.ToInt32(Request.Form["dispatcherChannelID"]);

            WindTurbine wt = DevTreeManagement.GetWindTurbine(turbineID);
            List<UltrasonicBoltParam> ultrasonicBoltParams = DauManagement.GetUltrasonicBoltParamByParkId(wt.WindParkID);

            //List<UltrasonicChannelConfig> ullist = DauManagement.GetUltrasonicChannelConfigByTurId(turbineID, dauID);
            List<UltrasonicChannelConfigModel> ullist = UltrasonicManager.GetUltrasonicChannelConf(turbineID, dauID);

            // 获取当前通道
            UltrasonicChannelConfigModel ultraModel = ullist.FirstOrDefault(k => k.ChannelNumber == ChannelNumber);

            // 文件存储
            string filePath = "";
            string boltModel = "";
            if (multipartFile != null && multipartFile.Length > 0)
            {
                ////获得保存路径
                //// 获取保存路径
                //string uploadDirectory = Path.Combine(HttpContext.Server.MapPath("../Uploads"));
                //if (!Directory.Exists(uploadDirectory))
                //{
                //    Directory.CreateDirectory(uploadDirectory);
                //}
                //filePath = Path.Combine(uploadDirectory, Path.GetFileName(multipartFile.FileName));
                //try
                //{
                //    // 解析文件名获取boltmodel 
                //    var fname = multipartFile.FileName.Split('-');
                //    if (fname.Length >= 4)
                //    {
                //        boltModel = fname[fname.Length - 4];
                //    }
                //    multipartFile.SaveAs(filePath);
                //}
                //catch (Exception ex)
                //{

                //}

                // 获取保存路径
                string uploadDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Uploads");
                if (!Directory.Exists(uploadDirectory))
                {
                    Directory.CreateDirectory(uploadDirectory);
                }

                filePath = Path.Combine(uploadDirectory, Path.GetFileName(multipartFile.FileName));

                try
                {
                    // 解析文件名获取boltmodel
                    var fname = multipartFile.FileName.Split('-');
                    if (fname.Length >= 4)
                    {
                        boltModel = fname[2];
                    }

                    // 保存文件
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        multipartFile.CopyTo(stream);
                    }
                    
                }
                catch (Exception ex)
                {
                    // 处理异常
                    // 注意：在实际应用中，建议记录日志而不是忽略异常
                }
            }

            // 批量修改
            //应用到整个法兰
            if (isall == "1")
            {
                // 法兰过滤

                List<UltrasonicChannelConfigModel> _list = ullist.Where(t => t.SectionName == ultraModel.SectionName).ToList() ;
                _list.ForEach(item =>
                {
                    //item.PreloadCalCoeffs = PreloadCalCoeffs;
                    //item.TempCalibCoeff = TempCalibCoeff;
                    //item.PreloadLowerLimmit = PreloadLowerLimmit;
                    //item.PreloadUpperLimmit = PreloadUpperLimmit;
                    //item.DispatcherID = DispatcherID;
                    //item.StandardFilePath = filePath;

                    DauManagement.EditUltrasonic(new UltrasonicChannelConfig() { 
                        ChannelNumber = item.ChannelNumber,
                        DauID = item.DauID,
                        DispatcherChannelID = item.DispatcherChannelID,
                        DispatcherID = DispatcherID,
                        PreloadCalCoeffs = PreloadCalCoeffs,
                        PreloadLowerLimmit = PreloadLowerLimmit,
                        PreloadUpperLimmit = PreloadUpperLimmit,
                        StandardFilePath =item.StandardFilePath,
                        TempCalibCoeff = (float)TempCalibCoeff,
                        WindTurbineID = item.WindTurbineID,
                        BoltModel = string.IsNullOrEmpty(boltModel) ? item.BoltModel : boltModel,
                    });
                });

                // 修改对不能批量修改的部分
                var ultra = ullist.FirstOrDefault(p => p.ChannelNumber == ChannelNumber);
                UltrasonicChannelConfig newconfig = new UltrasonicChannelConfig()
                {
                    ChannelNumber = ultra.ChannelNumber,
                    DauID = ultra.DauID,
                    WindTurbineID = ultra.WindTurbineID,
                    StandardFilePath = ultra.StandardFilePath,
                    PreloadCalCoeffs = PreloadCalCoeffs,
                    TempCalibCoeff = (float)TempCalibCoeff,
                    PreloadLowerLimmit = PreloadLowerLimmit,
                    PreloadUpperLimmit = PreloadUpperLimmit,
                    DispatcherID = DispatcherID,
                    DispatcherChannelID = Convert.ToInt32(Request.Form["dispatcherChannelID"]),
                    BoltModel = string.IsNullOrEmpty(boltModel) ? ultra.BoltModel : boltModel,
                };
                if(filePath != "")
                {
                    newconfig.StandardFilePath =System.IO.File.ReadAllText(filePath);
                }
                DauManagement.EditUltrasonic(newconfig);

                return true;
            }else
            {
                //UltrasonicChannelConfig ultra = ullist.FirstOrDefault(p => p.ChannelNumber == ChannelNumber);
                var ultra = ullist.FirstOrDefault(p => p.ChannelNumber == ChannelNumber);
                if (ultra != null)
                {

                    //ultra.StandardFilePath = filePath;
                    //ultra.PreloadCalCoeffs = Request.Form["preloadCalCoeffs"];
                    //ultra.TempCalibCoeff = Convert.ToInt32(Request.Form["tempCalibCoeff"]);
                    //ultra.PreloadLowerLimmit = Convert.ToInt32(Request.Form["preloadLowerLimmit"]);
                    //ultra.PreloadUpperLimmit = Convert.ToInt32(Request.Form["preloadUpperLimmit"]);
                    //ultra.DispatcherID = Convert.ToInt32(Request.Form["dispatcherID"]);

                    UltrasonicChannelConfig newconf = new UltrasonicChannelConfig()
                    {
                        ChannelNumber = ultra.ChannelNumber,
                        DauID = ultra.DauID,
                        WindTurbineID = ultra.WindTurbineID,
                        StandardFilePath = ultra.StandardFilePath,
                        PreloadCalCoeffs = Request.Form["preloadCalCoeffs"],
                        TempCalibCoeff = (float)Convert.ToDecimal(Request.Form["tempCalibCoeff"]),
                        PreloadLowerLimmit = Convert.ToSingle(Request.Form["preloadLowerLimmit"]),
                        PreloadUpperLimmit = Convert.ToSingle(Request.Form["preloadUpperLimmit"]),
                        DispatcherID = Convert.ToInt32(Request.Form["dispatcherID"]),
                        DispatcherChannelID = Convert.ToInt32(Request.Form["dispatcherChannelID"]),
                        BoltModel = string.IsNullOrEmpty(boltModel) ?  ultra.BoltModel: boltModel,
                    };
                    if(filePath != "")
                    {
                        newconf.StandardFilePath = filePath;
                        newconf.StandardContent = System.IO.File.ReadAllText(filePath);
                    }
                    DauManagement.EditUltrasonic(newconf);

                    return true;
                }

            }

            return false;
        }


    }
}
