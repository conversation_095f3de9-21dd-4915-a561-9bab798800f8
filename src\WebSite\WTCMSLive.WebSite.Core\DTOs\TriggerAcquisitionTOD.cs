﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class TriggerAcquisitionTOD
    {
        public string TurbineID { get; set; }
        public string TriggerRuleName { get; set; }
        public string TriggerMeasDefName { get; set; }
        public string? ConditionMonitoringLocIds { get; set; } = null;
        public string[]? TriggerData { get; set; } = new string[0];
        public string? isAddType { get; set; } = null;
        public string? dauid { get; set; }
        public string? triggertime { get; set; } = null;
    }
}
