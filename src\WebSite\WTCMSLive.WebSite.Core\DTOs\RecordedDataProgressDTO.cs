using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 录波数据接收进度DTO
    /// </summary>
    public class RecordedDataProgressDTO
    {
        public string ParkID { get; set; } = string.Empty;
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; } = string.Empty;

        /// <summary>
        /// DAU ID
        /// </summary>
        public string DauID { get; set; } = string.Empty;

        /// <summary>
        /// DAU IP地址
        /// </summary>
        public string DauIP { get; set; } = string.Empty;

        /// <summary>
        /// 录波开启状态
        /// </summary>
        public bool IsRecordingActive { get; set; }

        /// <summary>
        /// 录波开启时间
        /// </summary>
        public DateTime? RecordingStartTime { get; set; }

        /// <summary>
        /// 总文件数量
        /// </summary>
        public int TotalFileCount { get; set; }

        /// <summary>
        /// 已接收文件数量
        /// </summary>
        public int ReceivedFileCount { get; set; }

        /// <summary>
        /// 接收进度百分比
        /// </summary>
        public double ProgressPercentage => TotalFileCount > 0 ? (double)ReceivedFileCount / TotalFileCount * 100 : 0;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Recorded文件中的rcdata表记录DTO
    /// </summary>
    public class RcdataRecordDTO
    {
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 采集时间
        /// </summary>
        public DateTime CollectionTime { get; set; }

        /// <summary>
        /// 是否已接收
        /// </summary>
        public bool IsReceived { get; set; }

        /// <summary>
        /// 文件创建时间（SFTP上的文件）
        /// </summary>
        public DateTime? FileCreationTime { get; set; }
    }

    /// <summary>
    /// 录波数据监控请求DTO
    /// </summary>
    public class RecordedDataMonitorRequestDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        [Required]
        public string ParkID { get; set; } = string.Empty;

        /// <summary>
        /// 要监控的DAU列表
        /// </summary>
        [Required]
        public List<DAUDirectDTO> DAUs { get; set; } = new List<DAUDirectDTO>();
    }

    /// <summary>
    /// 录波数据监控响应DTO
    /// </summary>
    public class RecordedDataMonitorResponseDTO
    {
        /// <summary>
        /// 监控是否成功启动
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 监控的DAU进度列表
        /// </summary>
        public List<RecordedDataProgressDTO> ProgressList { get; set; } = new List<RecordedDataProgressDTO>();
    }
}
