﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.BusinessEntity.SVM;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: Maxy
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从业务层实体转换为DA层实体
    /// </summary>
    public static class ConvertEntityBusinessToDASVM
    {
        /// <summary>
        /// 晃度仪实体转换
        /// </summary>
        /// <param name="_svm"></param>
        /// <returns></returns>
        public static Entity.Models.SVMonitor ConvertSVM(SVMUnit _svm)
        {
            SVMonitor svMonitor = new SVMonitor();
            svMonitor.WindTurbineID = _svm.AssocWindTurbineID;
            svMonitor.SVMName = _svm.SVMName;
            svMonitor.SVMCode = _svm.SVMCode;
            svMonitor.ModbusAddress = int.Parse(_svm.ModbusAddress);
            //wangy 2015年11月26日 09:53:48 采集方式字段去掉
            //svMonitor.AcquisitionType = (sbyte)_svm.SVMAcqType;
            svMonitor.SVMSoftwareVersion = _svm.SVMSoftwareVersion;
            svMonitor.ComponentID = _svm.ComponentID;
            if (_svm.SVMRegisterList != null && _svm.SVMRegisterList.Count > 0)
            {
                _svm.SVMRegisterList.ForEach(c => svMonitor.SVMRegisters.Add(ConvertRegister(c)));
            }
            return svMonitor;
        }
        /// <summary>
        /// 寄存器实体转换
        /// </summary>
        /// <param name="_register"></param>
        /// <returns></returns>
        public static Entity.Models.SVMRegister ConvertRegister(BusinessEntity.SVM.SVMRegister _register)
        {
            Entity.Models.SVMRegister svmRegisterModel = new Entity.Models.SVMRegister();

            if (_register.AssocWindTurbineID != null)
            {
                svmRegisterModel.WindTurbineID = _register.AssocWindTurbineID;
            }
            svmRegisterModel.RegisterAdress = int.Parse(_register.SVMRegisterAdr);
            svmRegisterModel.MeasLocationID = _register.SVMMeasLocId;
            svmRegisterModel.ComponentID = _register.ComponentID;
            svmRegisterModel.ConvertCoefficient = 1;
            svmRegisterModel.RegisterType = 0;
            svmRegisterModel.RegisterStorrageType = 0;
            svmRegisterModel.ByteArrayType = 0;
            return svmRegisterModel;
        }

        public static Entity.Models.SVMWaveDefinition ConvertWaveDefinition(WaveDef_SVM _waveDef)
        {
            Entity.Models.SVMWaveDefinition waveDef = new Entity.Models.SVMWaveDefinition();
            waveDef.WindTurbineID = _waveDef.WindTurbineID;
            waveDef.MeasDefinitionID = int.Parse(_waveDef.MeasDefinitionID);
            if (!string.IsNullOrEmpty(_waveDef.WaveDefinitionID))
            {
                waveDef.WaveDefinitionID = int.Parse(_waveDef.WaveDefinitionID);
            }
            waveDef.MeasLocationID = _waveDef.MeasLocationID;
            waveDef.MeasDefinitionID = int.Parse(_waveDef.MeasDefinitionID);
            waveDef.ParamType = (short)_waveDef.ParamType;
            waveDef.WaveDefinitionName = _waveDef.WaveDefinitionName;
            waveDef.SampleLength = short.Parse(_waveDef.SampleLength.ToString());
            waveDef.SampleRate = decimal.Parse(_waveDef.SampleRate.ToString());
            return waveDef;
        }
        /// <summary>
        /// 波形定义(角度)实体转换
        /// </summary>
        /// <param name="_waveDefAng"></param>
        /// <returns></returns>
        public static Entity.Models.SVMWaveDefinition ConvertWaveDefinitionANG(SVMWaveDefinition_Angle _waveDefAng)
        {
            Entity.Models.SVMWaveDefinition waveDef = new Entity.Models.SVMWaveDefinition();
            waveDef.WindTurbineID = _waveDefAng.WindTurbineID;
            waveDef.MeasDefinitionID = int.Parse(_waveDefAng.MeasDefinitionID);
            if (!string.IsNullOrEmpty(_waveDefAng.WaveDefinitionID))
            {
                waveDef.WaveDefinitionID = int.Parse(_waveDefAng.WaveDefinitionID);
            }
            waveDef.MeasLocationID = _waveDefAng.MeasLocationID;
            waveDef.MeasDefinitionID = int.Parse(_waveDefAng.MeasDefinitionID);
            waveDef.ParamType = (short)_waveDefAng.ParamType;
            waveDef.WaveDefinitionName = _waveDefAng.WaveDefinitionName;
            return waveDef;
        }
        /// <summary>
        /// 波形定义(加速度)实体转换
        /// </summary>
        /// <param name="_waveDefAcc"></param>
        /// <returns></returns>
        public static Entity.Models.SVMWaveDefinition ConvertWaveDefinitionACC(SVMWaveDefinition_ACC _waveDefAcc)
        {
            Entity.Models.SVMWaveDefinition waveDef = new Entity.Models.SVMWaveDefinition();

            waveDef.WindTurbineID =_waveDefAcc.WindTurbineID;
            waveDef.MeasDefinitionID = int.Parse(_waveDefAcc.MeasDefinitionID);
            if (!string.IsNullOrEmpty(_waveDefAcc.WaveDefinitionID))
            {
                waveDef.WaveDefinitionID = int.Parse(_waveDefAcc.WaveDefinitionID);
            }
            waveDef.MeasLocationID = _waveDefAcc.MeasLocationID;
            waveDef.ParamType = (short)_waveDefAcc.ParamType;
            waveDef.WaveDefinitionName = _waveDefAcc.WaveDefinitionName;
            return waveDef;
        }

        /// <summary>
        /// 测量位置实体转换
        /// </summary>
        /// <param name="_measLoc"></param>
        /// <returns></returns>
        public static Entity.Models.SVMMeasLocation ConvertMeasLocation(MeasLoc_SVM _measLoc)
        {
            Entity.Models.SVMMeasLocation svmMeasLoc = new SVMMeasLocation();
            svmMeasLoc.ComponentID = _measLoc.ComponentID;
            svmMeasLoc.MeasLocationID = _measLoc.MeasLocationID;
            svmMeasLoc.WindTurbineID = _measLoc.WindTurbineID;
            svmMeasLoc.MeasurementLocationName = _measLoc.MeasurementLocationName;
            svmMeasLoc.ParamType = (short)_measLoc.ParamType;
            svmMeasLoc.OrderSeq = _measLoc.OrderSeq;
            return svmMeasLoc;
        }
    }
}
