﻿using System.Text;

namespace WindCMS.GWServer.Entity;

/// <summary>
/// 在线报告信息
/// </summary>
public class DauStationsEntity
{
    /// <summary>
    /// 采集站类型
    /// </summary>
    public int DauType { get; set; }
    
    /// <summary>
    /// 采集状态
    /// </summary>
    public int DauStatus { get; set; }
    
    /// <summary>
    /// 是否是网关设备
    /// </summary>
    public bool IsGatewayDevice { get; set; }
    
    /// <summary>
    /// 固件版本号
    /// </summary>
    public string Version { get; set; } = null!;
    
    /// <summary>
    /// 风场ID
    /// </summary>
    public string WindParkId { get; set; } = null!;
    
    /// <summary>
    /// 机组ID
    /// </summary>
    public string WindTurbineId { get; set; } = null!;

    /// <summary>
    /// 字节数组转换为在线报告信息
    /// </summary>
    /// <param name="responseData"></param>
    /// <param name="offset"></param>
    /// <returns></returns>
    public static DauStationsEntity ByteArrayToDauBaseParameter(byte[] responseData, int offset)
    {
        var dauStationsInformation = new DauStationsEntity();

        // 固件版本号
        var item1 = responseData[offset];
        offset += 1;
        var item2 = responseData[offset];
        offset += 1;
        var item3 = responseData[offset];
        offset += 1;
        var item4 = responseData[offset];
        offset += 1;
        dauStationsInformation.Version = $"{item1}.{item2}.{item3}.{item4}";
        
        // DAU 类型
        dauStationsInformation.DauType = responseData[offset];
        offset++;
        
        // 采集状态
        dauStationsInformation.DauStatus = responseData[offset];
        offset++;
        
        // 是否网关设备
        var isGatewayDevice = responseData[offset];
        dauStationsInformation.IsGatewayDevice = isGatewayDevice != 0;
        offset++;
        
        // 风场ID所占字节长度
        var windParkIdByteArrayLength = responseData[offset];
        offset++;
        
        // 机组ID所占字节长度
        var windTurbineIdByteArrayLength = responseData[offset];
        offset++;
        
        // 风场ID
        var windParkIdByteArray = new byte[windParkIdByteArrayLength];
        Buffer.BlockCopy(responseData, offset, windParkIdByteArray, 0, windParkIdByteArray.Length);
        dauStationsInformation.WindParkId = Encoding.ASCII.GetString(windParkIdByteArray);
        offset += windParkIdByteArrayLength;
        
        // 机组ID
        var windTurbineIdByteArray = new byte[windTurbineIdByteArrayLength];
        Buffer.BlockCopy(responseData, offset, windTurbineIdByteArray, 0, windTurbineIdByteArray.Length);
        dauStationsInformation.WindTurbineId = Encoding.ASCII.GetString(windTurbineIdByteArray);
        
        return dauStationsInformation;
    }
}