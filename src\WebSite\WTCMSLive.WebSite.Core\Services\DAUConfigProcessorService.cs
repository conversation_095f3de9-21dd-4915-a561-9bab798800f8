using CMSFramework.BusinessEntity;
using CMSFramework.DAUFacadeBase;
using CMSFramework.EF;
using Microsoft.EntityFrameworkCore;
using WindCMS.GWServer.Entity;
using WindCMS.GWServer.ListenerServer;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.DTOs;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// DAU配置处理后台服务
    /// 从队列中读取DAU配置数据并存储到数据库
    /// </summary>
    public class DAUConfigProcessorService : BackgroundService
    {
        private readonly ILogger<DAUConfigProcessorService> _logger;
        private readonly TurbineConfigSyncService _turbineConfigSyncService;

        public DAUConfigProcessorService(
            ILogger<DAUConfigProcessorService> logger,
            TurbineConfigSyncService turbineConfigSyncService)
        {
            _logger = logger;
            _turbineConfigSyncService = turbineConfigSyncService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("[DAUConfigProcessorService]DAU配置处理服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 从队列中读取数据（阻塞等待）
                    //var config = await _queueService.DequeueAsync(stoppingToken);

                    bool processedAny = false;
                    while (ListenerReadHandler.DauInfoQueue.TryDequeue(out DauInfoEntity? item))
                    {
                        processedAny = true;
                        // 处理配置数据

                        if(!item.DauConfig.IsGatewayDevice)
                        {
                            //if (item.WtLiveDbByteArray == null || item.WtLiveDbByteArray.Length == 0)
                            //{
                            //    await BuildDevtree(item);
                            //}
                            //await ProcessDAUConfigAsync(item);    
                        }

                        await BuildDevtree(item);
                        await ProcessDAUConfigToSqliteAsync(item.WtLiveDbByteArray);

                        // 修改dau的IP及状态
                        DauManagement.UpdateDAUIPAndOffline(
                            item.DauConfig.WindTurbineId,
                            item.DauConfig.DauDeviceType.GetHashCode().ToString(),
                            item.DauIp,
                            CMSFramework.TypeDef.EnumDauOnOffStatus.On);

                    }

                    // 如果没有处理任何数据，睡一会儿避免空转
                    if (!processedAny)
                    {
                        await Task.Delay(1000, stoppingToken);
                    }

                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("[DAUConfigProcessorService]服务正在停止");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[DAUConfigProcessorService]处理DAU配置时发生未预期的错误");
                    // 继续处理下一条数据，不中断服务
                    await Task.Delay(1000, stoppingToken); // 短暂延迟避免快速失败循环
                }
            }

            _logger.LogInformation("[DAUConfigProcessorService]DAU配置处理服务已停止");
        }


        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        private async Task ProcessDAUConfigToSqliteAsync(byte[] wtLiveDbByteArray)
        {
            if (wtLiveDbByteArray != null && wtLiveDbByteArray.Length > 0)
            {
                await _turbineConfigSyncService.SyncTurbineConfig(wtLiveDbByteArray);
            }
        }

        /// <summary>
        /// 构建
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        private async Task BuildDevtree(DauInfoEntity? config)
        {
            // 添加风场
            var _park = DevTreeManagement.GetWindPark(config.DauConfig.WindParkId);
            if(_park == null)
            {
                DevTreeManagement.AddWindPark(new WindPark()
                {
                    WindParkID = config.DauConfig.WindParkId,
                    WindParkCode = config.DauConfig.WindParkId,
                    WindParkName = config.DauConfig.WindParkId,
                    Address = "",
                    Area = "",
                    location = "",
                    Country = "",
                    OperationalDate = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")),
                }) ;
            }

            // 添加机组
            var _turbine = DevTreeManagement.GetWindTurbine(config.DauConfig.WindTurbineId);
            if( _turbine == null)
            {
                DevTreeManagement.AddWindTurbine_Manager(new WindTurbine()
                {
                    WindParkID = config.DauConfig.WindParkId,
                    WindTurbineID = config.DauConfig.WindTurbineId,
                    WindTurbineCode = config.DauConfig.WindTurbineId,
                    ConfigVersion = 0.ToString(),
                    OperationalDate = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")),
                    Location = "",
                    WindTurbineName = config.DauConfig.WindTurbineId,
                    MinWorkingRotSpeed = 0,
                });
            }

            // 添加dau
            var _dau = DauManagement.GetDAUNameById(config.DauConfig.WindTurbineId, config.DauConfig.DauDeviceType.GetHashCode().ToString());
            if( _dau == null)
            {
                DauManagement.AddDAU(new WindDAU()
                {
                    WindParkID = config.DauConfig.WindParkId,
                    WindTurbineID = config.DauConfig.WindTurbineId,
                    DauID = config.DauConfig.DauDeviceType.GetHashCode().ToString(),
                    DAUName = config.DauConfig.DauDeviceType.ToString(),
                    DauOnOffStatus = CMSFramework.TypeDef.EnumDauOnOffStatus.On,
                    DAUType = EnumDAUType.Vibration,
                    IP = config.DauIp,
                    WaveSaveInterval = 0,
                    TrendSaveInterval = 0,
                    DataAcquisitionInterval = 0,
                    IsAvailable = true,
                    MeasDefVersion = 0,
                    DAUMeasDefVersion = 0,
                    ServerAddress = config.DauIp,
                    Port = config.DauConfig.ConfigToolPort,
                });
            }

        }
        /// <summary>
        /// 处理DAU配置数据,获取到context对象存储至数据库
        /// </summary>
        private async Task ProcessDAUConfigAsync(DauInfoEntity? config)
        {
            if (config == null)
            {
                _logger.LogWarning("[ProcessDAUConfigAsync]接收到空的配置数据");
                return;
            }

            // 验证必要字段
            if (string.IsNullOrEmpty(config.DauConfig.WindParkId) || 
                string.IsNullOrWhiteSpace(config.DauConfig.WindTurbineId))
            {
                _logger.LogWarning("[ProcessDAUConfigAsync]配置数据缺少必要字段: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.DauConfig.WindParkId, config.DauConfig.WindTurbineId);
                return;
            }

            try
            {
                _logger.LogInformation("[ProcessDAUConfigAsync]开始处理DAU配置: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}, IP={IP}",
                    config.DauConfig.WindParkId, config.DauConfig.WindTurbineId, config.DauConfig.DauStatus);

                // 1. 处理设备信息，包含风场，机组、测点等所有配置信息
                //await ProcessDevicekAsync(config.DauContext);

                // 2. 处理dau配置，包含sftp、对时等
                //await ProcessDAUAdvancedAsync(config.DauConfig);

                _logger.LogInformation("[ProcessDAUConfigAsync]成功处理DAU配置: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config?.DauConfig.WindParkId, config?.DauConfig.WindTurbineId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessDAUConfigAsync]处理DAU配置失败: WindParkID={WindParkID}, WindTurbineID={WindTurbineID}",
                    config.DauConfig.WindParkId, config.DauConfig.WindTurbineId);
            }
        }

        private async Task ProcessDAUAdvancedAsync(DauConfigExtension? dauContext)
        {
            if(dauContext == null)
            {
                return;
            }
            throw new NotImplementedException();
        }

        /// <summary>
        /// 处理风场数据
        /// </summary>
        private async Task ProcessDevicekAsync(DauWorkContext? dauContext)
        {
            if(dauContext == null)
            {
                return;
            }
            try
            {
                // 处理设备
                await ProcessWindTurbineAsync(dauContext.DauTurbine);

                // 处理dau
                await ProcessDAUAsync(dauContext.Dau);

                // 处理测量定义
                await ProcessMeasDefAsync(dauContext.AllEnableMdf);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessWindParkAsync]处理风场数据失败: WindParkID={WindParkID}", dauContext.DauTurbine.WindParkID);
                throw;
            }
        }

        private async Task ProcessMeasDefAsync(List<MeasDefinition> allEnableMdf)
        {
            if(allEnableMdf == null)
            {
                return;
            }
            throw new NotImplementedException();
        }

        /// <summary>
        /// 处理机组数据
        /// </summary>
        private async Task ProcessWindTurbineAsync(WindTurbine? turbine)
        {
            if (turbine == null)
            {
                return;
            }
            try
            {
                using (var ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {

                    // 构建风场

                    var existingPark = await ctx.DevWindParks.FindAsync(turbine.WindParkID);

                    if (existingPark == null)
                    {
                        // 创建新风场
                        var newPark = new WindPark
                        {
                            WindParkID = turbine.WindParkID,
                            WindParkName = turbine.WindParkID, // 使用ID作为名称
                            WindParkCode = turbine.WindParkID,
                            OperationalDate = DateTime.Now,
                            WindTurbineList = new List<WindTurbine>()
                        };

                        ctx.DevWindParks.Add(newPark);
                        await ctx.SaveChangesAsync();

                        _logger.LogInformation("[ProcessWindParkAsync]创建新风场: WindParkID={WindParkID}", turbine.WindParkID);
                    }
                    else
                    {
                        _logger.LogDebug("[ProcessWindParkAsync]风场已存在: WindParkID={WindParkID}", turbine.WindParkID);
                    }


                    // 存储机组
                    var existingTurbine = await ctx.DevWindTurbines
                        .FirstOrDefaultAsync(t => t.WindTurbineID == turbine.WindTurbineID);

                    if (existingTurbine == null)
                    {
                        // 创建新机组
                        //var newTurbine = new WindTurbine
                        //{
                        //    WindTurbineID = config.WindTurbienID,
                        //    WindTurbineName = string.IsNullOrWhiteSpace(config.WindTurbienName)
                        //        ? config.WindTurbienID
                        //        : config.WindTurbienName,
                        //    WindParkID = config.WindParkID,
                        //    WindTurbineModel = "Unknown",
                        //    OperationalDate = DateTime.Now,
                        //    TurComponentList = new List<WindTurbineComponent>()
                        //};

                        ctx.DevWindTurbines.Add(turbine);
                        await ctx.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessWindParkAsync]处理风场数据失败: WindParkID={WindParkID}", turbine.WindParkID);
                throw;
            }
        }

        /// <summary>
        /// 处理DAU数据
        /// </summary>
        private async Task ProcessDAUAsync(WindDAU? dau)
        {
            try
            {
                using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    
                    var existingDAU = await ctx.DAUnits
                        .FirstOrDefaultAsync(d => d.WindTurbineID == dau.WindParkID && d.DauID == dau.DauID);


                    if (existingDAU == null)
                    {
                        // 创建新DAU
                        //var newDAU = new WindDAU
                        //{
                        //    WindTurbineID = config.WindTurbienID,
                        //    DauID = dauId,
                        //    DAUName = $"{turbineName}-DAU{dauId}",
                        //    WindParkID = config.WindParkID,
                        //    IP = config.DAUIP ?? "0.0.0.0",
                        //    Port = port,
                        //    IsAvailable = isAvailable,
                        //    DAUType = EnumDAUType.Vibration, // 默认振动监测类型
                        //    DeviceID = 1,
                        //    DataAcquisitionInterval = 60,
                        //    TrendSaveInterval = 60,
                        //    WaveSaveInterval = 0,
                        //    MeasDefVersion = 0,
                        //    DAUMeasDefVersion = 0,
                        //    DAUSoftwareVersion = "0",
                        //    DAUChannelList = new List<DAUChannelV2>(),
                        //    RotSpeedChannelList = new List<DAUChannel_RotSpeed>(),
                        //    ProcessChannelList = new List<DAUChannel_Process>(),
                        //    VoltageCurrentList = new List<DAUChannel_VoltageCurrent>()
                        //};
                        dau.DauOnOffStatus = CMSFramework.TypeDef.EnumDauOnOffStatus.On;
                        ctx.DAUnits.Add(dau);
                        await ctx.SaveChangesAsync();

                        _logger.LogInformation("[ProcessDAUAsync]创建新DAU: WindTurbineID={WindTurbineID}, DauID={DauID}, IP={IP}, Port={Port}",
                            dau.WindTurbineID, dau.DauID, dau.IP, dau.Port);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessDAUAsync]处理DAU数据失败: WindTurbineID={WindTurbineID}", dau.WindTurbineID);
                throw;
            }
        }
    }
}

