﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel
{
    public enum EnumLogType
    {
        [Description("用户管理")]
        UserManagementLog = 0,
        [Description("采集单元配置")]
        DAUManagementLog = 1,
        [Description("设备树配置")]
        DevTreeManagementLog = 2,
        [Description("报警事件")]
        AlarmEventLog = 3,
        [Description("晃度仪配置")]
        SVMManagementLog = 4,
        [Description("系统运行日志")]
        SystemRunningLog = 5,
    }
}
