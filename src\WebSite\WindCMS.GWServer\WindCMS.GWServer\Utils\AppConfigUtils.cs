﻿using System.Configuration;

namespace WindCMS.GWServer.Utils;

/// <summary>
/// AppConfig 工具类
/// </summary>
internal static class AppConfigUtils
{
    /// <summary>
    /// 监听端口
    /// </summary>
    private static int? _listenerPort;
    internal static int ListenerPort
    {
        get
        {
            if (_listenerPort != null)
            {
                return _listenerPort.Value;
            }
            
            try
            {
                _listenerPort = Convert.ToInt32(ConfigurationManager.AppSettings["ListenerPort"]);
            }
            catch
            {
                _listenerPort = 5050;
            }

            return _listenerPort.Value;
        }
    }
    
    /// <summary>
    /// 默认30秒检查一次
    /// </summary>
    private static TimeSpan? _dauNoResponseTime { get; set; }
    internal static TimeSpan DauNoResponseTime
    {
        get
        {
            if (_dauNoResponseTime != null)
            {
                return _dauNoResponseTime.Value;
            }

            try
            {
                _dauNoResponseTime =
                    TimeSpan.Parse(ConfigurationManager.AppSettings["DAUNoResponseTime"] ?? "0.00:05:00");
            }
            catch
            {
                _dauNoResponseTime = new TimeSpan(0, 0, 30);
            }

            return _dauNoResponseTime.Value;               
        }
    }
    
    
    /// <summary>
    /// 默认30秒检查一次
    /// </summary>
    private static bool? _dauSynchronizationContext { get; set; }
    internal static bool DauSynchronizationContext
    {
        get
        {
            if (_dauSynchronizationContext != null)
            {
                return _dauSynchronizationContext.Value;
            }
    
            try
            {
                _dauSynchronizationContext =
                    bool.Parse(ConfigurationManager.AppSettings["DauSynchronizationContext"] ?? "true");
            }
            catch
            {
                _dauSynchronizationContext = true;
            }
    
            return _dauSynchronizationContext.Value;               
        }
    }
}