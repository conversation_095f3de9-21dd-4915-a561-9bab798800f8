using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// DAU状态统计总览DTO
    /// </summary>
    public class DAUStatusCountResponseDTO
    {
        /// <summary>
        /// 全部DAU状态统计
        /// </summary>
        public DAUStatusSummaryDTO OverallSummary { get; set; }

        /// <summary>
        /// 各风场DAU状态统计列表
        /// </summary>
        public List<WindParkDAUStatusDTO> WindParkStatusList { get; set; } = new List<WindParkDAUStatusDTO>();

        /// <summary>
        /// 数据获取时间
        /// </summary>
        public DateTime DataTime { get; set; }
    }

    /// <summary>
    /// 风场DAU状态统计DTO
    /// </summary>
    public class WindParkDAUStatusDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string WindParkName { get; set; }

        /// <summary>
        /// 风场DAU状态统计
        /// </summary>
        public DAUStatusSummaryDTO StatusSummary { get; set; }

        /// <summary>
        /// 风场内部按状态统计
        /// </summary>
        public List<DAUStatusStatisticDTO> StatusStatistics { get; set; } = new List<DAUStatusStatisticDTO>();

        /// <summary>
        /// 风场内部按DAU名称统计
        /// </summary>
        public List<DAUNameStatisticDTO> NameStatistics { get; set; } = new List<DAUNameStatisticDTO>();

        /// <summary>
        /// 该风场下的DAU详细列表
        /// </summary>
        public List<DAUItemDTO> DAUList { get; set; } = new List<DAUItemDTO>();
    }

    /// <summary>
    /// 机组DAU状态详情DTO
    /// </summary>
    public class TurbineDAUStatusDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 机组名称
        /// </summary>
        public string WindTurbineName { get; set; }

        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string WindParkName { get; set; }

        /// <summary>
        /// 机组DAU状态统计
        /// </summary>
        public DAUStatusSummaryDTO StatusSummary { get; set; }

        /// <summary>
        /// 该机组下的DAU详细状态列表
        /// </summary>
        public List<DAUItemDTO> DAUList { get; set; } = new List<DAUItemDTO>();

        /// <summary>
        /// 机组整体状态（基于DAU状态计算）
        /// </summary>
        public int OverallStatus { get; set; }

        /// <summary>
        /// 机组整体状态描述
        /// </summary>
        public string OverallStatusDescription { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastUpdateTime { get; set; }
    }

    /// <summary>
    /// DAU状态统计DTO（按状态分组）
    /// </summary>
    public class DAUStatusStatisticDTO
    {
        /// <summary>
        /// 状态值
        /// </summary>
        public int StatusValue { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 该状态的DAU数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 该状态的DAU列表
        /// </summary>
        public List<string> DAUList { get; set; } = new List<string>();

        /// <summary>
        /// 占风场总DAU的百分比
        /// </summary>
        public double Percentage { get; set; }
    }

    /// <summary>
    /// DAU名称统计DTO（按DAU名称分组）
    /// </summary>
    public class DAUNameStatisticDTO
    {
        /// <summary>
        /// DAU名称
        /// </summary>
        public string DAUName { get; set; }

        /// <summary>
        /// 该名称的DAU数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 该名称DAU的状态分布
        /// </summary>
        public List<DAUNameStatusDistributionDTO> StatusDistribution { get; set; } = new List<DAUNameStatusDistributionDTO>();

        /// <summary>
        /// 占风场总DAU的百分比
        /// </summary>
        public double Percentage { get; set; }

        public int StatusValue { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }



        /// <summary>
        /// 该状态下该名称的DAU列表（机组ID_DAU ID）
        /// </summary>
        public List<string> DAUIdentifiers { get; set; } = new List<string>();
    }

}

    /// <summary>
    /// DAU名称状态分布DTO
    /// </summary>
    public class DAUNameStatusDistributionDTO
    {
        /// <summary>
        /// 状态值
        /// </summary>
        public int StatusValue { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 该状态下该名称的DAU数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 该状态下该名称的DAU列表（机组ID_DAU ID）
        /// </summary>
        public List<string> DAUIdentifiers { get; set; } = new List<string>();
    }

    /// <summary>
    /// DAU状态级别枚举（用于机组整体状态计算）
    /// </summary>
    public enum DAUStatusLevel
    {
        /// <summary>
        /// 离线
        /// </summary>
        Offline = -1,

        /// <summary>
        /// 正常
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 注意（未知、无数据到达）
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 故障（通信错误、传感器故障、转速故障）
        /// </summary>
        Fault = 2,

        /// <summary>
        /// 报警
        /// </summary>
        Alarm = 3
    }

