﻿using System.Text;
using CMSFramework.BusinessEntity;
using CMSFramework.DAUFacadeBase;
using CMSFramework.Logger;
using WindCMS.GWServer.Agent;
using WindCMS.GWServer.Entity;
using WindCMS.GWServer.Utils;
using WTCMSLive.BusinessModel;
using WTCMSLive.DAUFacade;

namespace WindCMS.GWServer.ListenerServer;

/// <summary>
/// 监听写事件处理
/// </summary>
public class ListenerWriteHandler : IListenerWriteHandler
{
    /// <summary>
    /// TCP 客户端
    /// </summary>
    private TcpListenerAgent Agent { get; set; }
    
    private IDauConfigExtension DauConfig { get; set; }
    
    /// <summary>
    /// 构造方法
    /// </summary>
    public ListenerWriteHandler(TcpListenerAgent tcpListenerAgent)
    {
        Agent = tcpListenerAgent;
        DauConfig = Agent.DauWorkContext.DauFacade as IDauConfigExtension;
    }

    /// <summary>
    /// 启动采集
    /// </summary>
    /// <returns></returns>
    public bool StartDaq()
    {
        try
        {
            DauConfig.ExtensionStartAcquisition();
        }
        catch (Exception)
        {
            Agent.ResultExecution = false;
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 停止采集
    /// </summary>
    /// <returns></returns>
    public bool StopDaq()
    {
        try
        {
            DauConfig.ExtensionStopAcquisition();
        }
        catch (Exception)
        {
            Agent.ResultExecution = false;
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置配置工具网络参数(IP、Port)
    /// </summary>
    /// <returns></returns>
    public bool SetConfigToolNetworkParameter()
    {
        try
        {
            var configExtension = Agent.DauConfigExtension;
            DauConfig.SetConfigToolNetworkParameter(configExtension.ConfigToolIpAddress, configExtension.ConfigToolPort);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SetConfigToolNetworkParameter] error.", e);
        }
        return Agent.ResultExecution;
    }
    
    /// <summary>
    /// 获取配置工具网络参数(IP、Port)
    /// </summary>
    /// <returns></returns>
    public bool GetConfigToolNetworkParameter()
    {
        try
        {
            var dauConfigExtension = new DauConfigExtension();
            Agent.ResultExecution = DauConfig.GetConfigToolNetworkParameter(dauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [GetConfigToolNetworkParameter] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置DAU基础信息(配置风场ID、机组ID、设备类型)
    /// </summary>
    public bool SetDauBaseParameter()
    {
        try
        {
            DauConfig.SetDauBaseParameter(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SetDauBaseParameter] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 获取DAU基础信息(配置风场ID、机组ID、设备类型)
    /// </summary>
    /// <returns></returns>
    public bool GetDauBaseParameter()
    {
        try
        {
            Agent.ResultExecution = DauConfig.GetDauBaseParameter(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [GetDauBaseParameterRequest] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置采集策略(波形间隔、特征值采集间隔、录播天数、录播功能是否开启、启动采集延时、是否存储触发采集)
    /// </summary>
    /// <returns></returns>
    public bool SetDauCollectionStrategyRequest()
    {
        return DauCollectionStrategyRequest(true);
    }

    /// <summary>
    /// 设置采集策略
    /// </summary>
    /// <param name="bypassed"></param>
    /// <returns></returns>
    private bool DauCollectionStrategyRequest(bool bypassed)
    {
        try
        {
            if (bypassed)
            {
                if (Agent.IsGatewayDevice)
                {
                    return true;
                }
            }
            DauConfig.SetDauCollectionStrategy(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SetDauCollectionStrategyRequest] error.", e);
        }

        return Agent.ResultExecution;
    }

    /// <summary>
    /// 获取采集策略(波形间隔、特征值采集间隔、录播天数、录播功能是否开启、启动采集延时、是否存储触发采集)
    /// </summary>
    /// <returns></returns>
    public bool GetDauCollectionStrategy()
    {
        try
        {
            Agent.ResultExecution = DauConfig.GetDauCollectionStrategy(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [GetDauCollectionStrategy] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置DAU推送信息(SFTP服务IP、端口、用户名字节数、密码字节数、路径字节数、用户名、密码、路径)
    /// </summary>
    /// <returns></returns>
    public bool SetDauPushParameter()
    {
        try
        {
            // 网关处理时无需进行下发, 最终在测量定义中统一下发
            if (Agent.IsGatewayDevice)
            {
                return true;
            }
            
            DauConfig.SetDauPushParameter(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SetDauPushParameter] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 获取DAU推送信息(SFTP服务IP、端口、用户名字节数、密码字节数、路径字节数、用户名、密码、路径)
    /// </summary>
    /// <returns></returns>
    public bool GetDauPushParameter()
    {
        try
        {
            // 网关处理时无需进行下发, 最终在测量定义中统一下发
            if (Agent.IsGatewayDevice)
            {
                return true;
            }

            Agent.ResultExecution = DauConfig.GetDauPushParameter(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [GetDauPushParameter] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置是否坏标志
    /// </summary>
    /// <returns></returns>
    public bool SetDauEquipmentWarranty()
    {
        try
        {
            DauConfig.SetDauEquipmentWarranty(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 获取是否坏标志
    /// </summary>
    /// <returns></returns>
    public bool GetDauEquipmentWarranty()
    {
        try
        {
            Agent.ResultExecution = DauConfig.GetDauEquipmentWarranty(Agent.DauConfigExtension);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 开始获取录播数据
    /// </summary>
    /// <returns></returns>
    public bool StartGetRecorded()
    {
        try
        {
            Agent.ResultExecution = DauConfig.StartGetRecorded();
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [StartGetRecorded] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 下发测量定义（采集策略、是否坏标记、测量定义）
    /// </summary>
    /// <returns></returns>
    public bool SendMDF(byte[] wtLiveDbByteArray)
    {
        try
        {
            var dauWorkContext = Agent.DauWorkContext;
            var configExtension = Agent.DauConfigExtension;
            if (Agent.IsGatewayDevice)
            {
                // 构建是否坏标记
                var snCode = GetDauSerizeCode(dauWorkContext);
                if (snCode != null)
                {
                    Agent.DauConfigExtension.EquipmentWarranty = BuildEquipmentWarranty(snCode); 
                    // 设置是否坏标记
                    SetDauEquipmentWarranty();
                    // 获取是否坏标记（防止已经设置后状态混乱）
                    GetDauEquipmentWarranty();   
                }
                
                // 下发DAU上下文信息
                
                SetDauWorkContext(wtLiveDbByteArray, 1 * 1024 - 14);
            }
            else
            {
                var windDau = BuildDauContext.GetDau(configExtension.WindTurbineId, configExtension.DauDeviceType.GetHashCode().ToString());
                
                // 重新为DAU对象复制（初始化的时候只有基础信息）
                RecoverBuildDau(dauWorkContext, windDau);
            
                // 重新加载DAU上下文信息
                BuildDauContext.ReloadDauContext(windDau, dauWorkContext);
            
                // 获取测量定义列表
                var measDefList = dauWorkContext.MDFList;
                if (measDefList.Count == 0)
                {
                    return false;
                }
            
                // 构建 DAU 采集策略
                BuildDauCollectionStrategy();
                // 设置采集策略
                DauCollectionStrategyRequest(false);

                // 构建是否坏标记
                var snCode = GetDauSerizeCode(dauWorkContext);
                if (snCode != null)
                {
                    Agent.DauConfigExtension.EquipmentWarranty = BuildEquipmentWarranty(snCode); 
                    // 设置是否坏标记
                    SetDauEquipmentWarranty();
                    // 获取是否坏标记（防止已经设置后状态混乱）
                    GetDauEquipmentWarranty();   
                }
            
                // 下发测量定义
                dauWorkContext.DauFacade.UpgradeDAUDef(measDefList, dauWorkContext.MainCtrlSys, dauWorkContext);

                if (dauWorkContext.Dau != null)
                {
                    DauContextDataProvider.Single.OnDAUUpdateMDF(dauWorkContext.Dau);
                }
                
                // 下发DAU上下文信息
                if (AppConfigUtils.DauSynchronizationContext)
                {
                    SetDauWorkContext(wtLiveDbByteArray, 1010);
                }
            }
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SendMDF] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 重新构建DAU信息
    /// </summary>
    /// <param name="dauWorkContext"></param>
    /// <param name="windDau"></param>
    private static void RecoverBuildDau(DauWorkContext dauWorkContext, WindDAU? windDau)
    {
        if (windDau == null)
        {
            return;
        }

        dauWorkContext.Dau ??= new WindDAU();
        dauWorkContext.Dau.WindTurbineID = windDau.WindTurbineID;
        dauWorkContext.Dau.DauID = windDau.DauID;
        dauWorkContext.Dau.DAUName = windDau.DAUName;
        dauWorkContext.Dau.SerialNumber = windDau.SerialNumber;
        dauWorkContext.Dau.IP = windDau.IP;
        dauWorkContext.Dau.Port = windDau.Port;
        dauWorkContext.Dau.DeviceID = windDau.DeviceID;
        dauWorkContext.Dau.WaveSaveInterval = windDau.WaveSaveInterval;
        dauWorkContext.Dau.TrendSaveInterval = windDau.TrendSaveInterval;
        dauWorkContext.Dau.DataAcquisitionInterval = windDau.DataAcquisitionInterval;
        dauWorkContext.Dau.IsAvailable = windDau.IsAvailable;
        dauWorkContext.Dau.MeasDefVersion = windDau.MeasDefVersion;
        dauWorkContext.Dau.DAUMeasDefVersion = windDau.DAUMeasDefVersion;
        dauWorkContext.Dau.DAUSoftwareVersion = windDau.DAUSoftwareVersion;
        dauWorkContext.Dau.WindParkID = windDau.WindParkID;
        dauWorkContext.Dau.DAUType = windDau.DAUType;
        dauWorkContext.Dau.DAUChannelList = windDau.DAUChannelList;
        dauWorkContext.Dau.RotSpeedChannelList = windDau.RotSpeedChannelList;
        dauWorkContext.Dau.ProcessChannelList = windDau.ProcessChannelList;
        dauWorkContext.Dau.VoltageCurrentList = windDau.VoltageCurrentList;
        dauWorkContext.Dau.UltrasonicChannelList = windDau.UltrasonicChannelList;
        dauWorkContext.Dau.DauOnOffStatus = windDau.DauOnOffStatus;
        dauWorkContext.Dau.ServerAddress = windDau.ServerAddress;
        dauWorkContext.Dau.ServerPort = windDau.ServerPort;
    }

    /// <summary>
    /// 构建 DAU 采集策略
    /// </summary>
    private void BuildDauCollectionStrategy()
    {
        var dauWorkContext = Agent.DauWorkContext;
        var configExtension = Agent.DauConfigExtension;
        
        // 判断 
        var measSolutionList = dauWorkContext.AllEnableMdf.SelectMany(t => t.SolutionList).ToList();
        var windDau = dauWorkContext.Dau;
        
        // 波形采集间隔
        configExtension.CvmWaveformAcquisitionInterval = 0;
        // 特征值采集间隔
        configExtension.CvmEigenvalueAcquisitionInterval = 0;   
        // 波形采集间隔
        configExtension.BvmWaveformAcquisitionInterval = 0;
        // 特征值采集间隔
        configExtension.BvmEigenvalueAcquisitionInterval = 0;   
        // 波形采集间隔
        configExtension.TvmWaveformAcquisitionInterval = 0;
        // 特征值采集间隔
        configExtension.TvmEigenvalueAcquisitionInterval = 0;
        if (measSolutionList is { Count: > 0 })
        {
            var groupBy = measSolutionList.GroupBy(solution => solution.MeasSolutionType);
            foreach (var grouping in groupBy)
            {
                var waveformAcquisitionInterval = (ushort)grouping.ToList().Min(t => t.WaveInterval);
                var eigenvalueAcquisitionInterval = (ushort)grouping.ToList().Min(t => t.EigenInterval);
                if (grouping.Key == "传动链")
                {
                    // 波形采集间隔
                    configExtension.CvmWaveformAcquisitionInterval = waveformAcquisitionInterval;
                    // 特征值采集间隔
                    configExtension.CvmEigenvalueAcquisitionInterval = eigenvalueAcquisitionInterval;      
                } 
                else if (grouping.Key == "叶片")
                {
                    // 波形采集间隔
                    configExtension.BvmWaveformAcquisitionInterval = waveformAcquisitionInterval;
                    // 特征值采集间隔
                    configExtension.BvmEigenvalueAcquisitionInterval = eigenvalueAcquisitionInterval;   
                } 
                else if (grouping.Key == "塔筒")
                {
                    // 波形采集间隔
                    configExtension.TvmWaveformAcquisitionInterval = waveformAcquisitionInterval;
                    // 特征值采集间隔
                    configExtension.TvmEigenvalueAcquisitionInterval = eigenvalueAcquisitionInterval;   
                }
            }
        }
        
        // 采集延时间隔
        int maxAcqTime = 0;
        if (configExtension.CvmEigenvalueAcquisitionInterval != 0)
        {
            maxAcqTime = configExtension.CvmEigenvalueAcquisitionInterval;
        } 
        if (configExtension.BvmEigenvalueAcquisitionInterval != 0 && configExtension.BvmEigenvalueAcquisitionInterval > maxAcqTime)
        {
            maxAcqTime = configExtension.BvmEigenvalueAcquisitionInterval;
        } 
        if (configExtension.TvmEigenvalueAcquisitionInterval != 0 && configExtension.TvmEigenvalueAcquisitionInterval > maxAcqTime)
        {
            maxAcqTime = configExtension.TvmEigenvalueAcquisitionInterval;
        }
        configExtension.AcquisitionDelayInterval = (ushort)new Random().Next(0, maxAcqTime + 1);
        
        // 设置时间触发天数
        foreach (var measDefinition in dauWorkContext.MDFList)
        {
            var triggerRules = measDefinition.TriggerRules;
            if (triggerRules != null)
            {
                foreach (var triggerRuleDef in triggerRules)
                {
                    var measTriggerTime = triggerRuleDef.TriggerTime;
                    if (measTriggerTime != null)
                    {
                        configExtension.TriggerCollectionDays = (byte)(measTriggerTime.TimeInterval / 60 / 24);
                    }
                    else
                    {
                        configExtension.TriggerCollectionDays = 3;
                    }
                }
            }
        }
        
        
        var dauExtendConfigs = DauManagement.GetDauExtendConfig(windDau);
        // 录播天数
        var recordedDays =
            dauExtendConfigs.FirstOrDefault(cfg =>
                cfg.Key == EnumDauExtendConfig.RecordedDays.GetHashCode().ToString());
        configExtension.RecordedDays = recordedDays != null ? ushort.Parse(recordedDays.Value) : (ushort)2;
        // 是否开启录播功能
        var enableRecordedDays = dauExtendConfigs.FirstOrDefault(cfg =>
            cfg.Key == EnumDauExtendConfig.EnableRecordedDays.GetHashCode().ToString());
        configExtension.EnableRecordedDays = enableRecordedDays != null && bool.Parse(enableRecordedDays.Value);
        
        // 对时服务IP
        var timeSynchronizationServiceIp = dauExtendConfigs.FirstOrDefault(cfg =>
            cfg.Key == EnumDauExtendConfig.TimeSynchronizationServiceIp.GetHashCode().ToString());
        configExtension.TimeSynchronizationServiceIp =
            timeSynchronizationServiceIp != null ? timeSynchronizationServiceIp.Value : "0.0.0.0";
        // 对时服务端口
        var timeSynchronizationServicePort = dauExtendConfigs.FirstOrDefault(cfg =>
            cfg.Key == EnumDauExtendConfig.TimeSynchronizationServicePort.GetHashCode().ToString());
        configExtension.TimeSynchronizationServicePort = (ushort?)(timeSynchronizationServicePort != null ? ushort.Parse(timeSynchronizationServicePort.Value) : 0);
    }

    /// <summary>
    /// 构建是否坏标志
    /// </summary>
    /// <returns></returns>
    private bool BuildEquipmentWarranty(string snCode)
    {
        return DeviceStateUtils.IsDeviceAbnormal(snCode);
    }
    
    /// <summary>
    /// 获取 DAU SN
    /// </summary>
    /// <param name="ctx"></param>
    private string? GetDauSerizeCode(DauWorkContext ctx)
    {
        try
        {
            // 设置DAU SN
            if (ctx.DauFacade is IDAUConfig dauFacade)
            {
                return dauFacade.GetDAUSerizeCode();
            }
        }
        catch (Exception)
        {
            // Ignore
        }
        return null;
    }

    /// <summary>
    /// 设置上下文信息
    /// </summary>
    /// <returns></returns>
    public bool SetDauContext(byte[] wtLiveDbByteArray, int dauContextConfigRequestArrayLength = 1010)
    {
        try
        {
            var config = Agent.DauConfigExtension;

            // DauDeviceType 即为 DauID
            var windDau = BuildDauContext.GetDau(config.WindTurbineId, config.DauDeviceType.GetHashCode().ToString());
            BuildDauContext.ReloadDauContext(windDau, Agent.DauWorkContext);
            SetDauWorkContext(wtLiveDbByteArray, dauContextConfigRequestArrayLength);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SendMDF] error.", e);
        }
        return Agent.ResultExecution;
    }

    /// <summary>
    /// 设置DAU上下文信息(DAU 长度只能设置为1010, DAU缓冲区只有1k)
    /// </summary>
    private void SetDauWorkContext(byte[] wtLiveDbByteArray, int dauContextConfigRequestArrayLength = 1010)
    {
        // 设置DAU上下文信息
        DauConfig.SetDauContext(wtLiveDbByteArray, dauContextConfigRequestArrayLength);
    }

    /// <summary>
    /// 获取上下文信息
    /// </summary>
    /// <returns></returns>
    public byte[] GetDauContext()
    {
        try
        {
            var agentCancellationToken = Agent.CancellationToken;
            if (agentCancellationToken == null)
            {
                return Array.Empty<byte>();
            }
            return DauConfig.GetDauContext(agentCancellationToken.Value);
        }
        catch (Exception e)
        {
            Agent.ResultExecution = false;
            Logger.LogErrorMessage("[ListenerWriteHandler] [SendMDF] error.", e);
            return null;
        }
    }
}