﻿using AppFramework.Utility;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Asn1.Ocsp;

namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// DAU单元相关内容
    /// </summary>
    public class DAUManager
    {
        #region DAU实时状态
        /// <summary>
        /// DAU实时状态
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUStatusByID(string id)
        {
            BaseTableModel tableModel = new BaseTableModel();
            WindDAU dau = DauManagement.GetDAUById(id);
            if (dau != null)
            {
                RTAlarmStatus_DAU data = GetDAURTAlarmStatusByWindTurbineId(id);
                if(data!=null)
                    tableModel = CreateTableByDAUData(data, dau, id);
            }
            return tableModel;
        }

        /// <summary>
        /// 根据机组ID获取DAU实时状态列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<Cell> GetRAUStatusListByID(string id)
        {
            List<Cell> daulist = new List<Cell>();
            List<RTAlarmStatus_DAU> dauRtList = GetDAURTAlarmStatusListByWindTurbineId(id);
            dauRtList.ForEach(item =>
            {
                string bgColor = null;
                switch (item.AlarmState)
                {
                    case EnumDAUStatus.Unknown:
                        bgColor = "#CCCCCC";
                        break;
                    case EnumDAUStatus.Normal:
                        bgColor = "#00FF00";
                        break;
                    default:
                        bgColor = "#FF0000";
                        break;
                }
                Cell cell = new Cell()
                {
                    title = item.DauID,
                    color = bgColor,
                    displayValue = AppFramework.Utility.EnumHelper.GetDescription(item.AlarmState),
                    type = item.StatusUpdateTime.ToString(),
                };
                daulist.Add(cell);
            });
            return daulist;
        }

        /// <summary>
        /// 根据机组ID取得机组对应采集单元实时状态列表
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static RTAlarmStatus_DAU GetDAURTAlarmStatusByWindTurbineId(string _windTurbineId)
        {
            RTAlarmStatus_DAU alarmDau = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                alarmDau = ctx.AlarmStatusRTDAUs.FirstOrDefault(obj => obj.WindTurbineID == _windTurbineId);
                if (alarmDau != null)
                    alarmDau.sensorRTList = ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _windTurbineId).ToList();
            }
            return alarmDau;
        }

        /// <summary>
        /// 获取DAU状态列表，可获取多台dau状态
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static List<RTAlarmStatus_DAU> GetDAURTAlarmStatusListByWindTurbineId(string _windTurbineId)
        {
            List<RTAlarmStatus_DAU> list = new List<RTAlarmStatus_DAU>();
            using(CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmStatusRTDAUs.Where(p => p.WindTurbineID == _windTurbineId).ToList();
                if(list.Count != 0)
                {
                    list.ForEach(item =>
                    {
                        item.sensorRTList = ctx.AlarmStatusRTSensors.Where(p => p.WindTurbineID == item.WindTurbineID && item.DauID == p.DauID).ToList();
                    });
                }
            }
            return list;
        }

        public BaseTableModel CreateTableByDAUData(RTAlarmStatus_DAU _data,WindDAU dau,string windTurbineId)
        {
            //增加测量位置排序
            List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(windTurbineId);
            List<RTAlarmStatus_Channel> list = new List<RTAlarmStatus_Channel>();
            List<DAUChannelV2> DAUChannelList = dau.DAUChannelList;
            foreach (var data in vibLoc.OrderBy(item => item.OrderSeq))
            {
                var channelVib = DAUChannelList.Find(item => item.MeasLocVibID == data.MeasLocationID);
                if (channelVib == null)
                    continue;
                RTAlarmStatus_Channel channel = _data.sensorRTList.Find(item => item.ChannelNumber == channelVib.ChannelNumber);
                if (channel != null)
                {
                    list.Add(channel);
                }
            }
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "DAUStatusTable";
            List<Rows> rows = new List<Rows>();
            foreach (RTAlarmStatus_Channel data in list)
            {
                var channelVib = DAUChannelList.Find(item => item.ChannelNumber == data.ChannelNumber);
                if (channelVib == null)
                    continue;
                MeasLoc_Vib locVib = vibLoc.Find(item => item.MeasLocationID == channelVib.MeasLocVibID);
                if (locVib == null)
                    continue;
                Rows cells = new Rows();
                cells.cells = CreateDAUStatusTableCell(data, locVib.MeasLocName,locVib.MeasLocationID);
                rows.Add(cells);
            }
            if (_data != null && _data.sensorRTList != null && _data.sensorRTList.Count > 0)
            {
                GetRPList(windTurbineId, rows,dau);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        private void GetRPList(string windTurbineId, List<Rows> rows, WindDAU dau)
        {
            List<RTAlarmStatus_RSChannel> list = DauManagement.GetAlarmStatusRTRSSensorList(windTurbineId);


            if(dau != null)
            {
                list = list.Where(item => item.DauID == dau.DauID).ToList();
            }
            foreach (RTAlarmStatus_RSChannel data in list)
            {
                Rows cellRDs = new Rows();
                cellRDs.cells = CreateRSDAUStatusTableCell(data);
                if (cellRDs.cells != null)
                    rows.Add(cellRDs);
            }
        }

        /// <summary>
        /// 把转速传感器直流分量，追加到最后
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        private Cell[] CreateRSDAUStatusTableCell(RTAlarmStatus_RSChannel _data)
        {
            if (_data==null) return null;
            //通道
            List<Cell> cells = new List<Cell>();
            Cell cell01 = new Cell();
            cell01.displayValue = _data.ChannelNumber.ToString();
            cells.Add(cell01);
            //测量位置
            Cell cell02 = new Cell();
            cell02.displayValue = "发电机转速";
            cells.Add(cell02);
            //类型
            //Cell cell7 = new Cell();
            //cell7.displayValue = "转速";
            //cells.Add(cell7);
            //直流分量
            Cell cell3 = new Cell();
            cell3.displayValue = "";// _data.DCDataValue.ToString("f3");
            cells.Add(cell3);
            //状态
            Cell cell4 = new Cell();
            cell4.displayValue = EnumHelper.GetDescription(_data.AlarmState);
            cell4.color = Utility.GetColorFromAlarmType(_data.AlarmState);
            cells.Add(cell4);
            //更新时间
            Cell cell5 = new Cell();
            cell5.displayValue = _data.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
            cells.Add(cell5);
            //趋势图
            Cell cell6 = new Cell();
            cell6.displayValue = "";
            cells.Add(cell6);
            return cells.ToArray();
        }

        private Cell[] CreateDAUStatusTableCell(RTAlarmStatus_Channel _data,string measLocName,string measLocCode)
        {
            List<Cell> cells = new List<Cell>();
            //通道
            Cell cell01 = new Cell();
            cell01.displayValue = _data.ChannelNumber.ToString();
            cell01.title = _data.ChannelNumber.ToString();
            cells.Add(cell01);
            //测量位置
            Cell cell02 = new Cell();
            cell02.displayValue = measLocName;
            cell02.title = measLocName;
            cells.Add(cell02);
            //类型
            //Cell cell7 = new Cell();
            //double Coeff_a = 0;
            //DAUChannelV2 channel = DauManagement.GetDAUVibChannelList(_data.WindTurbineID).Find(i => i.ChannelNumber == _data.ChannelNumber);
            ////cell7.displayValue = channel == null ? "未知" : double.TryParse(channel.Coeff_a, out Coeff_a) ? string.Format("加速度({0}mv/g)", GetCoeffName(Coeff_a*10)) : "未知";
            //cell7.displayValue = channel == null ? "未知" : double.TryParse(channel.Coeff_a, out Coeff_a) ? string.Format("加速度") : "未知";
            //cells.Add(cell7);
            //直流分量
            Cell cell3 = new Cell();
            cell3.displayValue = _data.DCDataValue.ToString("f3");
            cell3.title = _data.DCDataValue.ToString("f3");
            cells.Add(cell3);
            //状态
            Cell cell4 = new Cell();
            //modified by sq 20191018
            //法兰螺栓和发电机转子电流电压状态都为正常
            if(measLocCode.Contains("ENROTCUR") || measLocCode.Contains("FLANUL") || measLocCode.Contains("ENROTVOT"))
            {
                cell4.displayValue = "正常";
                cell4.color = "";
                cell4.title = "正常";
            }
            else
            {
                try
                {
                    cell4.displayValue = EnumHelper.GetDescription(_data.AlarmState);
                    cell4.color = Utility.GetColorFromAlarmType(_data.AlarmState);
                    cell4.title = EnumHelper.GetDescription(_data.AlarmState);
                }
                catch (Exception ex)
                {
                    cell4.displayValue = "未知";
                    cell4.color = "";
                    cell4.title = "未知";
                }
            }
            cells.Add(cell4);
            //更新时间
            Cell cell5 = new Cell();
            cell5.displayValue = _data.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
            cell5.title = _data.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
            cells.Add(cell5);
            //趋势图
            Cell cell6 = new Cell();
            cell6.displayValue = "偏置电压趋势图";
            cell6.type = "link";
            cell6.href = "javascript:openTrendChart(" + cell01.displayValue + "," + "'" + _data.WindTurbineID + "'" + "," + "'" + _data.DauID + "'" + ");";
            cells.Add(cell6);
            return cells.ToArray();
        }

        private int GetCoeffName(double Coeff_a)
        {
            //Coeff_a 单位是   mv/s^2
            // return 的单位是 mv/g
            if (Coeff_a > 0 && Coeff_a <= 120)
            {
                return 100;
            }
            else if (Coeff_a > 120 && Coeff_a <= 300)
            {
                return 250;
            }
            else if (Coeff_a > 300 && Coeff_a <= 600)
            {
                return 500;
            }
            else if (Coeff_a > 600 && Coeff_a <= 1200)
            {
                return 1000;
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region DAU运行日志

        public string GetNewDAULog(string turbineID)
        {
            List<DAURunLog> logList = DauManagement.GetDAULog(turbineID, 1);
            string log = string.Empty;
            if (logList.Count > 0)
            {
                log = "状态描述 :  " + logList[0].LogTitle;
            }
            else
            {
                log = "无最新数据";
            }
            return log;
        }

        public string GetDauRTStatus(string turbineID)
        {
            RTAlarmStatus_DAU data = GetDAURTAlarmStatusByWindTurbineId(turbineID);
            if (data == null)
            {
                return string.Empty;
            }
            return data.AlarmState.ToString();
        }

        /// <summary>
        /// 取得机组运行日志
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="count"> 显示数 </param>
        /// <returns></returns>
        public BaseTableModel GetDAULog(string _turbineId, int count)
        {
            List<DAURunLog> logList = DauManagement.GetDAULog(_turbineId, count);
            BaseTableModel tableModel = CreateDAULogTable(logList,_turbineId);

            return tableModel;
        }
        /// <summary>
        /// 根据时间范围选择日志
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUlogByTime(string _turbineId, DateTime beginTime, DateTime endTime)
        {
            List<DAURunLog> logList = DauManagement.GetDAULogByTime(_turbineId, beginTime, endTime);
            DAURunLog dAURunLog = new DAURunLog();
            List<DAURunLog> _logList = new List<DAURunLog>();
            foreach (DAURunLog item in logList)
            {
           
                dAURunLog.DauId = item.DauId;
       
                 _logList = logList.FindAll(t => t.DauId == dAURunLog.DauId);
                break;
            }
            BaseTableModel tableModel = CreateDAULogTable(_logList, _turbineId);

            return tableModel;
        }
        /// <summary>
        /// 根据时间范围选择日志
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUlogByTime(string _turbineId, string dauID, DateTime beginTime, DateTime endTime)
        {
            List<DAURunLog> logList = DauManagement.GetDAULogByTime(_turbineId, dauID, beginTime, endTime);

            BaseTableModel tableModel = CreateDAULogTable(logList, _turbineId);

            return tableModel;
        }

        private BaseTableModel CreateDAULogTable(List<DAURunLog> logList,string turid)
        {
            // 根据机组id，获取dau列表
            //var daulist =  DAUSManageModel.GetDAUListById(turid);

            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "DAULog";
            if (logList.Count == 0)
            {
                tableModel.rows = tableModel.addEmptyRow(3);
                return tableModel;
            }
            logList = logList.OrderByDescending(item => item.EventTime).ToList();
            List<Rows> rows = new List<Rows>();
            int logCount = 0;
            if (logList.Count > 1000)
            {
                logCount = 1000;
            }
            else
            {
                logCount = logList.Count;
            }
            for (int i = 0; i < logCount; i++)
            {
                Rows row = new Rows();

                row.cells = CreateDAULogCell(logList[i]);
                rows.Add(row);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        private Cell[] CreateDAULogCell(DAURunLog log)
        {
            List<Cell> cells = new List<Cell>();
            //DAU状态
            Cell cell01 = new Cell();
            cell01.displayValue = EnumHelper.GetDescription((EnumDAUStatus)log.AlarmState);
            //采集单元不记录正常，故不显示颜色好些
            //cell01.color = Utility.GetColorFromAlarmType((EnumDAUStatus)log.AlarmState);
            cells.Add(cell01);
            //备注
            Cell cell2 = new Cell();
            cell2.displayValue = log.LogTitle;
            cells.Add(cell2);
            
            //更新时间
            Cell cell3 = new Cell();
            cell3.displayValue = log.EventTime.ToString("yyyy-MM-dd HH:mm:ss");//需要转换函数
            cells.Add(cell3);
            //Cell cell4 = new Cell();
            //cell4.displayValue = log.EventTime == null ? "0" : log.EventTime.ToString().Replace("/", "").Replace(" ", "").Replace(":", "");//需要转换函数
            //cells.Add(cell4);
            return cells.ToArray();
        }

        #endregion

        #region DAU直流分量
        public AnalysisData GetSensorDCChartByChannel(string turbineID, int channel, DateTime _beginTime, DateTime _endTime,string DAUID)
        {
            //IReadMeasDataLogic readProvider = StorageFactoryLogic.GetReadDataLogic();
            List<SensorDCData> dcList = new List<SensorDCData>(); // readProvider.GetSensorDCData(turbineID, channel, _beginTime, _endTime);
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                dcList = ctx.SensorDCDatas.Where(p =>
                        p.WindTurbineID == turbineID && p.ChannelNumber == channel &&
                        p.DCAcquisitionTime >= _beginTime && p.DCAcquisitionTime <= _endTime && p.DAUId == DAUID).ToList();

            }
            //振动通道
            //List<DAUChannelV2> channelList = DauManagement.GetDAUVibChannelList(turbineID);
            List<DAUChannelV2> channelList = DAUSManageModel.GetDAUVibChannelList(turbineID, DAUID);
            List<MeasLoc_Vib> vibList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            DAUChannelV2 dauChannel = channelList.Find(item => item.ChannelNumber == channel);
            AnalysisData analysisData = new AnalysisData();
            List<string> timeValue = new List<string>();//采集时间
            List<double> eigenValue = new List<double>();//直流分量值
            List<double> waringValue = new List<double>();//最低配置电压
            List<double> errorValue = new List<double>();//最高配置电压
            for (int i = 0; i < dcList.Count; i++)
            {
                timeValue.Add(dcList[i].DCAcquisitionTime.ToString("yyyy-MM-dd HH:mm:ss"));
                eigenValue.Add(Math.Round(dcList[i].DCDataValue, 1));
                waringValue.Add(Math.Round(dauChannel.MinBiasVolt, 1));
                errorValue.Add(Math.Round(dauChannel.MaxBiasVolt, 1));
            }
            analysisData.id = channel.ToString();
            analysisData.titleName = vibList.Find(item => item.MeasLocationID == dauChannel.MeasLocVibID).MeasLocName;
            analysisData.subText = channel.ToString();
            analysisData.eigenValueData = eigenValue.ToArray();
            analysisData.waringValueData = waringValue.ToArray();
            analysisData.errorValueData = errorValue.ToArray();
            analysisData.timeValueData = timeValue.ToArray();
            return analysisData;
        }
        #endregion DAU直流分量

        #region  采集单元操作
        /// <summary>
        /// 获取采集单元列表
        /// </summary>
        /// <param name="edit"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUList(bool edit,string windParkId)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindDAU> daulists = DauManagement.GetDAUListByWindParkID(windParkId);
            List<WindDAU> daulist = new List<WindDAU>();
            //对DAUlist 排序。
            if (daulists!= null)
            {
                daulist = daulists.OrderBy(p => p.WindTurbineID).ToList();
            }
            tableModel.tableName = "DAUInfoTable";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < daulist.Count; i++)
            {
                Rows cells = new Rows();
                cells.cells = CreateDAUManagerListTableCell(daulist[i], edit);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        /// <summary>
        /// 创建采集单元Table
        /// </summary>
        /// <param name="dauManager"></param>
        /// <param name="edit"></param>
        /// <returns></returns>
        private Cell[] CreateDAUManagerListTableCell(WindDAU dauManager, bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //机组ID
            Cell cell01 = new Cell();
            cell01.displayValue = dauManager.WindTurbineID;
            //采集单元ID
            Cell cell10 = new Cell();
            cell10.displayValue = dauManager.DauID;
            //机组名称
            Cell cell02 = new Cell();
            //cell02.displayValue = dauManager.DAUName;
            string windTurbineName = null;
            using(CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windTurbineName = ctx.DevWindTurbines.FirstOrDefault(p => p.WindTurbineID == dauManager.WindTurbineID).WindTurbineName;
            }
            cell02.displayValue = windTurbineName;

            //设备类型
            Cell cell77 = new Cell();
            //cell77.displayValue = GetDauTypeString(dauManager.DAUType);
            cell77.displayValue = EnumHelper.GetDescription(dauManager.DAUType);

            //DAU名称
            Cell cell09 = new Cell();
            cell09.displayValue = dauManager.DAUName;
            //IP地址
            Cell cell03 = new Cell();
            cell03.displayValue = dauManager.IP;
            //采集间隔(分钟)
            Cell cell04 = new Cell();
            cell04.displayValue = dauManager.DataAcquisitionInterval.ToString();

            // 端口和设备ID
            Cell cell21 = new Cell();
            cell21.displayValue = dauManager.Port.ToString();

            Cell cell22 = new Cell();
            cell22.displayValue = dauManager.DeviceID.ToString();

            //状态
            Cell cell06 = new Cell();
            string isable = dauManager.IsAvailable == true ? "启用" : "禁用";
            cell06.type = "span";
            cell06.title = "nowStatus";
            cell06.displayValue = dauManager.IsAvailable == true ? "启用" : "禁用";
            //编辑
            Cell cell07 = new Cell();
            cell07.type = "btn";
            cell07.displayValue = "编辑";
            cell07.onclick = "editDAU('" + dauManager.WindTurbineID + "','" + dauManager.DauID + "','" + windTurbineName + "','" + dauManager.DAUName + "','" + dauManager.IP + "','" + dauManager.DataAcquisitionInterval + "','" + isable + "','" +dauManager.Port + "','" +dauManager.DeviceID + "','" + (int)dauManager.DAUType + "')";
            cell07.style = "btn btn-primary btn-sm btnEdit";
            //删除
            Cell cell08 = new Cell();
            cell08.type = "btn";
            cell08.displayValue = "删除";
            cell08.onclick = "deleteDAU('" + dauManager.WindTurbineID + "','" + dauManager.DauID + "',this)";
            cell08.style = "btn btn-danger btn-sm btnDelete";
            ////名称
            //Cell cell09 = new Cell();
            //cell09.displayValue = dauManager.Name;
            cellList.Add(cell01);
            cellList.Add(cell10);
            cellList.Add(cell02);
            cellList.Add(cell09);
            cellList.Add(cell77);
            cellList.Add(cell03);
            cellList.Add(cell04);

            cellList.Add(cell21);
            cellList.Add(cell22);

            cellList.Add(cell06);
            cellList.Add(cell07);
            cellList.Add(cell08);
            
            //cellList.Add(cell09);
            return cellList.ToArray();
        }

        //private string GetDauTypeString(EnumDAUType type)
        //{
        //    string dautype = "振动监测";
        //    switch (type)
        //    {
        //        case EnumDAUType.Vibration:
        //            dautype = "振动监测";
        //            break;
        //        case EnumDAUType.Ultrasonic:
        //            dautype = "超声波监测";
        //            break;
        //        case EnumDAUType.FiberStrain:
        //            dautype = "光纤应变";
        //            break;
        //        case EnumDAUType.DauStrain:
        //            dautype = "WindDAU应变";
        //            break;
        //        case EnumDAUType.Clearance:
        //            dautype = "间隙监测";
        //            break;
        //        case EnumDAUType.Angle:
        //            dautype = "转角监测";
        //            break;
        //    }
        //    return dautype;
        //}

        /// <summary>
        /// 获取采集单元状态列表
        /// </summary>
        /// <param name="edit"></param>
        /// <returns></returns>
        public BaseTableModel GetDAUList(string windParkId, string roleData)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindDAU> daulist = DauManagement.GetDAUListByWindParkID(windParkId);
            List<WindTurbine> turbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkId).OrderBy(item => item.WindTurbineName).ToList();
            tableModel.tableName = "DAUInfoTable";
            List<Rows> rows = new List<Rows>();
            turbineList.ForEach(item => {
                //WindDAU dau = daulist.Find(winddua => winddua.WindTurbineID == item.WindTurbineID);
                //if (dau != null)
                //{
                //    Rows cells = new Rows();
                //    cells.cells = CreateDAUManagerListTableCell(dau, windParkId);
                //    rows.Add(cells);
                //}

                //支持多采集单元，modify by sq 20190221
                List<WindDAU> myDauList = daulist.FindAll(p => p.WindTurbineID == item.WindTurbineID);
                
                myDauList.ForEach(myitem =>
                {
                    Rows cells = new Rows();
                    cells.cells = CreateDAUManagerListTableCell(myitem, windParkId, roleData);
                    rows.Add(cells);
                });
            });
            //for (int i = 0; i < daulist.Count; i++)
            //{
            //    Rows cells = new Rows();
            //    cells.cells = CreateDAUManagerListTableCell(daulist[i], windParkId);
            //    rows.Add(cells);
            //}
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        /// <summary>
        /// 创建采集单元状态Table
        /// </summary>
        /// <param name="dauManager"></param>
        /// <param name="edit"></param>
        /// <returns></returns>
        private Cell[] CreateDAUManagerListTableCell(WindDAU dauManager, string windParkId, string roleData)
        {
            List<Cell> cellList = new List<Cell>();
            cellList.Add(new Cell() { type = "hide", displayValue = "0" });
            //机组名称。
            Cell cell01 = new Cell();
            string windTurbineName = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windTurbineName = ctx.DevWindTurbines.FirstOrDefault(p => p.WindTurbineID == dauManager.WindTurbineID).WindTurbineName;
            }
            cell01.displayValue = windTurbineName;
            cellList.Add(cell01);
            //机组名称=》dau名称
            Cell cell02 = new Cell();
            cell02.displayValue =  dauManager.DAUName;
            cell02.type = "link";
            cell02.href = string.Format("/WindTurbine/ShowDAUDetail/{0}/{1}", windParkId, dauManager.WindTurbineID);
            cellList.Add(cell02);
            //IP地址
            Cell cell03 = new Cell();
            cell03.displayValue = dauManager.IP;
            cellList.Add(cell03);
            //采集间隔(分钟)
            Cell cell04 = new Cell();
            cell04.displayValue = dauManager.DataAcquisitionInterval.ToString();
            cellList.Add(cell04);
            //DAU运行状态
            //if (roleData == "ADMIN_SUPER") { 
            if (roleData.StartsWith("ADMIN_"))
            {
                Cell cell06 = new Cell();
            //cell06.displayValue = dauManager.IsAvailable == true ? "启用" : "禁用";
            //RTAlarmStatus_DAU data = GetDAURTAlarmStatusByWindTurbineId(dauManager.WindTurbineID);
            RTAlarmStatus_DAU data = DAUSManageModel.GetDAURTAlarmStatusByWindTurbineIdAndDAUID(dauManager.WindTurbineID, dauManager.DauID);
            if (data == null)
            {
                cell06.displayValue = "";
            }
            else {
                cell06.displayValue = AppFramework.Utility.EnumHelper.GetDescription(data.AlarmState);
                cell06.color = Utility.GetColorFromAlarmType(data.AlarmState);
            }
                cellList.Add(cell06);
            }
            //更新时间
            Cell cell07 = new Cell();
            //RTAlarmStatus_DAU DauState = DauManagement.GetDAUAlarmStatus(dauManager.WindTurbineID);
            RTAlarmStatus_DAU DauState = DAUSManageModel.GetDAURTAlarmStatusByWindTurbineIdAndDAUID(dauManager.WindTurbineID, dauManager.DauID);
            if (DauState == null)
            {
                cell07.displayValue = "--";
            }
            else {
                cell07.displayValue = DauState.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            cellList.Add(cell07);
            return cellList.ToArray();
        }

        public List<WindTurbine> GetTurbineDropDown(string windParkID)
        {
            List<WindTurbine> list = new List<WindTurbine>();
            //List<WindTurbine> wind = WTCMSLive.BusinessModel.DevTreeManagement.GetNotUsedDAUTurListByParkId(windParkID);

            //支持多测量定义，modified by sq 20190221.
            List<WindTurbine> wind = WTCMSLive.BusinessModel.DevTreeManagement.GetTurbinesListByWindParkId(windParkID);

            foreach (WindTurbine w in wind)
            {
                WindTurbine winds = new WindTurbine();
                winds.WindTurbineID = w.WindTurbineID;
                winds.WindTurbineName = w.WindTurbineName;
                list.Add(winds);
            }
            return list;
        }


        public static bool UpdateMeasDefVersion(string TurbineID)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                List<WindDAU> DAUList = ctx.DAUnits.Where(obj => obj.WindTurbineID == TurbineID).ToList();
                for(int i = 0; i < DAUList.Count; i++)
                {
                    if (DAUList[i] != null)
                    {
                        DAUList[i].MeasDefVersion += 1;
                        ctx.Entry(DAUList[i]).State = EntityState.Modified;
                        count += ctx.SaveChanges();
                    }
                }
            }
            return count > 0;
        }
        #endregion

        #region 振动通道列表
        public BaseTableModel GetChannelList(string turbineID, bool edit)
        {
            // 获取机组DAU实体
            WindDAU dataSourceV2 = DauManagement.GetDAUById(turbineID);
            List<MeasLoc_Vib> LocList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "vibchannelList";
            List<Rows> rowsList = new List<Rows>();
            if (dataSourceV2 != null)
            {
                for (int i = 0; i < dataSourceV2.DAUChannelList.Count; i++)
                {
                    DAUChannelV2 channelVib = dataSourceV2.DAUChannelList[i];
                    string measLocName =  LocList.Find(item=>item.MeasLocationID==channelVib.MeasLocVibID).MeasLocName;
                    Rows row = new Rows();
                    row.cells = GetChannelListTableCell(channelVib, measLocName, edit);
                    rowsList.Add(row);
                }
                tableModel.rows = rowsList.ToArray();
            }
            else
            {
                Rows rows = new Rows();
                List<Cell> cells = new List<Cell>();
                for (int i = 0; i < 7; i++)
                {
                    Cell cell = new Cell();
                    if (i == 0)
                    {
                        cell.displayValue = "列表无数据";
                    }
                    else
                    {
                        cell.displayValue = "";
                    }
                    cells.Add(cell);
                    cells.ToArray();
                }
                rows.cells = cells.ToArray();
                rowsList.Add(rows);
                tableModel.rows = rowsList.ToArray();
            }
            return tableModel;
        }
        private Cell[] GetChannelListTableCell(DAUChannelV2 channelList ,string measLocName,bool edit)
        {
            List<Cell> cells = new List<Cell>();
            //通道编号
            Cell cell0 = new Cell();
            cell0.displayValue = channelList.ChannelNumber.ToString();
            //测量位置
            Cell cell1 = new Cell();
            cell1.displayValue = measLocName;
            //灵敏度系数[mv/(m/s^2)] 
            Cell cell2 = new Cell();
            cell2.displayValue = channelList.Coeff_a.ToString("F2");//灵敏系数没设置成
            //最低偏置电压(V)    
            Cell cell3 = new Cell();
            cell3.displayValue = channelList.MinBiasVolt.ToString();
            //最高偏置电压(V)   
            Cell cell4 = new Cell();
            cell4.displayValue = channelList.MaxBiasVolt.ToString();
            //编辑
            Cell cell5 = new Cell();
            if (edit)
            {
                cell5.type = "btn";
                cell5.displayValue = "编辑";
                cell5.onclick = "editDAUVibChanne('" + channelList.ChannelNumber + "')";
                cell5.style = "btn btn-primary btn-sm";
            }
            else { cell5.displayValue = "-"; }
            //删除
            Cell cell6 = new Cell();
            if (edit)
            {
                cell6.type = "btn";
                cell6.displayValue = " 删除";
                cell6.onclick = "deleteDAUVibChanne('" + channelList.ChannelNumber + "')";
                cell6.style = "btn btn-danger btn-sm";
            }
            else { cell6.displayValue = "-"; }
            cells.Add(cell0);
            cells.Add(cell1);
            cells.Add(cell2);
            cells.Add(cell3);
            cells.Add(cell4);
            cells.Add(cell5);
            cells.Add(cell6);
            return cells.ToArray();
        }
        #endregion

        #region 转速通道列表
        public BaseTableModel DAURotSpdChannel(string turbineID)
        {
            WindDAU dataSourceV2 = DauManagement.GetDAUById(turbineID);
            List<MeasLoc_RotSpd> rotSpdList = DevTreeManagement.GetRotSpdMeasLocListByTurId(turbineID);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "spdchannelList";
            List<Rows> rowsList = new List<Rows>();
            if (dataSourceV2 != null)
            {
                for (int i = 0; i < dataSourceV2.RotSpeedChannelList.Count; i++)
                {
                    Rows row = new Rows();
                    var rotChannle = dataSourceV2.RotSpeedChannelList[i];
                    MeasLoc_RotSpd rotspd = rotSpdList.Find(item => item.MeasLocationID == rotChannle.MeasLocRotSpdID);
                    row.cells = GetDAURotSpdChannelTableCell(rotChannle, rotspd);
                    rowsList.Add(row);
                }
                tableModel.rows = rowsList.ToArray();
            }
            else {
                Rows rows = new Rows();
                List<Cell> cells = new List<Cell>();
                for (int i = 0; i < 5; i++)
                {
                    Cell cell = new Cell();
                    if (i == 0)
                    {
                        cell.displayValue = "列表无数据";
                    }
                    else
                    {
                        cell.displayValue = "";
                    }
                    cells.Add(cell);
                    cells.ToArray();
                }
                rows.cells = cells.ToArray();
                rowsList.Add(rows);
                tableModel.rows = rowsList.ToArray();
            }
            return tableModel;
        }
        private Cell[] GetDAURotSpdChannelTableCell(DAUChannel_RotSpeed channelList, MeasLoc_RotSpd rotspd)
        {
            List<Cell> cells = new List<Cell>();
            //通道编号
            Cell cell0 = new Cell();
            cell0.displayValue = channelList.ChannelNumber.ToString();
            //测量位置
            Cell cell1 = new Cell();
            cell1.displayValue = rotspd.MeasLocName;
            //编码器线数
            Cell cell2 = new Cell();
            cell2.displayValue = rotspd.LineCounts.ToString();
            //编辑
            Cell cell4 = new Cell();
            //if (edit)
            //{
            //    cell4.type = "btn";
            //    cell4.displayValue = "编辑";
            //    cell4.onclick = "editSVM('" + channelList.ChannelNumber + "')";
            //    cell4.style = "btn btn-default";
            //}
            //else { cell5.displayValue = "-"; }
            cells.Add(cell0);
            cells.Add(cell1);
            cells.Add(cell2);
            cells.Add(cell4);

            return cells.ToArray();
        }
        #endregion

        #region 工况通道列表
        public BaseTableModel WorkConditionChannel(string turbineID)
        {
            WindDAU dataSourceV2 = DauManagement.GetDAUById(turbineID);
            List<MeasLoc_Process> processList = DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "workConditionList";
            List<Rows> rowsList = new List<Rows>();
            if (dataSourceV2 != null)
            {
                for (int i = 0; i < dataSourceV2.ProcessChannelList.Count; i++)
                {
                    Rows row = new Rows();
                    var channel = dataSourceV2.ProcessChannelList[i];
                    MeasLoc_Process process = processList.Find(itme => itme.MeasLocationID == channel.MeasLoc_ProcessId);
                    row.cells = GetWorkConditionChannelTableCell(channel, process);
                    rowsList.Add(row);
                }
                tableModel.rows = rowsList.ToArray();
            }
            else
            {
                Rows rows = new Rows();
                List<Cell> cells = new List<Cell>();
                for (int i = 0; i < 5; i++)
                {
                    Cell cell = new Cell();
                    if (i == 0)
                    {
                        cell.displayValue = "列表无数据";
                    }
                    else
                    {
                        cell.displayValue = "";
                    }
                    cells.Add(cell);
                    cells.ToArray();
                }
                rows.cells = cells.ToArray();
                rowsList.Add(rows);
                tableModel.rows = rowsList.ToArray();
            }
            return tableModel;
        }
        private Cell[] GetWorkConditionChannelTableCell(DAUChannel_Process channelList, MeasLoc_Process process)
        {
            List<Cell> cells = new List<Cell>();
            //通道编号
            Cell cell0 = new Cell();
            cell0.displayValue = channelList.ChannelNumber.ToString();
            //测量位置
            Cell cell1 = new Cell();
            cell1.displayValue = process.MeasLocName;
            //信号带宽（Hz）
            Cell cell2 = new Cell();
            cell2.displayValue = process.MeasLocName;//信号带宽没设置成
            //采样长度（秒）   
            //Cell cell3 = new Cell();
            //cell3.displayValue = channelList.SampleLength.ToString();
            //转换系数a  
            Cell cell4 = new Cell();
            cell4.displayValue = channelList.Coeff_a.ToString();
            //转换系数b	
            Cell cell5 = new Cell();
            cell5.displayValue = channelList.Coeff_b.ToString();

            cells.Add(cell0);
            cells.Add(cell1);
            cells.Add(cell2);
            //cells.Add(cell3);
            cells.Add(cell4);
            cells.Add(cell5);
            return cells.ToArray();
        }
        #endregion

        #region DAU查询

        public static WindDAU? GetDAUbyName(string curWindID, string curDAUID, string targetWindID)
        {
            // 获取当前的dau
            var curDAU = DauManagement.GetDAUNameById(curWindID, curDAUID);
            // 获取目标机组的DAU数据
            if (curDAU != null)
            {
                return DAUSManageModel.GetDAUByTrubineIdAndDauName(targetWindID, curDAU.DAUName);
            }
            return null;
        }
        #endregion
    }
}