﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;
using System.IO;
using CMSFramework.BusinessEntity;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 模板管理接口
    /// </summary>
    public static class TemplateManage
    {
        /// <summary>
        /// 获取数据库版本实体
        /// </summary>
        /// <returns></returns>
        public static Global GetGlobal()
        {
            Global global = null;
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                global = ctx.SysConfigurations.ToList().FirstOrDefault();
            }
            return global;
        }

        /// <summary>
        /// 保存信息至XML文档
        /// </summary>
        /// <param name="_fileName"></param>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static void Save<T>(string _fileName, T _entity)
        {
            using (FileStream fs = new FileStream(_fileName, FileMode.OpenOrCreate))
            {
                XmlSerializer xmlSer = new XmlSerializer(typeof(T));
                xmlSer.Serialize(fs, _entity);
                fs.Close();
            }
        }

        /// <summary>
        /// 从XML文档中获取信息
        /// </summary>
        /// <param name="_fileName"></param>
        /// <returns></returns>
        public static T Load<T>(string _fileName)
        {
            using (FileStream fs = new FileStream(_fileName, FileMode.Open))
            {
                XmlSerializer xmlSer = new XmlSerializer(typeof(T));
                T entity = (T)xmlSer.Deserialize(fs);
                fs.Close();
                return entity; 
            }
        }
    }
}
