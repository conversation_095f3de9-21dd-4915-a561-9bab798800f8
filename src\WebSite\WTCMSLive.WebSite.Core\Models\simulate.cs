﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public static class Simulate
    {

        /// <summary>
        /// 趋势图数据构造
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="meadDef"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="measLocationCount"></param>
        /// <returns></returns>
        public static List<AnalysisData> GetEVDataTrendByTime(string turbineID, string meadDef, DateTime beginTime, DateTime endTime, out int measLocationCount)
        {
            measLocationCount = 0;
            List<AnalysisData> dataList = new List<AnalysisData>();
            for (int i = 0; i < 8; i++)
            {
                AnalysisData data = new AnalysisData();
                data.titleName = "测量位置" + i.ToString();
                data.subText = "子标签" + i.ToString();
                data.id = "MeasDefinition" + i.ToString();
                data.EigenValue = i.ToString();
                data.eigenValueData = new[] { 0.1, 0.2, 0.3, 0.4, 0.5, 0.5, 0.1 };
                data.errorValueData = new[] { 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5 };
                data.waringValueData = new[] { 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3 };
                data.timeValueData = new[] { "2014/06/10", "2014/06/12", "2014/06/13", "2014/06/15", "2014/06/16", "2014/06/18", "2014/06/19" };
                dataList.Add(data);
            }
            return dataList;
        }

        /// <summary>
        /// 调用下层API，根据风场ID，取得对应的机组数据列表
        /// </summary>
        /// <param name="parkId">风场ID</param>
        /// <returns>风场机组状态详细</returns>
        public static List<WindTurbineInfo> GetWindTurbinInfoList(string parkId)
        {
            List<WindTurbineInfo> datalist = new List<WindTurbineInfo>();
            for (int i = 0; i < 10; i++)
            {
                WindTurbineInfo info = new WindTurbineInfo();
                info.windTurbineId = i.ToString();
                info.windTurbineName = info.windTurbineId;
                info.diagStatus = DiagStatus.Apply;
                info.WindTurbineComponent = new Dictionary<string, string>();
                info.windTurbineUpdateTime = DateTime.Now;
                info.diagLevel = DiagLevel.Waring;
                info.alarmEvent = "waring";
                info.DAUstatus = "Normal";
                info.DAUupdateTime = DateTime.Now;
                datalist.Add(info);
            }
                return datalist;
        }

        public static List<WindPark> GetWindParkList()
        {

            List<WindPark> dataList = new List<WindPark>();
            for (int i = 1; i <10; i++)
            {
                WindPark park = new WindPark();
                park.WindParkName = Utility.CreateLinkUrl("Farm",i.ToString(),"风电场"+i.ToString());
                dataList.Add(park);
            }
            return dataList;
        }

        public static BaseTableModel GetTableModel()
        {
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "test";
            tableModel.rows = GetRows().ToArray();
            return tableModel;
        }
        public static List<Rows> GetRows()
        {
            List<Rows> Rows = new List<Rows>();
            for (int i = 0; i < 5; i++)
            {
                Rows cells = new Rows();
                cells.cells = CreateCell("中文测试" + i.ToString()).ToArray();
                Rows.Add(cells);
            }
            return Rows;
        }

        public static List<Cell> CreateCell(string value)
        {
            List<Cell> list = new List<Cell>();
            for (int i = 0; i < 7; i++)
            {
                Cell celldata = new Cell();
                celldata.displayValue = i.ToString() + value;
                list.Add(celldata);
            }
            return list;
        }
        //取得机组情报
        public static WindTurbine GetWindTurbine(string turbineID)
        {
            WindTurbine data = new WindTurbine();
            data.WindTurbineName = "机组01";
            data.WindTurbineCode = "01";
            data.WindTurbineModel = "WT1500";
            data.OperationalDate = DateTime.Now;
            data.WTurbineModel = new WindTurbineModel();
            data.WTurbineModel.Manufactory = "中国南车";

            return data;
        }

        public static List<LabelData_WorkCond> GetWorkCondLabelListByTurId(string turbineID)
        {
            List<LabelData_WorkCond> list = new List<LabelData_WorkCond>();
            LabelData_WorkCond data1 = new LabelData_WorkCond();
            data1.WorkingCondData = new WorkingConditionData();
            data1.WorkingCondData.Param_Value = 12.0;
            list.Add(data1);
            LabelData_WorkCond data2 = new LabelData_WorkCond();
            data2.WorkingCondData = new WorkingConditionData();
            data2.WorkingCondData.Param_Value = 12.0;
            list.Add(data2);
            return list;
        }

        internal static string GetLabelGroupList(string turbineId)
        {
            List<UILabelGroup> labelListUI = new List<UILabelGroup>();
            for (int i = 0; i < 3; i++)
            {
                UILabelGroup labelUI = new UILabelGroup();
                labelUI.ID = i.ToString();
                labelUI.Name = i.ToString();
                labelUI.labelX = i;
                labelUI.labelY = i;
                labelUI.Width = i;
                labelUI.Height = i;
                labelUI.Direction = i.ToString();
                labelUI.DataList = new List<UILabelData>();
                for (int j = 0; j < 2; j++)
                {
                    UILabelData data = new UILabelData();
                    data.LabelGroupID = j.ToString();
                    data.LabelDataID = j.ToString();
                    data.DataName = j.ToString();
                    data.Order = j;
                    data.LabelDataType = j;
                    labelUI.DataList.Add(data);
                }
                labelListUI.Add(labelUI);
            }
            return labelListUI.ToJson();
        }
    }
}