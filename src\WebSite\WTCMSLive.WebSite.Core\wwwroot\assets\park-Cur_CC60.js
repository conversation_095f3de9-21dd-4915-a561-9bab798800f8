import{u as Y,W as Z}from"./table-DznTy2O5.js";import{O as K}from"./index-BKL_RKUZ.js";import{r as i,u as ee,j as O,h as te,w as ae,f as F,d,o as D,i as P,b as p,c as B,q as ne,F as oe,g as C,p as q,t as A,m as b}from"./index-BedJHPLx.js";import{u as le}from"./jfDeviceManage-DLlM3C01.js";import{g as re}from"./tools-DC78Tda0.js";import{_ as se,p as ie,o as ue,s as h}from"./useWebSocket-DEdJh53k.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as ce}from"./ActionButton-FpgTQOJj.js";import{B as pe}from"./index-CTEgH1Bv.js";import{M as fe}from"./index-BD0EDEeZ.js";import"./styleChecker-D6uzjM95.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const f=320,ve={__name:"park",setup(me){const c=le(),V=Y(),y=i(!1),w=ee(),I=i(""),g=i(""),T=i({}),r=i([]);i([]);const _=i([]),S=i(w.params.id),u=i(!1),$=i([]),z=i(!1),L=O(()=>g.value==="useMeasurementDefinition"?"1200px":"600px"),N=O(()=>g.value==="otherConfig"?"part":"horizontal"),v=(n,a)=>[{title:"机组名称",dataIndex:"windTurbineName",labelInValue:!a,columnWidth:100,inputType:"select",selectOptions:V.deviceOptions,disabled:a,isrequired:!0,formItemWidth:f},{title:"采集器类型",dataIndex:"dauType",hasChangeEvent:!0,columnWidth:140,inputType:"select",isrequired:!0,selectOptions:c.dAUTypeList,isdisplay:!a,formItemWidth:f,headerOperations:{filters:[]}},{title:"DAU IP地址",dataIndex:"ip",columnWidth:150,formItemWidth:f,columnOperate:{type:"ip"},validateRules:re({type:"ip",title:"IP地址",required:!0})},{title:"服务器地址",dataIndex:"port",columnWidth:70,formItemWidth:f},{title:"在离线状态",dataIndex:"dauOnOffStatus",otherColumn:!0,columnWidth:120,formItemWidth:f,headerOperations:{filters:[{text:"在线",value:2},{text:"离线",value:1}]}}],m=i(v()),x=async()=>{u.value=!0;let n=await c.fetchGetDAUList({WindParkId:S.value});r.value=n,u.value=!1,m.value=v()};te(()=>{x()}),ae(()=>w.params.id,n=>{c.reset(),S.value=n,x()});const U=async(n,a)=>{u.value=!0;let o=r.value.filter(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)),t=await c.fetchStartAcquisition(o);u.value=!1,t&&t.code===1?(m.value=[...v(),{title:"采集状态",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:t.data[e.ip]}:e)):b.error("操作失败")},R=async(n,a)=>{u.value=!0;let o=r.value.filter(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)),t=await c.fetchStopAcquisition(o);u.value=!1,t&&t.code===1?(m.value=[...v(),{title:"停止采集",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:t.data[e.ip]}:e)):b.error("操作失败")},J=async(n,a)=>{u.value=!0;let o=r.value.filter(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)),t=await c.fetchSetMeasureDefinition(o);u.value=!1,t&&t.code===1?(m.value=[...v(),{title:"测量定义下发",dataIndex:"operateStatus",otherColumn:!0,columnWidth:120,formItemWidth:f}],r.value=r.value.map(e=>n.includes(`${e.windTurbineID}&&${e.dauID}`)?{...e,operateStatus:t.data[e.ip]}:e)):b.error("操作失败")},j=async(n,a)=>{g.value=a,I.value="推送配置",$.value=n,m.value=[...v()],_.value=ie(),M()},E=async(n,a)=>{g.value=a,I.value="高级参数配置",$.value=n,_.value=ue(),m.value=[...v()],M()},M=()=>{y.value=!0},W=n=>{y.value=!1,_.value=[],T.value={},g.value="",I.value=""},G=async n=>{u.value=!0;let o=r.value.filter(e=>$.value.includes(`${e.windTurbineID}&&${e.dauID}`)).map(e=>({...e,...n})),t=null;g.value==="pushConfig"?t=await c.fetchJfSetSFTPConfig(o):g.value==="otherConfig"&&(t=await c.fetchJfSetAdvancedParameters(o)),u.value=!1,t?(b.success("操作成功"),W()):b.error("操作失败")},H=async n=>{const a=await h.startConnection("/Hubs/ServerPerformanceHub");if(z.value=a,a){h.onReceiveMessage("ProgressMonitoringStarted",t=>{console.log("进度启动结果",t)}),h.onReceiveMessage("ProgressUpdate",t=>{console.log("进度更新",t)}),h.onReceiveMessage("CurrentProgressUpdate",t=>{console.log("当前进度响应",t)});const o={parkID:S.value,daus:n};h.sendMessage("StartProgressMonitoring",o)}else b.error("连接失败")},Q=async(n,a)=>{let o=r.value.filter(t=>n.includes(`${t.windTurbineID}&&${t.dauID}`));await c.fetchGetWaveFormData(o),m.value=[...v(),{title:"录波数据进度",dataIndex:"process",otherColumn:!0,columnWidth:120,formItemWidth:f}],H(o),r.value=r.value.map(t=>n.includes(`${t.windTurbineID}&&${t.dauID}`)?{...t,process:100}:t)};return(n,a)=>{const o=pe,t=se,e=fe,X=ce;return D(),F(X,{spinning:u.value,size:"large"},{default:d(()=>[P("div",null,[p(Z,{ref:"table",size:"default","table-key":"0","table-title":"设备列表","table-columns":m.value,recordKey:l=>`${l.windTurbineID}&&${l.dauID}`,"table-datas":r.value,noBatchApply:!0,selectedRows:!0},{rightButtons:d(({selectedRowKeys:l})=>[p(o,{type:"primary",onClick:s=>U(l,"startCollection"),disabled:!l.length},{default:d(()=>a[0]||(a[0]=[C(" 启动采集 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"]),p(o,{onClick:s=>R(l,"stopCollection"),disabled:!l.length},{default:d(()=>a[1]||(a[1]=[C(" 停止采集 ",-1)])),_:2,__:[1]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:s=>J(l,"useMeasurementDefinition"),disabled:!l.length},{default:d(()=>a[2]||(a[2]=[C(" 测量定义下发 ",-1)])),_:2,__:[2]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:s=>j(l,"pushConfig"),disabled:!l.length},{default:d(()=>a[3]||(a[3]=[C(" 推送配置 ",-1)])),_:2,__:[3]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:s=>Q(l,"getObtainWaveform"),disabled:!l.length},{default:d(()=>a[4]||(a[4]=[C(" 获取录波数据 ",-1)])),_:2,__:[4]},1032,["onClick","disabled"]),p(o,{type:"primary",onClick:s=>E(l,"otherConfig"),disabled:!l.length},{default:d(()=>a[5]||(a[5]=[C(" 高级参数配置 ",-1)])),_:2,__:[5]},1032,["onClick","disabled"])]),otherColumn:d(({record:l,text:s,column:k})=>[k.dataIndex==="dauOnOffStatus"?(D(),B(oe,{key:0},[P("span",{class:q([s==2?"green":"gray","circle"])},null,2),C(" "+A(s==1?"离线":"在线"),1)],64)):k.dataIndex==="process"&&s&&s!==""?(D(),F(t,{key:1,type:"circle",size:30,"stroke-color":{"0%":"#108ee9","100%":"#87d068"},percent:90})):k.dataIndex==="operateStatus"?(D(),B("span",{key:2,class:q([s?"greenText":"redtext"])},A(s?"成功":"失败"),3)):ne("",!0)]),_:1},8,["table-columns","recordKey","table-datas"]),p(e,{maskClosable:!1,width:L.value,open:y.value,title:I.value,footer:"",destroyOnClose:!0,onCancel:W},{default:d(()=>[p(K,{titleCol:_.value,initFormData:T.value,formlayout:N.value,onSubmit:G},null,8,["titleCol","initFormData","formlayout"])]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},Oe=de(ve,[["__scopeId","data-v-d91a9741"]]);export{Oe as default};
