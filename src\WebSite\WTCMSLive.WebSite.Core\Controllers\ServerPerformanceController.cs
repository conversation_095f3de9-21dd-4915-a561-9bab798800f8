using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using WTCMSLive.WebSite.Core.DTOs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ServerPerformanceController : ControllerBase
    {
        private readonly ILogger<ServerPerformanceController> _logger;
        private readonly IMetricsStorageService _storageService;

        public ServerPerformanceController(
            ILogger<ServerPerformanceController> logger,
            IMetricsStorageService storageService)
        {
            _logger = logger;
            _storageService = storageService;
        }

        /// <summary>
        /// 获取服务器性能监控信息
        /// </summary>
        /// <returns>服务器性能数据</returns>
        [HttpGet("performance")]
        public IActionResult GetServerPerformance()
        {
            try
            {
                var performanceData = new ServerPerformanceDTO
                {
                    CpuUsage = GetCpuUsage(),
                    MemoryUsage = GetMemoryUsage(),
                    DiskUsage = GetDiskUsage(),
                    Timestamp = DateTime.Now,
                    ServerName = Environment.MachineName,
                    OperatingSystem = GetOperatingSystemInfo()
                };

                return Ok(performanceData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetServerPerformance]获取服务器性能信息失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[GetServerPerformance]获取服务器性能信息失败", ex);
                return Ok(new ServerPerformanceDTO
                {
                    CpuUsage = new CpuUsageDTO { UsagePercentage = 0, ErrorMessage = "获取CPU信息失败" },
                    MemoryUsage = new MemoryUsageDTO { UsagePercentage = 0, ErrorMessage = "获取内存信息失败" },
                    DiskUsage = new List<DiskUsageDTO>(),
                    Timestamp = DateTime.Now,
                    ServerName = Environment.MachineName,
                    OperatingSystem = "Unknown"
                });
            }
        }

        /// <summary>
        /// 查询历史性能数据
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>历史性能数据</returns>
        [HttpPost("query-history")]
        public async Task<IActionResult> QueryHistoryPerformance([FromBody] MetricsQueryRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (request.StartTime >= request.EndTime)
                {
                    return Ok(ApiResponse<string>.Error("开始时间必须小于结束时间"));
                }

                var data = await _storageService.QueryMetricsDataAsync(request);
                return Ok(ApiResponse<object>.Success(data, "查询历史性能数据成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[QueryHistoryPerformance]查询历史性能数据失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[QueryHistoryPerformance]查询历史性能数据失败", ex);
                return Ok(ApiResponse<string>.Error("查询历史性能数据失败"));
            }
        }

        /// <summary>
        /// 下载历史性能数据
        /// </summary>
        /// <param name="request">下载请求</param>
        /// <returns>性能数据文件</returns>
        [HttpPost("download-history")]
        public async Task<IActionResult> DownloadHistoryPerformance([FromBody] MetricsQueryRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                if (request.StartTime >= request.EndTime)
                {
                    return Ok(ApiResponse<string>.Error("开始时间必须小于结束时间"));
                }

                var downloadData = await _storageService.DownloadMetricsDataAsync(request);

                if (downloadData == null || downloadData.FileData == null)
                {
                    return Ok(ApiResponse<string>.Error("没有找到指定时间范围的数据"));
                }

                return File(downloadData.FileData, downloadData.ContentType, downloadData.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DownloadHistoryPerformance]下载历史性能数据失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[DownloadHistoryPerformance]下载历史性能数据失败", ex);
                return Ok(ApiResponse<string>.Error("下载历史性能数据失败"));
            }
        }

        /// <summary>
        /// 下载所有性能数据
        /// </summary>
        /// <returns>所有性能数据CSV文件</returns>
        [HttpGet("download")]
        public async Task<IActionResult> DownloadAllPerformanceData()
        {
            try
            {
                var downloadData = await _storageService.DownloadAllMetricsDataAsync();

                if (downloadData == null || downloadData.FileData == null)
                {
                    return Ok(ApiResponse<string>.Error("没有找到任何数据"));
                }

                return File(downloadData.FileData, downloadData.ContentType, downloadData.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DownloadAllPerformanceData]下载所有性能数据失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[DownloadAllPerformanceData]下载所有性能数据失败", ex);
                return Ok(ApiResponse<string>.Error("下载所有性能数据失败"));
            }
        }

        /// <summary>
        /// 获取性能数据文件信息
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>文件信息列表</returns>
        [HttpPost("file-info")]
        public async Task<IActionResult> GetPerformanceFileInfo([FromBody] MetricsQueryRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                var fileInfo = await _storageService.GetMetricsFileInfoAsync(request.StartTime, request.EndTime);
                return Ok(ApiResponse<object>.Success(fileInfo, "获取文件信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetPerformanceFileInfo]获取性能数据文件信息失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[GetPerformanceFileInfo]获取性能数据文件信息失败", ex);
                return Ok(ApiResponse<string>.Error("获取文件信息失败"));
            }
        }

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        /// <returns>存储统计信息</returns>
        [HttpGet("storage-statistics")]
        public async Task<IActionResult> GetStorageStatistics()
        {
            try
            {
                var statistics = await _storageService.GetStorageStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetStorageStatistics]获取存储统计信息失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[GetStorageStatistics]获取存储统计信息失败", ex);
                return Ok(ApiResponse<string>.Error("获取存储统计信息失败"));
            }
        }

        /// <summary>
        /// 手动清理过期数据
        /// </summary>
        /// <returns>清理结果</returns>
        [HttpPost("cleanup-expired")]
        public async Task<IActionResult> CleanupExpiredData()
        {
            try
            {
                var cleanedCount = await _storageService.CleanupExpiredDataAsync();
                return Ok(ApiResponse<string>.Success($"成功清理了 {cleanedCount} 个过期数据文件"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CleanupExpiredData]清理过期数据失败");
                CMSFramework.Logger.Logger.LogErrorMessage("[CleanupExpiredData]清理过期数据失败", ex);
                return Ok(ApiResponse<string>.Error("清理过期数据失败"));
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        /// <returns>CPU使用率信息</returns>
        private CpuUsageDTO GetCpuUsage()
        {
            try
            {
                double cpuPercentage = 0;

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    cpuPercentage = GetWindowsCpuUsage();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    cpuPercentage = GetLinuxCpuUsage();
                }
                else
                {
                    // 对于其他平台，使用进程CPU时间计算（不太准确但可用）
                    cpuPercentage = GetProcessCpuUsage();
                }

                return new CpuUsageDTO
                {
                    UsagePercentage = Math.Max(0, Math.Min(100, Math.Round(cpuPercentage, 2))),
                    ProcessorCount = Environment.ProcessorCount,
                    ErrorMessage = string.Empty
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetCpuUsage]获取CPU使用率失败");
                return new CpuUsageDTO
                {
                    UsagePercentage = 0,
                    ProcessorCount = Environment.ProcessorCount,
                    ErrorMessage = $"获取CPU使用率失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取Windows系统CPU使用率
        /// </summary>
        /// <returns>CPU使用率</returns>
        private double GetWindowsCpuUsage()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new System.Management.ManagementObjectSearcher("select * from Win32_PerfRawData_PerfOS_Processor where Name='_Total'");
                    var results = searcher.Get();
                    foreach (System.Management.ManagementObject obj in results)
                    {
                        var timestamp1 = Convert.ToUInt64(obj["Timestamp_Sys100NS"]);
                        var processorTime1 = Convert.ToUInt64(obj["PercentProcessorTime"]);

                        Thread.Sleep(100);

                        obj.Get();
                        var timestamp2 = Convert.ToUInt64(obj["Timestamp_Sys100NS"]);
                        var processorTime2 = Convert.ToUInt64(obj["PercentProcessorTime"]);

                        var cpuUsage = (1.0 - (double)(processorTime2 - processorTime1) / (timestamp2 - timestamp1)) * 100;
                        return Math.Round(cpuUsage, 2);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetWindowsCpuUsage]获取Windows CPU使用率失败");
            }

            return 0;
        }

        /// <summary>
        /// 获取Linux系统CPU使用率
        /// </summary>
        /// <returns>CPU使用率</returns>
        private double GetLinuxCpuUsage()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && System.IO.File.Exists("/proc/stat"))
                {
                    // 读取第一次CPU统计
                    var stat1 = ReadLinuxCpuStat();
                    if (stat1 == null) return 0;

                    Thread.Sleep(100);

                    // 读取第二次CPU统计
                    var stat2 = ReadLinuxCpuStat();
                    if (stat2 == null) return 0;

                    // 计算CPU使用率
                    var totalDiff = stat2.Value.Total - stat1.Value.Total;
                    var idleDiff = stat2.Value.Idle - stat1.Value.Idle;

                    if (totalDiff <= 0) return 0;

                    var cpuUsage = (1.0 - (double)idleDiff / totalDiff) * 100;
                    return Math.Round(cpuUsage, 2);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetLinuxCpuUsage]获取Linux CPU使用率失败");
            }

            return 0;
        }

        /// <summary>
        /// 读取Linux CPU统计信息
        /// </summary>
        /// <returns>CPU统计信息</returns>
        private (long Total, long Idle)? ReadLinuxCpuStat()
        {
            try
            {
                var lines = System.IO.File.ReadAllLines("/proc/stat");
                var cpuLine = lines.FirstOrDefault(l => l.StartsWith("cpu "));
                if (string.IsNullOrEmpty(cpuLine)) return null;

                var parts = cpuLine.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length < 5) return null;

                // CPU时间：user, nice, system, idle, iowait, irq, softirq, steal
                var user = long.Parse(parts[1]);
                var nice = long.Parse(parts[2]);
                var system = long.Parse(parts[3]);
                var idle = long.Parse(parts[4]);
                var iowait = parts.Length > 5 ? long.Parse(parts[5]) : 0;
                var irq = parts.Length > 6 ? long.Parse(parts[6]) : 0;
                var softirq = parts.Length > 7 ? long.Parse(parts[7]) : 0;
                var steal = parts.Length > 8 ? long.Parse(parts[8]) : 0;

                var total = user + nice + system + idle + iowait + irq + softirq + steal;
                return (total, idle);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[ReadLinuxCpuStat]读取Linux CPU统计失败");
                return null;
            }
        }

        /// <summary>
        /// 获取进程CPU使用率（备用方法）
        /// </summary>
        /// <returns>CPU使用率</returns>
        private double GetProcessCpuUsage()
        {
            try
            {
                using var process = Process.GetCurrentProcess();
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;

                Thread.Sleep(100);

                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;

                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

                return cpuUsageTotal * 100;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetProcessCpuUsage]获取进程CPU使用率失败");
                return 0;
            }
        }

        /// <summary>
        /// 获取内存使用率
        /// </summary>
        /// <returns>内存使用率信息</returns>
        private MemoryUsageDTO GetMemoryUsage()
        {
            try
            {
                var gc = GC.GetTotalMemory(false);
                var workingSet = Environment.WorkingSet;
                
                long totalMemory = 0;
                long availableMemory = 0;

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    var (total, available) = GetWindowsMemoryInfo();
                    totalMemory = total;
                    availableMemory = available;
                    _logger.LogDebug($"[GetMemoryUsage] Windows - 总内存: {totalMemory}字节, 可用内存: {availableMemory}字节");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    var (total, available) = GetLinuxMemoryInfo();
                    totalMemory = total;
                    availableMemory = available;
                    _logger.LogDebug($"[GetMemoryUsage] Linux - 总内存: {totalMemory}字节, 可用内存: {availableMemory}字节");
                }

                var usedMemory = totalMemory - availableMemory;
                _logger.LogDebug($"[GetMemoryUsage] 计算结果 - 已用内存: {usedMemory}字节");
                var usagePercentage = totalMemory > 0 ? Math.Round((double)usedMemory / totalMemory * 100, 2) : 0;
                
                return new MemoryUsageDTO
                {
                    UsagePercentage = usagePercentage,
                    TotalMemoryMB = Math.Round(totalMemory / (1024.0 * 1024.0 * 1024.0), 2), // 转换为GB
                    UsedMemoryMB = Math.Round(usedMemory / (1024.0 * 1024.0), 2),
                    AvailableMemoryMB = Math.Round(availableMemory / (1024.0 * 1024.0), 2),
                    ProcessWorkingSetMB = Math.Round(workingSet / (1024.0 * 1024.0), 2),
                    GCMemoryMB = Math.Round(gc / (1024.0 * 1024.0), 2),
                    ErrorMessage = string.Empty
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetMemoryUsage]获取内存使用率失败");
                return new MemoryUsageDTO
                {
                    UsagePercentage = 0,
                    TotalMemoryMB = 0,
                    UsedMemoryMB = 0,
                    AvailableMemoryMB = 0,
                    ProcessWorkingSetMB = Math.Round(Environment.WorkingSet / (1024.0 * 1024.0), 2),
                    GCMemoryMB = Math.Round(GC.GetTotalMemory(false) / (1024.0 * 1024.0), 2),
                    ErrorMessage = $"获取内存使用率失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取Windows系统内存信息
        /// </summary>
        /// <returns>总内存和可用内存（字节）</returns>
        private (long total, long available) GetWindowsMemoryInfo()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT TotalVisibleMemorySize, FreePhysicalMemory FROM Win32_OperatingSystem");
                    var results = searcher.Get();
                    foreach (ManagementObject obj in results)
                    {
                        // WMI返回的是KB，需要转换为字节
                        var totalBytes = Convert.ToInt64(obj["TotalVisibleMemorySize"]) * 1024;
                        var freeBytes = Convert.ToInt64(obj["FreePhysicalMemory"]) * 1024;

                        _logger.LogDebug($"[GetWindowsMemoryInfo] 总内存: {totalBytes / 1024 / 1024}MB, 可用内存: {freeBytes / 1024 / 1024}MB");
                        return (totalBytes, freeBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetWindowsMemoryInfo]获取Windows内存信息失败");
            }

            return (0, 0);
        }

        /// <summary>
        /// 获取Linux系统内存信息
        /// </summary>
        /// <returns>总内存和可用内存（字节）</returns>
        private (long total, long available) GetLinuxMemoryInfo()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && System.IO.File.Exists("/proc/meminfo"))
                {
                    var lines = System.IO.File.ReadAllLines("/proc/meminfo");
                    long totalBytes = 0, availableBytes = 0;
                    long freeBytes = 0, buffersBytes = 0, cachedBytes = 0;

                    foreach (var line in lines)
                    {
                        var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length < 2) continue;

                        if (line.StartsWith("MemTotal:") && long.TryParse(parts[1], out var total))
                        {
                            totalBytes = total * 1024; // 转换为字节
                        }
                        else if (line.StartsWith("MemAvailable:") && long.TryParse(parts[1], out var available))
                        {
                            availableBytes = available * 1024; // 转换为字节
                        }
                        else if (line.StartsWith("MemFree:") && long.TryParse(parts[1], out var free))
                        {
                            freeBytes = free * 1024;
                        }
                        else if (line.StartsWith("Buffers:") && long.TryParse(parts[1], out var buffers))
                        {
                            buffersBytes = buffers * 1024;
                        }
                        else if (line.StartsWith("Cached:") && long.TryParse(parts[1], out var cached))
                        {
                            cachedBytes = cached * 1024;
                        }
                    }

                    // 如果没有MemAvailable，则使用MemFree + Buffers + Cached估算
                    if (availableBytes == 0 && totalBytes > 0)
                    {
                        availableBytes = freeBytes + buffersBytes + cachedBytes;
                    }

                    return (totalBytes, availableBytes);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetLinuxMemoryInfo]获取Linux内存信息失败");
            }

            return (0, 0);
        }

        /// <summary>
        /// 获取磁盘使用率
        /// </summary>
        /// <returns>磁盘使用率信息列表</returns>
        private List<DiskUsageDTO> GetDiskUsage()
        {
            var diskUsageList = new List<DiskUsageDTO>();
            
            try
            {
                var drives = DriveInfo.GetDrives();
                
                foreach (var drive in drives)
                {
                    try
                    {
                        if (drive.IsReady)
                        {
                            var totalSize = drive.TotalSize;
                            var freeSpace = drive.TotalFreeSpace;
                            var usedSpace = totalSize - freeSpace;
                            var usagePercentage = totalSize > 0 ? Math.Round((double)usedSpace / totalSize * 100, 2) : 0;
                            
                            diskUsageList.Add(new DiskUsageDTO
                            {
                                DriveName = drive.Name,
                                DriveType = drive.DriveType.ToString(),
                                FileSystem = drive.DriveFormat,
                                UsagePercentage = usagePercentage,
                                TotalSizeGB = Math.Round(totalSize / (1024.0 * 1024.0 * 1024.0), 2),
                                UsedSizeGB = Math.Round(usedSpace / (1024.0 * 1024.0 * 1024.0), 2),
                                FreeSizeGB = Math.Round(freeSpace / (1024.0 * 1024.0 * 1024.0), 2),
                                ErrorMessage = string.Empty
                            });
                        }
                        else
                        {
                            diskUsageList.Add(new DiskUsageDTO
                            {
                                DriveName = drive.Name,
                                DriveType = drive.DriveType.ToString(),
                                FileSystem = "Unknown",
                                UsagePercentage = 0,
                                TotalSizeGB = 0,
                                UsedSizeGB = 0,
                                FreeSizeGB = 0,
                                ErrorMessage = "驱动器未就绪"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[GetDiskUsage]获取磁盘 {DriveName} 信息失败", drive.Name);
                        diskUsageList.Add(new DiskUsageDTO
                        {
                            DriveName = drive.Name,
                            DriveType = drive.DriveType.ToString(),
                            FileSystem = "Unknown",
                            UsagePercentage = 0,
                            TotalSizeGB = 0,
                            UsedSizeGB = 0,
                            FreeSizeGB = 0,
                            ErrorMessage = $"获取磁盘信息失败: {ex.Message}"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetDiskUsage]获取磁盘使用率失败");
            }
            
            return diskUsageList.OrderBy(d => d.DriveName).ToList();
        }

        /// <summary>
        /// 获取操作系统信息
        /// </summary>
        /// <returns>操作系统信息</returns>
        private string GetOperatingSystemInfo()
        {
            try
            {
                var osDescription = RuntimeInformation.OSDescription;
                var architecture = RuntimeInformation.OSArchitecture.ToString();
                return $"{osDescription} ({architecture})";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[GetOperatingSystemInfo]获取操作系统信息失败");
                return "Unknown";
            }
        }

        #endregion
    }
}
