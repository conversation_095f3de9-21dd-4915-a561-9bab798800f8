import{u as Oe,C as Ae,W as P}from"./table-DznTy2O5.js";import{O as Re}from"./index-BKL_RKUZ.js";import{W as We}from"./index-C1FH5xdO.js";import{S as xe,$ as Ne,a0 as _e,a1 as Se,a2 as Ve,a3 as Pe,a4 as Fe,a5 as Ge,a6 as qe,a7 as Ee,a8 as $e,a9 as Be,aa as Ke,ab as je,ac as ze,ad as Ye,ae as Ue,af as He,ag as Xe,ah as Ze,ai as Je,aj as Qe,ak as et,al as tt,am as at,an as ot,ao as st,ap as nt,aq as rt,r as I,u as lt,X as it,j as oe,w as ct,f as se,d as k,Z as ut,o as M,c as R,b as T,i as be,F as pt,e as dt,g as ne,t as mt,q as he,m as b,ar as fe}from"./index-BedJHPLx.js";import{a as re,b as F,f as le}from"./tools-DC78Tda0.js";import{u as bt}from"./model-Db0y2bgw.js";import{u as ht}from"./devTree-Dgv5CE1u.js";import{_ as ft}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as yt}from"./ActionButton-FpgTQOJj.js";import{D as Dt,a as vt}from"./index-BPV4rCOw.js";import{M as wt}from"./index-BD0EDEeZ.js";import{B as Lt}from"./index-CTEgH1Bv.js";import"./styleChecker-D6uzjM95.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const It=xe("configDevice",{state:()=>({deviceInfo:{},vibMeaslocationList:[],processMeaslocationList:[],componentList:[],sectionList:[],orientatioList:[],workCondMeasLocDicOptions:[],workCondMeasLocsList:[],enumWorkConDataSourceOptions:[],rotSpdMeasLocList:[],modbusMeasLocList:[],sVMParamTypeList:[],modbusMeasLocoptions:[],OilParamType:[]}),actions:{reset(){this.$reset()},async fetchDeviceInfo(o={}){try{const e=await rt(o);return this.deviceInfo=e,e}catch(e){throw console.error("获取设备失败:",e),e}},async fetcheditOneDevice(o={}){try{return await nt(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchGetVibMeaslocation(o={}){try{const e=await st(o);return e&&e.length>0?(this.getVibMeaslocation=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetProcessMeaslocation(o={}){try{const e=await ot(o);return e&&e.length>0?(this.processMeaslocationList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetOrientationList(o={}){try{const e=await at(o);let u=re(e,"key");return this.orientatioList=u,u}catch(e){throw console.error("获取失败:",e),e}},async fetchGetComponentList(o={}){try{const e=await tt(o);if(e&&e.length>0){let u=F(e,{label:"componentName",value:"componentID"},{nother:!0});return this.componentList=u,u}return[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSectionList(o={}){try{const e=await et(o);let u=re(e,"key");return this.sectionList=u,u}catch(e){throw console.error("获取失败:",e),e}},async fetchGetWorkCondMeasLocDic(o={}){try{const e=await Qe(o);let u=F(e);return this.workCondMeasLocDicOptions=u,u}catch(e){throw console.error("获取失败:",e),e}},async fetchGetEnumWorkConDataSource(o={}){try{const e=await Je(o);let u=F(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.enumWorkConDataSourceOptions=u,u}catch(e){throw console.error("获取失败:",e),e}},async fetchAddVibMeasLocs(o={}){try{return await Ze(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditVibMeasLoc(o={}){try{return await Xe(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteVibMeasLocs(o={}){try{return await He(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchAddProcessMeasLocs(o={}){try{return await Ue(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditProcessMeasLoc(o={}){try{return await Ye(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteProcessMeasLocs(o={}){try{return await ze(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetWorkCondMeasLocs(o={}){try{const e=await je(o);return e&&e.length>0?(this.workCondMeasLocsList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchAddWorkingConditionMeaslocs(o={}){try{return await Ke(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditWorkingConditionMeas(o={}){try{return await Be(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteWorkingConditionMeasBatch(o={}){try{return await $e(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetRotSpdMeasLocList(o={}){try{const e=await Ee(o);return this.rotSpdMeasLocList=e,e}catch(e){throw console.error("删除失败:",e),e}},async fetchAddRotSpdMeaslocs(o={}){try{return await qe(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditRotSpdLoc(o={}){try{return await Ge(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteRotSpdLoc(o={}){try{return await Fe(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetModbusMeasLocList(o){try{const e=await Pe(o);this.modbusMeasLocList=e;let u=F(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=u,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddModbusMeasloc(o){try{return await Ve(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteMeasLoc(o){try{return await Se(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetSVMParamType(o){try{const e=await _e(o);let u=F(e,{label:"value",value:"value"},{nother:!0});return this.sVMParamType=u,u}catch(e){throw console.error("操作失败:",e),e}},async fetchGetOilParamType(o){try{const e=await Ne(o);let u=re(e,!1);return this.OilParamType=u,u}catch(e){throw console.error("操作失败:",e),e}}}}),Ct={class:"border"},Tt={key:0,class:"editForm"},gt={key:0,class:"getMeasLocName"},kt={key:0,class:"setNameBtn"},Mt={key:2},Ot="YYYY-MM-DD",At={__name:"device",setup(o){const e=It();bt();const u=ht(),ye=Oe(),Y=[{label:"晃度仪",value:"svm",text:"晃度仪"},{label:"倾角仪",value:"tim",text:"倾角仪"},{label:"其他Modbus",value:"modbus",text:"其他Modbus"},{label:"油液",value:"oil",text:"油液"}],G=()=>[{title:"振动测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,columnWidth:200,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},inputType:"selectinput",selectOptions:[],hasChangeEvent:!0,customRender:({record:t})=>t.devTurComponent&&t.devTurComponent.componentName?t.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],q=t=>[{title:"电流电压测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,afterContent:!0,isrequired:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,columnWidth:200,inputType:"selectinput",selectOptions:[],hasChangeEvent:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},customRender:({record:a})=>a.devTurComponent&&a.devTurComponent.componentName?a.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],U=t=>[{title:"转速测量位置名称",dataIndex:"measLocName",columnWidth:80,isrequired:!0},{title:"变速比",dataIndex:"gearRatio",columnWidth:100,isrequired:!0},{title:"编码器线数",dataIndex:"lineCounts",columnWidth:70,isrequired:!0}],E=t=>[{title:"工况测量位置名称",dataIndex:"measLocName",width:300,columnWidth:220,afterContent:!0,isrequired:!0},{title:"工况参数",dataIndex:"mDFWorkData",labelInValue:!0,columnWidth:220,inputType:"select",selectOptions:[],isrequired:!0,...t?{}:{customRender:({record:a})=>{if(!e.workCondMeasLocDicOptions||!e.workCondMeasLocDicOptions.length)return a.param_Type_Code||"";const n=e.workCondMeasLocDicOptions.find(l=>l.value==a.param_Type_Code);return n?n.label:a.param_Type_Code}}},{title:"数据来源",dataIndex:"datafrom",labelInValue:!0,inputType:"select",columnWidth:220,selectOptions:[],isrequired:!0,headerOperations:{filters:e.enumWorkConDataSourceOptions,filterDataIndex:["fieldBusType"]},...t?{}:{customRender:({text:a,record:n})=>{if(!e.enumWorkConDataSourceOptions||!e.enumWorkConDataSourceOptions.length)return a;const l=e.enumWorkConDataSourceOptions.find(r=>r.value==n.fieldBusType);return l?l.label:a}}}],$=()=>[{title:"测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["componentName"]},columnWidth:160,inputType:"selectinput",selectOptions:[],hasChangeEvent:!0,customRender:({record:t})=>t.componentName||""},{title:"截面",dataIndex:"sectionName",columnWidth:160,inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]},columnWidth:160},{title:"测量位置类型",dataIndex:"measType",inputType:"select",isrequired:!0,hasChangeEvent:!0,selectOptions:Y,headerOperations:{filters:Y},customRender:({text:t,record:a})=>{const n=Y.find(l=>l.value==a.measType);return n?n.label:t}},{title:"物理量",dataIndex:"physicalType",inputType:"select",labelInValue:!0,selectOptions:[],tableList:[],headerOperations:{filters:[]}}],ie=lt(),H=I(""),A=I(null),B=I(""),w=I(""),W=I(),v=I({}),i=I(ie.params.id),h=I(""),X=I(!1),D=I([]),Z=I(!1),s=it({table1Columns:G(),table2Columns:q(),table3Columns:U(),table4Columns:E(),table5Columns:$(),tableDatas1:[],tableDatas2:[],tableDatas3:[],tableDatas4:[],tableDatas5:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{}}),De=oe(()=>{switch(h.value){case"1":return["measLocName"];case"2":return["measLocName"];case"3":return["measLocName"];case"4":return["measLocName","mDFWorkData"];case"5":return["measLocName"];default:return[]}}),ve=oe(()=>{switch(h.value){case"1":return["measLocName"];case"2":return["measLocName"];case"3":return["measLocName"];case"4":return["measLocName"];case"5":return["measLocName"];default:return[]}}),we=()=>{let t=u.findAncestorsWithNodes(i.value);t&&t.length&&t.length>1&&(H.value=t[t.length-2].id)},ce=(t={})=>[{label:"设备名称",value:t.windTurbineName},{label:"设备编号",value:t.windTurbineCode},{label:"设备型号",value:t.windTurbineModel},{label:"投运日期",value:t.operationalDate?ut(t.operationalDate).format(Ot):""},{label:"额定功率(KW)",value:t.ratedPower},{label:"设备厂商",value:t.wTurbineModel&&t.wTurbineModel.manufactory?t.wTurbineModel.manufactory:""}],ue=I([ce({})]),pe=async t=>{if(X.value=!0,i.value){const a=await e.fetchDeviceInfo({turbineID:i.value});ue.value=ce(a)}X.value=!1},Le=async t=>{H.value&&await ye.fetchDevTreedDevicelist({windParkID:H.value,useTobath:!0})},Ie=oe(()=>w.value==="batchAdd"?"1200px":"600px"),K=async t=>{i.value&&(s.tableDatas1=await e.fetchGetVibMeaslocation({turbineID:i.value}),s.table1Columns=G())},j=async t=>{i.value&&(s.tableDatas2=await e.fetchGetProcessMeaslocation({turbineID:i.value}),s.table2Columns=q())},z=async t=>{i.value&&(s.tableDatas4=await e.fetchGetWorkCondMeasLocs({turbineID:i.value}),s.table4Columns=E())},J=async t=>{i.value&&(s.tableDatas3=await e.fetchGetRotSpdMeasLocList({turbineID:i.value}))},Q=async t=>{i.value&&(s.tableDatas5=await e.fetchGetModbusMeasLocList({turbineID:i.value}),s.table5Columns=$())},x=async()=>{await e.fetchGetComponentList({turbineID:i.value})},N=async()=>{(!e.orientatioList||e.orientatioList.length<1)&&await e.fetchGetOrientationList()},ee=async()=>{(!e.sVMParamType||e.sVMParamType.length<1)&&await e.fetchGetSVMParamType()},te=async()=>{(!e.workCondMeasLocDicOptions||e.workCondMeasLocDicOptions.length<1)&&await e.fetchGetWorkCondMeasLocDic()},ae=async()=>{(!e.enumWorkConDataSourceOptions||e.enumWorkConDataSourceOptions.length<1)&&await e.fetchGetEnumWorkConDataSource()};ct(()=>ie.params.id,async t=>{t&&(e.reset(),i.value=t,we(),await Le(),pe(),K(),j(),await ae(),await te(),z(),J(),Q())},{immediate:!0});const _=async t=>{const{tableKey:a,title:n,operateType:l}=t;switch(w.value=l,h.value=a,B.value="添加"+n.split("列表")[0],a){case"1":await x(),await N();let r=G();r[1].selectOptions=[...e.componentList],r[3].selectOptions=[...e.orientatioList],D.value=[...r];break;case"2":await x(),await N();let p=q();p[1].selectOptions=[...e.componentList],p[3].selectOptions=[...e.orientatioList],D.value=[...p];break;case"3":let d=U();D.value=[...d],v.value={measLocName:"发电机转速"};break;case"4":await te(),await ae();let c=E(!0);c[1].selectOptions=[...e.workCondMeasLocDicOptions],c[2].selectOptions=[...e.enumWorkConDataSourceOptions],D.value=[...c];break;case"5":await x(),await N(),await ee();let m=$();m[1].selectOptions=[...e.componentList],m[3].selectOptions=[...e.orientatioList],m[5].selectOptions=[...e.sVMParamType],D.value=[...m];break}me()},S=async t=>{const{tableKey:a,selectedkeys:n}=t;if(!(!t||!n||!n.length))switch(a){case"1":const l=await e.fetchDeleteVibMeasLocs({sourceData:n,targetTurbineIds:s.batchApplyData});l&&l.code===1?(K(),s.bathApplyResponse1=l.batchResults||{},b.success("删除成功")):b.error("删除失败:"+l.msg);break;case"2":const r=await e.fetchDeleteProcessMeasLocs({sourceData:n,targetTurbineIds:s.batchApplyData});r&&r.code===1?(j(),s.bathApplyResponse2=r.batchResults||{},b.success("删除成功")):b.error("删除失败:"+r.msg);break;case"3":let d={sourceData:[{WindTurbineID:i.value,MeasLocationID:n[0]}],targetTurbineIds:s.batchApplyData};const c=await e.fetchDeleteRotSpdLoc(d);c&&c.code===1?(J(),s.bathApplyResponse3=c.batchResults||{},b.success("删除成功")):b.error("删除失败:"+c.msg);break;case"4":let m=n.map(C=>({measLocationID:C,windTurbineID:i.value}));const L=await e.fetchDeleteWorkingConditionMeasBatch({sourceData:m,targetTurbineIds:s.batchApplyData});L&&L.code===1?(s.bathApplyResponse4=L.batchResults||{},z(),b.success("删除成功")):b.error("删除失败:"+L.msg);break;case"5":const f=await e.fetchBatchDeleteMeasLoc({sourceData:n,targetTurbineIds:s.batchApplyData});f&&f.code===1?(s.bathApplyResponse5=f.batchResults||{},Q(),b.success("删除成功")):b.error("删除失败:"+f.msg);break}},V=async t=>{var p,d,c,m,L;const{rowData:a,tableKey:n,title:l,operateType:r}=t;switch(w.value=r,h.value=n,B.value="编辑"+l.split("列表")[0],n){case"1":case"2":let f=[];n==1?f=G():f=q(),await x(),await N();let C=await e.fetchGetSectionList({turbineID:i.value,componentName:(p=a.devTurComponent)==null?void 0:p.componentName});f[1].selectOptions=[...e.componentList],f[3].selectOptions=[...e.orientatioList],f[2].selectOptions=C,D.value=[...f],v.value={...a,componentID:{label:(d=a.devTurComponent)==null?void 0:d.componentName,value:(c=a.devTurComponent)==null?void 0:c.componentID}};break;case"3":v.value={...a},D.value=U();break;case"4":await te(),await ae();let O=E(!0);O[1].selectOptions=[...e.workCondMeasLocDicOptions],O[2].selectOptions=[...e.enumWorkConDataSourceOptions],D.value=[...O],v.value={...a,datafrom:{label:"",value:a.fieldBusType}};break;case"5":await x(),await N(),await ee();let y=$();y[1].selectOptions=[...e.componentList],y[3].selectOptions=[...e.orientatioList],y[5].selectOptions=[...e.sVMParamType],D.value=[...y],v.value={...a,componentID:{label:(m=a.devTurComponent)==null?void 0:m.componentName,value:(L=a.devTurComponent)==null?void 0:L.componentID}};break}me()},de=async t=>{const{value:a,dataIndex:n,index:l}=t;if(n){if(n.indexOf("componentID")>-1&&a.label){let r=[...D.value];if(a.value.indexOf("newOption_")>-1){r[2].selectOptions=[{label:"无",value:"无"}],w.value=="batchAdd"?A.value.setTableFieldValue({formDataIndex:`${r[2].dataIndex}[${l}]`,tableDataIndex:r[2].dataIndex,index:l,value:"无"}):W.value.setFieldValue("sectionList","无");return}let p=await e.fetchGetSectionList({turbineID:i.value,componentName:a.label});r[2].selectOptions=p,D.value=[...r],p&&p.length>0&&(w.value=="batchAdd"?A.value.setTableFieldValue({formDataIndex:`${r[2].dataIndex}[${l}]`,tableDataIndex:r[2].dataIndex,index:l,value:p[0].value}):W.value.setFieldValue("sectionList",p[0].value))}else if(n=="measType"&&a){console.log("value",t);let r=[],p=!1,d=D.value;if(a=="oil"?r=await e.fetchGetOilParamType():a=="modbus"?p=!0:(await ee(),r=e.sVMParamType),w.value=="batchAdd"){if(t.index>=d[5].tableList.length)for(let c=d[5].tableList.length;c<=t.index;c++)d[5].tableList.push({});d[5].tableList[t.index].selectOptions=r,d[5].tableList[t.index].disabled=p,r&&A.value.setTableFieldValue({formDataIndex:`physicalType[${t.index}]`,tableDataIndex:"physicalType",index:t.index,value:r.length?r[0]:""})}}}},Ce=async t=>{var a,n,l,r;if(w.value=="editDevice"){let p={...t,componentIds:t.componentIds&&t.componentIds.length?t.componentIds.join(","):"",windTurbineID:v.value.windTurbineID,windParkId:v.value.windParkID};const d=await e.fetcheditOneDevice(p);d&&d.code===1?(pe(),t.windTurbineName!==v.value.windTurbineName&&await u.getDevTreeDatas(),await u.getDevTreeDatas(),g(),b.success("提交成功")):b.error("提交失败:"+d.msg)}else switch(h.value){case"1":case"2":let p={...t,windTurbineID:i.value,measLocationID:v.value.measLocationID,componentName:(a=t.componentID)==null?void 0:a.value,componentID:(n=t.componentID)==null?void 0:n.value},d={};h.value==1?d=await e.fetchEditVibMeasLoc(p):d=await e.fetchEditProcessMeasLoc(p),d&&d.code===1?(h.value==1?K():j(),g(),b.success("提交成功")):b.error("提交失败:"+d.msg);break;case"3":let c={...t,WindTurbineID:i.value},m={};w.value=="edit"?(c.measLocationID=v.value.measLocationID,m=await e.fetchEditRotSpdLoc({sourceData:c,targetTurbineIds:s.batchApplyData})):m=await e.fetchAddRotSpdMeaslocs({sourceData:[c],targetTurbineIds:s.batchApplyData}),m&&m.code===1?(J(),s.bathApplyResponse3=m.batchResults||{},g(),b.success("提交成功")):b.error("提交失败:"+m.msg);break;case"4":let L={windTurbineID:i.value,measLocationID:v.value.measLocationID,measLocName:v.value.measLocName,datafrom:(l=t.datafrom)==null?void 0:l.label,mDFWorkData:(r=t.mDFWorkData)==null?void 0:r.label};const f=await e.fetchEditWorkingConditionMeas(L);f&&f.code===1?(z(),g(),b.success("提交成功")):b.error("提交失败:"+f.msg);break}},Te=async t=>{switch(h.value){case"1":case"2":let a={windTurbineID:i.value,measLocationID:""},l=le(t,a,{componentID:{label:"componentName",value:"componentID"}});l=l.map(y=>(y.componentID.indexOf("newOption_")>-1&&(y.componentID=""),y));let r={};h.value==1?r=await e.fetchAddVibMeasLocs({sourceData:l,targetTurbineIds:s.batchApplyData}):r=await e.fetchAddProcessMeasLocs({sourceData:l,targetTurbineIds:s.batchApplyData}),r&&r.code===1?(h.value==1?(K(),s.bathApplyResponse1=r.batchResults||{}):(j(),s.bathApplyResponse2=r.batchResults||{}),g(),b.success("提交成功")):b.error("提交失败:"+r.msg);break;case"4":let p={windTurbineID:i.value,measLocationID:""},c=le(t,p,{mDFWorkData:{label:"MDFWorkData"},datafrom:{label:"datafrom"}}),m=await e.fetchAddWorkingConditionMeaslocs({sourceData:c,targetTurbineIds:s.batchApplyData});m&&m.code===1?(z(),s.bathApplyResponse4=m.batchResults||{},g(),b.success("提交成功")):b.error("提交失败:"+m.msg);break;case"5":let L={windTurbineID:i.value,measLocationID:""},C=le(t,L,{componentID:{label:"componentName",value:"componentID"}});C=C.map(y=>(y.componentID.indexOf("newOption_")>-1&&(y.componentID=""),y));for(let y=0;y<C.length;y++)C[y].physicalType=C[y].physicalType||"";let O=await e.fetchAddModbusMeasloc({sourceData:C,targetTurbineIds:s.batchApplyData});O&&O.code===1?(Q(),s.bathApplyResponse5=O.batchResults||{},g(),b.success("提交成功")):b.error("提交失败:"+O.msg);break}},ge=t=>{var r;const{index:a}=t,n=(r=A.value)==null?void 0:r.getTableFieldsValue();if(!n||!Object.keys(n).length||a==null)return;let l="";h.value==1||h.value==2||h.value==5?(l+=n[`componentID[${a}]`]&&n[`componentID[${a}]`].label?n[`componentID[${a}]`].label:"",l+=n[`sectionName[${a}]`]||"",l+=n[`orientation[${a}]`]||"",h.value==5&&(l+=n[`physicalType[${a}]`]&&n[`physicalType[${a}]`].label?n[`physicalType[${a}]`].label:"")):h.value==4&&(l=n[`mDFWorkData[${a}]`]&&n[`mDFWorkData[${a}]`].label?n[`mDFWorkData[${a}]`].label:""),A.value.setTableFieldValue({formDataIndex:`measLocName[${a}]`,tableDataIndex:"measLocName",index:a,value:l}),l&&l!==""&&A.value.clearValidate(`measLocName[${a}]`)},ke=()=>{var n;const t=(n=W.value)==null?void 0:n.getFieldsValue();let a="";h.value==1||h.value==2?(a+=t.componentID&&t.componentID.label?t.componentID.label:"",a+=t.sectionName||"",a+=t.orientation||""):h.value==4&&(a=t.mDFWorkData&&t.mDFWorkData.label?t.mDFWorkData.label:""),W.value.setFieldValue("measLocName",a)},me=()=>{Z.value=!0},g=t=>{Z.value=!1,v.value={},D.value=[],w.value="",B.value="",h.value=""},Me=async t=>{t.type&&t.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${t.key}`]={}):(s.batchApplyData=t.turbines,s.batchApplyKey=t.key)};return fe("deviceId",i),fe("bathApplySubmit",Me),(t,a)=>{const n=vt,l=Dt,r=Lt,p=wt,d=yt;return M(),se(d,{spinning:X.value,size:"large"},{default:k(()=>[(M(),R("div",{key:i.value},[T(Ae,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{rightButtons:k(()=>a[1]||(a[1]=[])),content:k(()=>[be("div",Ct,[T(l,{column:4,size:"small"},{default:k(()=>[(M(!0),R(pt,null,dt(ue.value,c=>(M(),se(n,{label:c.label,key:c.label},{default:k(()=>[ne(mt(c.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),be("div",null,[T(P,{ref:"table",size:"default","table-key":"1","table-title":"振动测量位置列表","table-columns":s.table1Columns,"table-operate":["delete","add","batchDelete","batchAdd"],"record-key":"measLocationID",borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-datas":s.tableDatas1,onAddRow:_,onDeleteRow:S,onEditRow:V,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"]),T(P,{ref:"table",size:"default","table-key":"2","table-title":"电流电压测量位置列表","table-columns":s.table2Columns,bathApplyResponse:s.bathApplyResponse2,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:s.batchApplyKey=="2","table-datas":s.tableDatas2,onAddRow:_,onDeleteRow:S,onEditRow:V,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),T(P,{ref:"table",size:"default","table-key":"3","table-title":"转速测量位置列表","table-columns":s.table3Columns,bathApplyResponse:s.bathApplyResponse3,"table-operate":["batchDelete",s.tableDatas3.length?"":"add","delete","edit"],borderLight:s.batchApplyKey=="3","record-key":"measLocationID","table-datas":s.tableDatas3,onAddRow:_,onDeleteRow:S,onEditRow:V,noPagination:!0},null,8,["table-columns","bathApplyResponse","table-operate","borderLight","table-datas"]),T(P,{ref:"table",size:"default","table-key":"4","table-title":"工况测量位置列表","table-columns":s.table4Columns,bathApplyResponse:s.bathApplyResponse4,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:s.batchApplyKey=="4","table-datas":s.tableDatas4,onAddRow:_,onDeleteRow:S,onEditRow:V,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),T(P,{ref:"table",size:"default","table-key":"5","table-title":"Modbus设备测量位置列表","table-columns":s.table5Columns,bathApplyResponse:s.bathApplyResponse5,"table-operate":["batchDelete","batchAdd","delete","batchAdd"],"record-key":"measLocationID",borderLight:s.batchApplyKey=="5","table-datas":s.tableDatas5,onAddRow:_,onDeleteRow:S,onEditRow:V,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"])]),T(p,{maskClosable:!1,width:Ie.value,open:Z.value,title:B.value,footer:"",destroyOnClose:!0,onCancel:g},{default:k(()=>[w.value==="add"||w.value==="edit"||w.value==="editDevice"?(M(),R("div",Tt,[T(Re,{ref_key:"formModalRef",ref:W,titleCol:D.value,initFormData:v.value,onChange:de,onSubmit:Ce,onCancelForm:g},{otherInfo:k(({formModel:c})=>[h.value=="1"||h.value=="2"||h.value=="4"?(M(),R("div",gt,[T(r,{onClick:a[0]||(a[0]=m=>ke()),class:"autoName"},{default:k(()=>a[2]||(a[2]=[ne("自动",-1)])),_:1,__:[2]})])):he("",!0)]),_:1},8,["titleCol","initFormData"])])):w.value==="batchAdd"?(M(),se(We,{key:1,ref_key:"modalTableFormRef",ref:A,size:"default","table-key":"0","table-columns":D.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":De.value,"noRepeat-Keys":ve.value,onSubmit:Te,onHangeTableFormChange:de,onCancel:g},{afterContent:k(({column:c,record:m,text:L,index:f})=>[c.dataIndex==="measLocName"?(M(),R("div",kt,[T(r,{onClick:C=>ge({record:m,index:f}),class:"autoName"},{default:k(()=>a[3]||(a[3]=[ne("自动",-1)])),_:2,__:[3]},1032,["onClick"])])):he("",!0)]),_:1},8,["table-columns","noCopyUp-keys","noRepeat-Keys"])):(M(),R("div",Mt))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}},Ut=ft(At,[["__scopeId","data-v-f724a5fe"]]);export{Ut as default};
