﻿using System;
using System.Collections.Generic;
using System.Linq;
using CMSFramework.BusinessEntity;
using CMSFramework.BusinessEntity.SVM;
using CMSFramework.EigenValueDef;

namespace WTCMSLive.BusinessModel
{
    public class EigenValueManage
    {
        #region 特征值类型列表写入dll
        /// <summary>
        /// 根据测量位置，获取全部特征值列表
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="measLocID">测量位置ID</param>
        /// <returns></returns>
        public static List<EigenValueData_Vib> GetFBEigenValueListByMeasLoc(string turbineID, string measLocID, string waveDefID)
        {
            //①获取测量位置下公共特征值列表
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            //获取测量位置下波形定义
            List<WaveDefinition> waveList = MeasDefinitionManagement.GetWaveDefByMeasLoc(measLocID);
            //②获取波形定义对应的特征值列表
            for (int i = 0; i < waveList.Count; i++)
            {
                if (waveDefID != string.Empty && waveDefID != waveList[i].WaveDefinitionID.ToString())
                    continue;
                //时域
                if (waveList[i].WaveFormType == EnumWaveFormType.WDF_Time)
                {
                    //时域波形处理逻辑
                    eigenValueList.AddRange(GetPublicEigenValueList(turbineID, measLocID));
                    eigenValueList.AddRange(GetWDFTimeList(turbineID, waveList[i].WaveDefinitionID.ToString(), eigenValueList));
                }
                //包络波形处理逻辑
                if (waveList[i].WaveFormType == EnumWaveFormType.WDF_Envelope)
                {
                    eigenValueList.AddRange(GetEnvlopeEigenValueList(turbineID, waveList[i].WaveDefinitionID.ToString()));
                }
            }
            eigenValueList.ForEach(item => item.MeasLocationID = measLocID);
            eigenValueList.ForEach(item => item.EigenValueID = measLocID + "&&" + item.EigenValueCode);
            return eigenValueList;
        }

        /// <summary>
        /// 获取时域波形特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="waveDefID"></param>
        /// <param name="baseList">机组相关特征值列表</param>
        /// <returns></returns>
        private static List<EigenValueData_Vib> GetWDFTimeList(string turbineID, string waveDefID, List<EigenValueData_Vib> baseList)
        {
            //获取时域波形
            WaveDef_Time wavedef = MeasDefinitionManagement.GetWaveDefById_Time(turbineID, waveDefID);
            //取得波形上下限
            float upperFre = wavedef.UpperLimitFreqency;
            float lowFre = wavedef.LowerLimitFreqency;
            return GetWDFTimeList(lowFre, upperFre, baseList);
        }

        private static List<EigenValueData_Vib> GetWDFTimeList(float lowFre, float upperFre, List<EigenValueData_Vib> baseList)
        {
            string eigenValueB = lowFre.ToString() + '-' + upperFre.ToString();
            string eigenValueName = string.Empty;
            if (upperFre >= 1000)
            {
                eigenValueName = lowFre.ToString() + '-' + (upperFre / 1000).ToString() + "K";
            }
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            bool hasSameEigenValue = false;
            //查看时域带宽有效值是否重复<RMS>
            if (baseList != null)
            {
                string freEigenValue = eigenValueB + "_BRMS";
                foreach (EigenValueData_Vib item in baseList)
                {
                    if (item.EigenValueCode == freEigenValue)
                    {
                        hasSameEigenValue = true;
                        break;
                    }
                }
            }
            //没有重复时，添加对应RMS
            if (hasSameEigenValue == false)
            {
                EigenValueData_Vib BRMS = new EigenValueData_Vib();
                BRMS.EigenValueCode = eigenValueB + "_RMS";
                //BRMS.EigenValueName = eigenValueName + "HZ 有效值";
                //BRMS.FreqBand_Max = upperFre;
                //BRMS.FreqBand_Min = lowFre;
                eigenValueList.Add(BRMS);
            }
            //构建时域波形对应列表

            //峰值
            EigenValueData_Vib PK = new EigenValueData_Vib();
            PK.EigenValueCode = eigenValueB + "_PK";
            //PK.EigenValueName = eigenValueName + "HZ 峰值";
            //PK.FreqBand_Max = upperFre;
            //PK.FreqBand_Min = lowFre;
            eigenValueList.Add(PK);
            //峰峰值
            EigenValueData_Vib PPK = new EigenValueData_Vib();
            PPK.EigenValueCode = eigenValueB + "_PPK";
            //PPK.EigenValueName = eigenValueName + "HZ 峰峰值";
            //PPK.FreqBand_Max = upperFre;
            //PPK.FreqBand_Min = lowFre;
            eigenValueList.Add(PPK);
            //峭度指标
            EigenValueData_Vib KTS = new EigenValueData_Vib();
            KTS.EigenValueCode = eigenValueB + "_KTS";
            //KTS.EigenValueName = eigenValueName + "HZ 峭度指标";
            //KTS.FreqBand_Max = upperFre;
            //KTS.FreqBand_Min = lowFre;
            eigenValueList.Add(KTS);
            //峰值指标
            EigenValueData_Vib CF = new EigenValueData_Vib();
            CF.EigenValueCode = eigenValueB + "_CF";
            //CF.EigenValueName = eigenValueName + "HZ 峰值指标";
            //CF.FreqBand_Max = upperFre;
            //CF.FreqBand_Min = lowFre;
            eigenValueList.Add(CF);
            //偏斜度
            EigenValueData_Vib SK = new EigenValueData_Vib();
            SK.EigenValueCode = eigenValueB + "_SK";
            //SK.EigenValueName = eigenValueName + "HZ 偏斜度";
            //SK.FreqBand_Max = upperFre;
            //SK.FreqBand_Min = lowFre;
            eigenValueList.Add(SK);
            //裕度指标
            EigenValueData_Vib maring = new EigenValueData_Vib();
            maring.EigenValueCode = eigenValueB + "_LF";
            //maring.EigenValueName = eigenValueName + "HZ 裕度";
            //maring.FreqBand_Max = upperFre;
            //maring.FreqBand_Min = lowFre;
            eigenValueList.Add(maring);

            // add by steel @ 2015-12-9
            //速度有效值
            EigenValueData_Vib VRMS = new EigenValueData_Vib();
            VRMS.EigenValueCode = eigenValueB + "_VRMS";
            //VRMS.EigenValueName = eigenValueName + "HZ 速度有效值";
            //VRMS.FreqBand_Max = upperFre;
            //VRMS.FreqBand_Min = lowFre;
            eigenValueList.Add(VRMS);

            return eigenValueList;
        }

        /// <summary>
        /// 获取包络波形特征值
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="waveDefID"></param>
        /// <returns></returns>
        public static List<EigenValueData_Vib> GetEnvlopeEigenValueList(string turbineID, string waveDefID)
        {
            WaveDef_Envlope waveEnvlope = MeasDefinitionManagement.GetWaveDefById_Envlope(turbineID, waveDefID);
            return GetEnvlopeEigenValueList(turbineID, waveEnvlope.EnvBandWidth);
        }

        public static List<EigenValueData_Vib> GetEnvlopeEigenValueList(string turbineID, float envBandWidth)
        {
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            string band = string.Empty;
            if (envBandWidth >= 1000)
            {
                band = (envBandWidth / 1000).ToString() + "K";
            }
            else
            {
                band = envBandWidth.ToString();
            }

            //包络峰值因数
            EigenValueData_Vib ECF = new EigenValueData_Vib();
            ECF.EigenValueCode = "0-" + envBandWidth.ToString() + "_ECF";
            //ECF.EigenValueName = "0-" + band + "_峰值因数";
            //ECF.FreqBand_Max = envBandWidth;
            eigenValueList.Add(ECF);
            //包络峭度
            EigenValueData_Vib EKTS = new EigenValueData_Vib();
            EKTS.EigenValueCode = "0-" + envBandWidth.ToString() + "_EKTS";
            //EKTS.EigenValueName = "0-" + band + "_峭度指标";
            //EKTS.FreqBand_Max = envBandWidth;
            eigenValueList.Add(EKTS);
            //脉冲指标
            EigenValueData_Vib impulse = new EigenValueData_Vib();
            impulse.EigenValueCode = "0-" + envBandWidth.ToString() + "_EIF";
            //impulse.EigenValueName = "0-" + band + "_脉冲指标";
            //impulse.FreqBand_Max = envBandWidth;
            //impulse.FreqBand_Min = 0;
            eigenValueList.Add(impulse);
            //裕度指标
            EigenValueData_Vib maring = new EigenValueData_Vib();
            maring.EigenValueCode = "0-" + envBandWidth.ToString() + "_ELF";
            //maring.EigenValueName = "0-" + band + "_裕度指标";
            //maring.FreqBand_Max = envBandWidth;
            //maring.FreqBand_Min = 0;
            eigenValueList.Add(maring);
            //波形指标
            EigenValueData_Vib esf = new EigenValueData_Vib();
            esf.EigenValueCode = "0-" + envBandWidth.ToString() + "_ESF";
            //esf.EigenValueName = "0-" + band + "_波形指标";
            //esf.FreqBand_Max = envBandWidth;
            //esf.FreqBand_Min = 0;
            eigenValueList.Add(esf);
            return eigenValueList;
        }
        /// <summary>
        /// 频带监测特征值
        /// </summary>
        /// <returns></returns>
        public static List<EigenValueData_Vib> GetPublicEigenValueList(string turbineID, string measLocID)
        {
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            //return eigenValueList;
            //获取部件类型            
            MeasLoc_Vib measLoc_Vib = DevTreeManagement.GetVibMeasLocByID(measLocID);
            eigenValueList = GetEigenValueFromConfig(measLoc_Vib);
            return eigenValueList;

            #region 已经移除 频带监测特征值
            //根据部件获取特征值列表
            //switch (measLoc_Vib.ComponentName)
            //{
            //    case "主轴承":
            //        //VDI3834
            //        EigenValue_FreqBand BRMS20 = new EigenValue_FreqBand();
            //        BRMS20.EigenValueCode = "0.1-10_BRMS";
            //        BRMS20.EigenValueName = "0.1~10Hz 频带有效值";
            //        BRMS20.FreqBand_Max = 10;
            //        BRMS20.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS20);
            //        //WRD
            //        EigenValue_FreqBand BRMS1 = new EigenValue_FreqBand();
            //        BRMS1.EigenValueCode = "50-300_BRMS";
            //        BRMS1.EigenValueName = "50~300Hz 频带有效值";
            //        BRMS1.FreqBand_Max = 300;
            //        BRMS1.FreqBand_Min = 50;
            //        eigenValueList.Add(BRMS1);
            //        EigenValue_FreqBand BRMS2 = new EigenValue_FreqBand();
            //        BRMS2.EigenValueCode = "10-50_VRMS";
            //        BRMS2.EigenValueName = "10~50KHz 速度有效值";
            //        BRMS2.FreqBand_Max = 50;
            //        BRMS2.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS2);
            //        EigenValue_FreqBand BRMS3 = new EigenValue_FreqBand();
            //        BRMS3.EigenValueCode = "50-300_VRMS";
            //        BRMS3.EigenValueName = "50~300Hz 速度有效值";
            //        BRMS3.FreqBand_Max = 300;
            //        BRMS3.FreqBand_Min = 50;
            //        eigenValueList.Add(BRMS3);
            //        EigenValue_FreqBand BRMS4 = new EigenValue_FreqBand();
            //        BRMS4.EigenValueCode = "300-1000_VRMS";
            //        BRMS4.EigenValueName = "300~1KHz 速度有效值";
            //        BRMS4.FreqBand_Max = 1000;
            //        BRMS4.FreqBand_Min = 300;
            //        eigenValueList.Add(BRMS4);
            //        EigenValue_FreqBand BRMS26 = new EigenValue_FreqBand();
            //        BRMS26.EigenValueCode = "10-1000_VRMS";
            //        BRMS26.EigenValueName = "10~1KHz 速度有效值";
            //        BRMS26.FreqBand_Max = 1000;
            //        BRMS26.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS26);
            //        break;
            //    case "齿轮箱":
            //        //VDI3834
            //        EigenValue_FreqBand BRMS201 = new EigenValue_FreqBand();
            //        BRMS201.EigenValueCode = "0.1-10_BRMS";
            //        BRMS201.EigenValueName = "0.1~10Hz 频带有效值";
            //        BRMS201.FreqBand_Max = 10;
            //        BRMS201.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS201);
            //        EigenValue_FreqBand BRMS22 = new EigenValue_FreqBand();
            //        BRMS22.EigenValueCode = "10-2000_BRMS";
            //        BRMS22.EigenValueName = "10~2KHz 频带有效值";
            //        BRMS22.FreqBand_Max = 2000;
            //        BRMS22.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS22);
            //        EigenValue_FreqBand BRMS30 = new EigenValue_FreqBand();
            //        BRMS30.EigenValueCode = "10-1000_VRMS";
            //        BRMS30.EigenValueName = "10~1KHz 速度有效值";
            //        BRMS30.FreqBand_Max = 1000;
            //        BRMS30.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS30);
            //        //WRD
            //        EigenValue_FreqBand BRMS5 = new EigenValue_FreqBand();
            //        BRMS5.EigenValueCode = "10-50_VRMS";
            //        BRMS5.EigenValueName = "10~50Hz 速度有效值";
            //        BRMS5.FreqBand_Max = 50;
            //        BRMS5.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS5);
            //        EigenValue_FreqBand BRMS6 = new EigenValue_FreqBand();
            //        BRMS6.EigenValueCode = "50-300_VRMS";
            //        BRMS6.EigenValueName = "50~300Hz 速度有效值";
            //        BRMS6.FreqBand_Max = 300;
            //        BRMS6.FreqBand_Min = 50;
            //        eigenValueList.Add(BRMS6);
            //        EigenValue_FreqBand BRMS7 = new EigenValue_FreqBand();
            //        BRMS7.EigenValueCode = "300-1000_VRMS";
            //        BRMS7.EigenValueName = "300~1KHz 速度有效值";
            //        BRMS7.FreqBand_Max = 1000;
            //        BRMS7.FreqBand_Min = 300;
            //        eigenValueList.Add(BRMS7);
            //        break;
            //    case "发电机":
            //        //VID3834
            //        EigenValue_FreqBand BRMS24 = new EigenValue_FreqBand();
            //        BRMS24.EigenValueCode = "10-5000_BRMS";
            //        BRMS24.EigenValueName = "10~5KHz 频带有效值";
            //        BRMS24.FreqBand_Max = 50000;
            //        BRMS24.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS24);
            //        EigenValue_FreqBand BRMS261 = new EigenValue_FreqBand();
            //        BRMS261.EigenValueCode = "10-1000_VRMS";
            //        BRMS261.EigenValueName = "10~1KHz 速度有效值";
            //        BRMS261.FreqBand_Max = 1000;
            //        BRMS261.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS261);
            //        //WRD
            //        //2个转频在配置文件中
            //        eigenValueList.AddRange(GetEigenValueFromConfig(measLoc_Vib.ComponentName));
            //        EigenValue_FreqBand BRMS8 = new EigenValue_FreqBand();
            //        BRMS8.EigenValueCode = "1000-10000_RMS";
            //        BRMS8.EigenValueName = "1K~10KHz 频带有效值";
            //        BRMS8.FreqBand_Max = 10000;
            //        BRMS8.FreqBand_Min = 1000;
            //        eigenValueList.Add(BRMS8);
            //        EigenValue_FreqBand BRMS9 = new EigenValue_FreqBand();
            //        BRMS9.EigenValueCode = "10-250_VRMS";
            //        BRMS9.EigenValueName = "10~250Hz 速度有效值";
            //        BRMS9.FreqBand_Max = 250;
            //        BRMS9.FreqBand_Min = 10;
            //        eigenValueList.Add(BRMS9);
            //        EigenValue_FreqBand BRMS10 = new EigenValue_FreqBand();
            //        BRMS10.EigenValueCode = "250-1000_VRMS";
            //        BRMS10.EigenValueName = "250~1KHz 速度有效值";
            //        BRMS10.FreqBand_Max = 1000;
            //        BRMS10.FreqBand_Min = 250;
            //        eigenValueList.Add(BRMS10);
            //        break;
            //    case "塔筒":
            //        //VDI3834
            //        EigenValue_FreqBand BRMS25 = new EigenValue_FreqBand();
            //        BRMS25.EigenValueCode = "0.1-10_BRMS";
            //        BRMS25.EigenValueName = "0.1~10Hz 频带有效值";
            //        BRMS25.FreqBand_Max = 10;
            //        BRMS25.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS25);
            //        EigenValue_FreqBand BRMS28 = new EigenValue_FreqBand();
            //        BRMS28.EigenValueCode = "0.1-10_VRMS";
            //        BRMS28.EigenValueName = "0.1~10Hz 速度有效值";
            //        BRMS28.FreqBand_Max = 10;
            //        BRMS28.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS28);
            //        break;
            //    case "机舱":
            //        EigenValue_FreqBand BRMS29 = new EigenValue_FreqBand();
            //        BRMS29.EigenValueCode = "0.1-10_BRMS";
            //        BRMS29.EigenValueName = "0.1~10Hz 频带有效值";
            //        BRMS29.FreqBand_Max = 10;
            //        BRMS29.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS29);
            //        EigenValue_FreqBand BRMS27 = new EigenValue_FreqBand();
            //        BRMS27.EigenValueCode = "0.1-10_VRMS";
            //        BRMS27.EigenValueName = "0.1~10Hz 速度有效值";
            //        BRMS27.FreqBand_Max = 10;
            //        BRMS27.FreqBand_Min = 0.1;
            //        eigenValueList.Add(BRMS27);
            //        break;
            //    default :
            //        break;
            //}
            //
            //return eigenValueList;
            #endregion
        }

        public static List<EigenValueData_Vib> GetPublicEigenValueList_Modbus(string turbineID, string measLocID)
        {
            List<EigenValueData_Vib> eigenValueList = new List<EigenValueData_Vib>();
            //return eigenValueList;
            //获取部件类型            
            MeasLoc_Vib measLoc_Vib = new MeasLoc_Vib() ;
            MeasLoc_Modbus measloc = SVMManagement.GetMeasLoc_ModbusListByMeasdID(measLocID);
            if(measloc != null)
            {
                measLoc_Vib.ComponentID = measloc.ComponentID;
                measLoc_Vib.Orientation = measloc.Orientation;
                measLoc_Vib.SectionName = measloc.SectionName;
                measLoc_Vib.MeasLocName = measloc.MeasLocName;
                measLoc_Vib.MeasLocationID = measloc.MeasLocationID;
                measLoc_Vib.DevTurComponent = DevTreeManagement.GetTurbComponent(measloc.ComponentID) ;
                eigenValueList = GetEigenValueFromConfig(measLoc_Vib);

            }

            return eigenValueList;
        }
        /// <summary>
         /// 晃度特征值[无波形相关]
         /// </summary>
         /// <returns></returns>
        public static List<EigenValueData_SVM> GetSVMPublicEigenValueList(string turbineID, string measLocID)
        {
            List<EigenValueData_SVM> eigenValueList = new List<EigenValueData_SVM>();
            //根据晃度仪测量位置ID
            MeasLoc_SVM _measLoc = SVMManagement.GetMeasloc_SVMByMeasLocID(measLocID);
            if (_measLoc==null) {
                return eigenValueList = null;
            }
            //根据测量位置类型，返回对应列表。
            switch (_measLoc.ParamType)
            {
                case EnumSVMParamType.Pitch://俯仰角
                    // 固有频率
                    //EigenValueData_SVM p1 = new EigenValueData_SVM();
                    //p1.EigenValueID = "NFPitch";
                    //p1.EigenValueType = EnumSVMEigenValueType.NFPitch;
                    //p1.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(p1);

                    // 位移平均值
                    EigenValueData_SVM p2 = new EigenValueData_SVM();
                    p2.EigenValueID = "TDBAVG";
                    //p2.EigenValueType = EnumSVMEigenValueType.TDBAVG;
                    p2.EigenValueType = EnumSVMEigenValueType.TDBAVG;
                    p2.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(p2);

                    break;
                case EnumSVMParamType.Roll://横滚角

                    //// 固有频率
                    //EigenValueData_SVM ba1 = new EigenValueData_SVM();
                    //ba1.EigenValueID = "NFPitch";
                    //ba1.EigenValueType = EnumSVMEigenValueType.NFPitch;
                    //ba1.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(ba1);
                    break;
                case EnumSVMParamType.Vertical://垂直加速度
                    //EigenValueData_SVM VA = new EigenValueData_SVM();
                    //VA.EigenValueID = "VA" + '_' + _measLoc.ParamType.ToString();
                    //VA.EigenValueType = EnumSVMEigenValueType.VerticalAcceleration;
                    //VA.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(VA);
                    EigenValueData_SVM ba1v = new EigenValueData_SVM();
                    ba1v.EigenValueID = "NFRoll";
                    //ba1v.EigenValueType = EnumSVMEigenValueType.NFRoll;
                    ba1v.EigenValueType = EnumSVMEigenValueType.NFRoll;
                    ba1v.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(ba1v);
                    break;
                case EnumSVMParamType.Horizontal://水平加速度
                    //EigenValueData_SVM LA = new EigenValueData_SVM();
                    //LA.EigenValueID = "LA" + '_' + _measLoc.ParamType.ToString();
                    //LA.EigenValueType = EnumSVMEigenValueType.LateralAcceleration;
                    //LA.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(LA);
                    //EigenValueData_SVM TNF = new EigenValueData_SVM();
                    //TNF.EigenValueID = "TNF" + '_' + _measLoc.ParamType.ToString();
                    //TNF.EigenValueType = EnumSVMEigenValueType.NaturalFrequency;
                    //TNF.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(TNF);
                    //EigenValueData_SVM TNFMA = new EigenValueData_SVM();
                    //TNFMA.EigenValueID = "TNFMA" + '_' + _measLoc.ParamType.ToString();
                    //TNFMA.EigenValueType = EnumSVMEigenValueType.NFAmplitude;
                    //TNFMA.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(TNFMA);
                    EigenValueData_SVM ba1 = new EigenValueData_SVM();
                    ba1.EigenValueID = "NFPitch";
                    //ba1.EigenValueType = EnumSVMEigenValueType.NFPitch;
                    ba1.EigenValueType = EnumSVMEigenValueType.NFPitch;
                    ba1.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(ba1);
                    break;
                case EnumSVMParamType.Axisl://轴向加速度
                    //EigenValueData_SVM AA = new EigenValueData_SVM();
                    //AA.EigenValueID = "AA" + '_' + _measLoc.ParamType.ToString();
                    //AA.EigenValueType = EnumSVMEigenValueType.AxialAcceleration;
                    //AA.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(AA);

                    EigenValueData_SVM aa1 = new EigenValueData_SVM();
                    aa1.EigenValueID = "NFRoll";
                    //aa1.EigenValueType = EnumSVMEigenValueType.NFRoll;
                    aa1.EigenValueType = EnumSVMEigenValueType.NFRoll;
                    aa1.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(aa1);
                    break;
                case EnumSVMParamType.YInclination:
                    //EigenValueData_SVM Actrual = new EigenValueData_SVM();
                    //Actrual.EigenValueID = "Actrual" + '_' + _measLoc.ParamType.ToString();
                    //Actrual.EigenValueType = EnumSVMEigenValueType.LateralAcceleration;
                    //Actrual.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(Actrual);
                    //EigenValueData_SVM NFRoll = new EigenValueData_SVM();
                    //NFRoll.EigenValueID = "NFRoll" + '_' + _measLoc.ParamType.ToString();
                    //NFRoll.EigenValueType = EnumSVMEigenValueType.NaturalFrequency;
                    //NFRoll.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(NFRoll);
                    //EigenValueData_SVM NFPitch = new EigenValueData_SVM();
                    //NFPitch.EigenValueID = "NFPitch" + '_' + _measLoc.ParamType.ToString();
                    //NFPitch.EigenValueType = EnumSVMEigenValueType.NFAmplitude;
                    //NFPitch.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(NFPitch);
                    //EigenValueData_SVM TDBAVG = new EigenValueData_SVM();
                    //NFPitch.EigenValueID = "TDBAVG" + '_' + _measLoc.ParamType.ToString();
                    //NFPitch.EigenValueType = EnumSVMEigenValueType.NFAmplitude;
                    //NFPitch.WindTurbineID = _measLoc.WindTurbineID;
                    //eigenValueList.Add(TDBAVG);
                    // 真实角度
                    EigenValueData_SVM y1 = new EigenValueData_SVM();
                    y1.EigenValueID = "Actrual";
                    y1.EigenValueType = EnumSVMEigenValueType.Actrual;
                    //y1.EigenValueType = EnumSVMEigenValueType.ACCRMS;
                    y1.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(y1);
                    break;
            }
            return eigenValueList;
        }

        /// <summary>
        /// 转换特征值Name
        /// </summary>
        /// <param name="eigenValueCode"></param>
        /// <returns>下限带宽-上限带宽</returns>
        public static string GetFreBandByCode(string eigenValueCode)
        {
            //var evCode = new string[] { "GAP", "PB", "ICEA", "TDR", "BSC", "TDF" };
            
            ////if (eigenValueCode.StartsWith("SPV"))
            //if (evCode.Any(code => eigenValueCode.StartsWith(code)))
            //{
            //    return eigenValueCode;
            //}
            return EVNameUtility.GetEVNameByEVCode(eigenValueCode);
        }

        public static string GetAMSFreBandByCode(string eigenValueCode)
        {
            return CMSFramework.EigenValueDef.EVAMSNameUtility.GetEVNameByEVCode(eigenValueCode);
        }
        /// <summary>
        /// 从配置文件中获取特征值列表
        /// </summary>
        /// <returns></returns>
        public static List<EigenValueData_Vib> GetEigenValueFromConfig(MeasLoc_Vib _measLoc)
        {
            //如果已经读取过特征值配置文件，不再读取
            EigenValue_ConfigFile configFile = new EigenValue_ConfigFile();
            EigenValue_ConfigFile.GetEigenValueConfigFile();
            List<EigenValue_ConfigItem> eigenvalueList = configFile.GetEigenValueList();

            //类型转换
            List<EigenValueData_Vib> _freEigenValueList = new List<EigenValueData_Vib>();
            foreach (EigenValue_ConfigItem eigenV in eigenvalueList)
            {
                //判断配置数据有无方向,有方向不等时返回。为空或null时或相等继续
                if (!string.IsNullOrWhiteSpace(eigenV.Orientation) && _measLoc.Orientation != eigenV.Orientation)
                    continue;
                //判断配置数据有无截面,有方向不等时返回。为空或null时或相等继续
                if (!string.IsNullOrWhiteSpace(eigenV.SectionName) && _measLoc.SectionName != eigenV.SectionName)
                    continue;
                //部件相同，以及截面和方向配置通过。添加入此测量位置的特征值列表中。
                if (_measLoc.DevTurComponent.ComponentName == eigenV.ComponentName)
                {
                    EigenValueData_Vib frebandValue = new EigenValueData_Vib();
                    frebandValue.EigenValueCode = eigenV.EigenValueCode;
                    frebandValue.WindTurbineID = _measLoc.WindTurbineID;
                    frebandValue.MeasLocationID = _measLoc.MeasLocationID;
                    frebandValue.EngUnitName = eigenV.EigenValueName;
                    
                    _freEigenValueList.Add(frebandValue);
                }
            }
            return _freEigenValueList;
        }

        public static List<EigenValueData_Vib> SortCMSEigenValue(List<EigenValueData_Vib> dataList)
        {
            if (dataList.Count == 0)
            {
                return dataList;
            }
            List<EigenValueData_Vib> sortData = new List<EigenValueData_Vib>();
            /*Dictionary<string, string> dic = CMSFramework.EigenValueDef.EVAMSNameUtility.GetAMSEigenValue();
            foreach (var eigen in dic)
            {
                // 0-2000_CF
                var list = dataList.FindAll(item => item.EigenValueCode.Split('_')[1] == eigen.Key);
                if (list.Count > 0)
                    sortData.AddRange(list);
            }
            return sortData;*/
            List<EigenValueData_Vib> sortDataIndex = GetPublicEigenValueList(dataList[0].WindTurbineID, dataList[0].MeasLocationID);
            //List<EigenValueData_Vib> sortData = new List<EigenValueData_Vib>();
            //以配置列表查询值
            for (int i = 0; i < sortDataIndex.Count; i++)
            {
                foreach (EigenValueData_Vib freBand in dataList)
                {
                    if (freBand.EigenValueCode == sortDataIndex[i].EigenValueCode)
                    {
                        sortData.Add(freBand);
                        dataList.Remove(freBand);
                        break;
                    }
                }
            }
            //没有在配置列表中的数据插到尾巴
            dataList.ForEach(item => item.EngUnitName = GetOrderIndexByCode(item.EigenValueCode));
            sortData.AddRange(dataList.OrderBy(item => item.EngUnitName));
            return sortData;
        }

        public static List<EigenValueData_Vib> SortEigenValue(List<EigenValueData_Vib> dataList)
        {
            if (dataList.Count == 0)
            {
                return dataList;
            }
            List<EigenValueData_Vib> sortData = new List<EigenValueData_Vib>();
            Dictionary<string, string> dic = CMSFramework.EigenValueDef.EVAMSNameUtility.GetAMSEigenValue();
            foreach (var eigen in dic)
            {
                // 0-2000_CF
                var list = dataList.FindAll(item => item.EigenValueCode.Split('_')[1] == eigen.Key);
                if(list.Count>0)
                    sortData.AddRange(list);
            }
            return sortData;
            List<EigenValueData_Vib> sortDataIndex = GetPublicEigenValueList(dataList[0].WindTurbineID, dataList[0].MeasLocationID);
            //List<EigenValueData_Vib> sortData = new List<EigenValueData_Vib>();
            //以配置列表查询值
            for (int i = 0; i < sortDataIndex.Count; i++)
            {
                foreach (EigenValueData_Vib freBand in dataList)
                {
                    if (freBand.EigenValueCode == sortDataIndex[i].EigenValueCode)
                    {
                        sortData.Add(freBand);
                        dataList.Remove(freBand);
                        break;
                    }
                }
            }
            //没有在配置列表中的数据插到尾巴
            dataList.ForEach(item => item.EngUnitName = GetOrderIndexByCode(item.EigenValueCode));

            sortData.AddRange(dataList.OrderBy(item => item.EngUnitName));

            return sortData;
        }
        //非频带排序顺序设置
        private static string GetOrderIndexByCode(string eigenValueCode)
        {
            string code = string.Empty;
            if (eigenValueCode.Contains('_'))
            {
                code = eigenValueCode.Split('_')[1];
            }
            else
            {
                code = eigenValueCode;
            }
            string order = string.Empty;
            switch (code)
            {
                case "PK":
                case "gPkm":
                    order = "1";
                    break;
                case "PPK":
                    order = "2";
                    break;
                case "RMS":
                case "BRMS":
                    order = "3";
                    break;
                case "KTS":
                    order = "4";
                    break;
                case "SK":
                    order = "5";
                    break;
                case "CF":
                    order = "6";
                    break;
                case "LF":
                case "ELF":
                    order = "7";
                    break;
                default:
                    order = "8";
                    break;
            }

            return order;
        }

        private static string ConvertFreBand(string p)
        {
            if (p.Contains("K") || p.Contains("k"))
            {
                //本身转换完成
                return p;
            }
            double freBand = Convert.ToDouble(p);
            string dataBand = string.Empty;
            if (freBand >= 1000)
            {
                double data = freBand / 1000;
                dataBand = data.ToString() + "K";
            }
            else
            {
                dataBand = p;
            }
            return dataBand;
        }
        #endregion

        #region 特征值配置

        /// <summary>
        /// 获取
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="MeasLocationID"></param>
        /// <returns></returns>
        public static List<MeasDef_Ev_Vib> GetMdfTimeDomainEvConf(string turbineID)
        {
            List<MeasDef_Ev_Vib> list = new List<MeasDef_Ev_Vib>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                list = ctx.TimeDomainEvConfs.Where(item => item.WindTurbineID == turbineID ).ToList();
            }
            return list;
        }
        public static List<MeasDef_Ev_Vib> GetMdfTimeDomainEvConf(string turbineID, string MeasDefinitionID)
        {
            List<MeasDef_Ev_Vib> list = new List<MeasDef_Ev_Vib>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                list = ctx.TimeDomainEvConfs.Where(item => item.WindTurbineID == turbineID && item.MeasDefinitionID == MeasDefinitionID).ToList();
            }
            return list;
        }

        public static List<MeasDef_Ev_Vib> GetMdfTimeDomainEvConf(string turbineID,string WaveDefinitionID,string MeasDefinitionID,string MeasLocationID)
        {
            List<MeasDef_Ev_Vib> list = new List<MeasDef_Ev_Vib>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                list = ctx.TimeDomainEvConfs.Where(item => item.WindTurbineID == turbineID && item.WaveDefinitionID == WaveDefinitionID && item.MeasDefinitionID == MeasDefinitionID  && item.MeasLocationID == MeasLocationID).ToList();
            }
            return list;
        }

        public static void AddMdfTimedomainEvConf(List<MeasDef_Ev_Vib> data)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.TimeDomainEvConfs.AddRange(data);
                ctx.SaveChanges();
            }
        }

        public static void DelMdfTimedomainEvConf(string windturbineID,string waveDefinitionID,string MeasDefinitionID)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctx.TimeDomainEvConfs.RemoveRange(ctx.TimeDomainEvConfs.Where(t=>t.WindTurbineID == windturbineID && t.WaveDefinitionID == waveDefinitionID && t.MeasDefinitionID == MeasDefinitionID));
                ctx.SaveChanges();
            }
        }


        /// <summary>
        /// 获取当前测量定义下的所有的工况特征值
        /// </summary>
        /// <param name="windturbineID"></param>
        /// <param name="measid"></param>
        /// <returns></returns>
        public static List<MeasDef_Ev_Process> GetEigenValueProcess(string windturbineID, string measid)
        {
            List<MeasDef_Ev_Process> res = new List<MeasDef_Ev_Process>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res =  ctx.ProcessEvConfs.Where(t=>t.WindTurbineID == windturbineID && t.MeasDefinitionID == measid).ToList();
            }
            return res;

        }

        /// <summary>
        /// 获取机组下的所有的工况特征值
        /// </summary>
        /// <param name="windturbineID"></param>
        /// <returns></returns>
        public static List<MeasDef_Ev_Process> GetEigenValueProcess(string windturbineID)
        {
            List<MeasDef_Ev_Process> res = new List<MeasDef_Ev_Process>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.ProcessEvConfs.Where(t => t.WindTurbineID == windturbineID).ToList();
            }
            return res;

        }


        #endregion
    }
}
