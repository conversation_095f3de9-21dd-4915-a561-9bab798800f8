﻿using System.Web;
using WTCMSLive.WebSite.Models;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Mvc;
using log4net;
using Microsoft.AspNetCore.Authorization;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Helpers;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountController : ControllerBase
    {
        private ILog Log;

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="request">修改密码请求</param>
        /// <returns></returns>
        [HttpPost("ChangePassword")]
        public IActionResult ChangePassword([FromBody] ChangePasswordRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                User user = UserManagement.Login(request.Account, request.OldPassword);
                if (user == null)
                {
                    return Ok(ApiResponse<string>.Error("原密码错误"));
                }

                UserManagement.EditUserPassword(request.Account, request.NewPassword);
                return Ok(ApiResponse<string>.Success("密码修改成功"));
            }
            catch (Exception ex)
            {
                Log?.Error("修改密码失败", ex);
                CMSFramework.Logger.Logger.LogErrorMessage("[ChangePassword]修改密码失败", ex);
                return Ok(ApiResponse<string>.Error($"密码修改失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="request">重置密码请求</param>
        /// <returns></returns>
        [HttpPost("ResetUser")]
        public IActionResult ResetUser([FromBody] ResetPasswordRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                UserManagement.EditUserPassword(request.Account, "666666");
                return Ok(ApiResponse<string>.Success("密码重置成功，新密码为：666666"));
            }
            catch (Exception ex)
            {
                Log?.Error("重置密码失败", ex);
                CMSFramework.Logger.Logger.LogErrorMessage("[ResetUser]重置密码失败", ex);
                return Ok(ApiResponse<string>.Error($"重置密码失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="request">添加用户请求</param>
        /// <returns></returns>
        [HttpPost("adduser")]
        public IActionResult AddUser([FromBody] AddUserRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                // 检查用户名是否已存在
                var checkResult = CheckUserName(request.Account);
                if (checkResult.State == 0)
                {
                    return Ok(ApiResponse<string>.Error(checkResult.Message));
                }

                User _user = new User
                {
                    UserName = request.UserName,
                    UserID = request.Account,
                    PassWord = request.Password,
                    Email = request.Email,
                    Phone = request.Phone ?? ""
                };

                UserManagement.AddUser(_user, request.Role);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.Account,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("添加_用户({0})", request.Account)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("用户添加成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddUser]追加用户失败", ex);
                return Ok(ApiResponse<string>.Error($"添加用户失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 编辑用户
        /// </summary>
        /// <param name="request">编辑用户请求</param>
        /// <returns></returns>
        [HttpPost("edituser")]
        public IActionResult EditUser([FromBody] EditUserRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                User userAccount = UserManagement.GetUserById(request.Account);
                if (userAccount == null)
                {
                    return Ok(ApiResponse<string>.Error("用户不存在"));
                }

                userAccount.UserName = request.UserName;
                userAccount.Email = request.Email;
                userAccount.Phone = request.Phone ?? "";

                UserManagement.EditUser(userAccount);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.Account,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("编辑_用户({0})", request.Account)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("用户编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditUser]编辑用户失败", ex);
                return Ok(ApiResponse<string>.Error($"编辑用户失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 检查用户名是否存在（私有方法）
        /// </summary>
        /// <param name="account">账号</param>
        /// <returns>检查结果</returns>
        private CheckUserNameResponseDTO CheckUserName(string account)
        {
            try
            {
                User myuser = UserManagement.GetUserById(account);

                if (myuser != null)
                {
                    return new CheckUserNameResponseDTO
                    {
                        State = 0,
                        Message = "帐号重复，请修改后添加！"
                    };
                }
                else
                {
                    return new CheckUserNameResponseDTO
                    {
                        State = 1,
                        Message = ""
                    };
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[CheckUserName]检查用户名失败", ex);
                return new CheckUserNameResponseDTO
                {
                    State = 0,
                    Message = $"检查用户名失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="request">删除用户请求</param>
        /// <returns></returns>
        [HttpPost("DeleteUser")]
        public IActionResult DeleteUser([FromBody] DeleteUserRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                UserManagement.DeleteUser(request.Account);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.Account,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("删除_用户({0})", request.Account)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("用户删除成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteUser]删除用户失败", ex);
                return Ok(ApiResponse<string>.Error($"删除用户失败: {ex.Message}"));
            }
        }


        [HttpGet("userlist")]
        public IActionResult GetUserList()
        {
            return Ok(UserManagement.GetUserList());
        }

        #region 角色管理接口

        /// <summary>
        /// 获取角色列表（包含绑定的模块）
        /// </summary>
        /// <returns>角色列表</returns>
        [HttpGet("rolelist")]
        public IActionResult GetRoleList()
        {
            try
            {
                var roles = RoleModuleHelper.GetRoleListWithModules();
                return Ok(roles);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetRoleList]获取角色列表失败", ex);
                return Ok(new List<RoleDTO>());
            }
        }

        /// <summary>
        /// 获取所有模块列表
        /// </summary>
        /// <returns>模块列表</returns>
        [HttpGet("modulelist")]
        public IActionResult GetModuleList()
        {
            try
            {
                var modules = RoleModuleHelper.GetAllModules();
                return Ok(modules);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetModuleList]获取模块列表失败", ex);
                return Ok(new List<ModuleDTO>());
            }
        }

        /// <summary>
        /// 添加角色
        /// </summary>
        /// <param name="request">添加角色请求</param>
        /// <returns></returns>
        [HttpPost("addrole")]
        public IActionResult AddRole([FromBody] AddRoleRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                // 检查角色名称是否重复
                if (RoleModuleHelper.IsRoleNameExists(request.RoleName))
                {
                    return Ok(ApiResponse<string>.Error("角色名称已存在，请修改后添加"));
                }

                RoleModuleHelper.AddRoleWithModules(request);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.RoleName,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("添加_角色({0})", request.RoleName)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("角色添加成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddRole]添加角色失败", ex);
                return Ok(ApiResponse<string>.Error($"添加角色失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 编辑角色
        /// </summary>
        /// <param name="request">编辑角色请求</param>
        /// <returns></returns>
        [HttpPost("editrole")]
        public IActionResult EditRole([FromBody] EditRoleRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                // 检查角色名称是否重复（排除当前角色）
                if (RoleModuleHelper.IsRoleNameExists(request.RoleName, request.RoleID))
                {
                    return Ok(ApiResponse<string>.Error("角色名称已存在，请修改后保存"));
                }

                RoleModuleHelper.EditRoleWithModules(request);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.RoleID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("编辑_角色({0})", request.RoleName)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("角色编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRole]编辑角色失败", ex);
                return Ok(ApiResponse<string>.Error($"编辑角色失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="request">删除角色请求</param>
        /// <returns></returns>
        [HttpPost("deleterole")]
        public IActionResult DeleteRole([FromBody] DeleteRoleRequestDTO request)
        {
            try
            {
                if (request == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                RoleModuleHelper.DeleteRoleWithMappings(request.RoleID);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = request.RoleID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = string.Format("删除_角色({0})", request.RoleID)
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("角色删除成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteRole]删除角色失败", ex);
                return Ok(ApiResponse<string>.Error($"删除角色失败: {ex.Message}"));
            }
        }

        #endregion
    }
}
