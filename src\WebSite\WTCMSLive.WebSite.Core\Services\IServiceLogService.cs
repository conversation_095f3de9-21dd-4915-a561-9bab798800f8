using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 服务日志管理接口
    /// </summary>
    public interface IServiceLogService
    {
        /// <summary>
        /// 获取服务日志文件列表
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志文件列表</returns>
        Task<List<LogFileInfoDTO>> GetLogFilesAsync(string serviceId, string logType, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 获取日志文件内容
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <param name="fileName">文件名</param>
        /// <returns>日志内容</returns>
        Task<LogContentDTO> GetLogContentAsync(string serviceId, string logType, string fileName);

        /// <summary>
        /// 开始实时日志监控
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <param name="connectionId">SignalR连接ID</param>
        /// <returns>是否成功</returns>
        Task<bool> StartRealTimeLogAsync(string serviceId, string logType, string connectionId);

        /// <summary>
        /// 停止实时日志监控
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <param name="connectionId">SignalR连接ID</param>
        /// <returns>是否成功</returns>
        Task<bool> StopRealTimeLogAsync(string serviceId, string logType, string connectionId);

        /// <summary>
        /// 获取服务根目录
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <returns>服务根目录路径</returns>
        Task<string> GetServiceRootDirectoryAsync(string serviceId);

        /// <summary>
        /// 检查日志目录是否存在
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="logType">日志类型</param>
        /// <returns>是否存在</returns>
        Task<bool> CheckLogDirectoryExistsAsync(string serviceId, string logType);
    }
}
