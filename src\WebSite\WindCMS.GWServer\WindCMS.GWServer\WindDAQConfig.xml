<?xml version="1.0"?>
<DAUFacadeConfigFile xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <!-- 默认DAU固件 -->
    <DefaultConfig>
        <DAUVersion>1.4.13.14</DAUVersion>
        <WeridaCoreVer>1.0.0.0</WeridaCoreVer>
        <WeridaCoreDLLFile>DAUE6X/Werida.Core.1413x.dll</WeridaCoreDLLFile>
        <DauFacadeDllFile>DAUE6X/CMSFramework.DAUFacade.E6X.dll</DauFacadeDllFile>
    </DefaultConfig>
    <!--当节点值为true时，软件运行时不匹配DAUVersion和WeridaCore版本，直接获取DefaultConfig节点下的WeridaCoreDLLFile和DauFacadeDllFile节点中的DLL信息，默认可以适配所有的DAU固件-->
    <!--当节点值为false时，软件会按DAUVersion和WeridaCore版本，去匹配合适的WeridaCoreDLLFile和DauFacadeDllFile节点下的DLL信息-->
    <!--考虑：1.频繁出现此字段配置为true导致下发测量定义出错；2.新版本支持的是一个机组多个DAU，一个风场会有不同的固件版本。此处默认为false-->
    <IsOneVersion>false</IsOneVersion>
    <!-- 支持DAU固件 -->
    <SuportDAUConfigs>
        <DAUFacadeConfig>
            <DAUVersion>(?:\d{1,3}\.){3}\d{1,3}</DAUVersion>
            <WeridaCoreVer>1.0.0.0</WeridaCoreVer>
            <WeridaCoreDLLFile>DAUE6X/Werida.Core.1413x.dll</WeridaCoreDLLFile>
            <DauFacadeDllFile>DAUE6X/CMSFramework.DAUFacade.E6X.dll</DauFacadeDllFile>
        </DAUFacadeConfig>
    </SuportDAUConfigs>

</DAUFacadeConfigFile>