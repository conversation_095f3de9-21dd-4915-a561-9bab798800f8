﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class MeasLocationDA
    {
        /// <summary>
        /// 根据机组Id， 获取振动测量位置列表
        /// </summary>
        /// <param name="_windTurId"></param>
        /// <returns></returns>
        public static List<MeasLoc_Vib> GetVibMeasLocationByTurId(string _windTurId)
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                WindTurbine wt = ctx.DevWindTurbines.Find(_windTurId);
                list = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID == _windTurId).ToList();
                List<WindTurbineComponent> component = ctx.DevTurComponents.Where(item => item.WindTurbineID == _windTurId).ToList();
                list.ForEach(item =>
                {
                    item.DevTurComponent = component.Find(c => c.ComponentID == item.ComponentID);
                    SetVibMeasLocName(item, wt);
                });
            }
            return list;
        }

        public static List<MeasLoc_Process> GetWfMeasLocationByTurbineID()
        {
            List<MeasLoc_Process> list = new List<MeasLoc_Process>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocProcesses.ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取所有的测量位置列表
        /// </summary>
        /// <returns></returns>
        public static List<MeasLoc_Vib> GetAllMeasLocations()
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                List<WindTurbine> wtList = ctx.DevWindTurbines.ToList();
                list = ctx.DevMeasLocVibrations.ToList();
                var componentList = ctx.DevTurComponents.ToList();
                list.ForEach(item =>
                {
                    WindTurbine wt = wtList.Find(t => t.WindTurbineID == item.WindTurbineID);
                    if (wt != null)
                    {
                        item.DevTurComponent = componentList.Find(com => com.ComponentID == item.ComponentID);
                        SetVibMeasLocName(item, wt);
                    }

                });
            }
            return list;
        }

        /// <summary>
        /// 获取振动测量位置实体
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public static MeasLoc_Vib GetVibMeasLocByID(string _vibMeasLocID)
        {
            MeasLoc_Vib measLocVib = new MeasLoc_Vib();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocVib = ctx.DevMeasLocVibrations.Find(_vibMeasLocID);
                if (measLocVib == null)
                    return null;
                WindTurbine wt = ctx.DevWindTurbines.Find(measLocVib.WindTurbineID);
                measLocVib.DevTurComponent = ctx.DevTurComponents.Find(measLocVib.ComponentID);
                SetVibMeasLocName(measLocVib, wt);
            }
            return measLocVib;
        }
        /// <summary>
        /// 设置新测点名称（电机+（轴承/电压/电流）+方向）
        /// </summary>
        /// <param name="vib"></param>
        /// <param name="wt"></param>
        private static void SetVibMeasLocName(MeasLoc_Vib vib, WindTurbine wt)
        {
            vib.MeasLocName = wt.WindTurbineName + vib.DevTurComponent.ComponentName + vib.MeasLocName;
        }
    }
}