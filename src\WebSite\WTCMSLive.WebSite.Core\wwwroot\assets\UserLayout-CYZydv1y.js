import"./ChangeLanguage.vue_vue_type_style_index_0_scoped_f1388662_lang-l0sNRNKZ.js";import{_ as l}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{co as u,r as _,h as d,c as p,i as e,b as v,n as m,g as a,t as r,s as f,o as y,p as h}from"./index-BedJHPLx.js";const g={id:"userLayout",class:h(["user-layout-wrapper"])},w={class:"container"},V={class:"user-layout-content"},x={class:"footer"},B={class:"copyright"},L={__name:"UserLayout",setup(N){const n=u(),t=_(""),c=new Date().getFullYear();return d(async()=>{let o=await n.fetchGetVersion();t.value=o}),(o,s)=>{const i=m("router-view");return y(),p("div",g,[e("div",w,[e("div",V,[v(i)]),e("div",x,[e("div",B,[a(" © "+r(f(c))+" V"+r(t.value)+" ",1),s[0]||(s[0]=e("span",null,"配置网站",-1)),s[1]||(s[1]=a(" All Rights Reserved ",-1))])])])])}}},C=l(L,[["__scopeId","data-v-48e87119"]]);export{C as default};
