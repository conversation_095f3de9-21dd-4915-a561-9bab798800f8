﻿using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using WTCMSLive.WebSite.Common;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core;
using WTCMSLive.WebSite.Core.Attributes;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DAUController : ControllerBase
    {
        // 王岩 2015年6月16日 16:15:37
        // 采集单元相关操作

        #region 风场相关

        /// <summary>
        /// 获取dau类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDAUType")]
        public IActionResult GetWaveType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumDAUType value in Enum.GetValues(typeof(EnumDAUType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }

        /// <summary>
        /// 获取dau列表
        /// </summary>
        /// <param name="windParkId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetDAUList")]
        public IActionResult GetDAUList(string WindParkId, string? WindTurbineID)
        {
            List<WindDAU> daulists = DauManagement.GetDAUListByWindParkID(WindParkId);
            if (!string.IsNullOrEmpty(WindTurbineID))
            {
                daulists = daulists.Where(t => t.WindTurbineID == WindTurbineID).ToList();
            }

            var park = DevTreeManagement.GetWindPark(WindParkId);

            List<WindDAUDTO> res = new List<WindDAUDTO>();
            foreach (var dau in daulists)
            {
                res.Add(new WindDAUDTO()
                {
                    WindParkID = WindParkId,
                    WindTurbineID = dau.WindTurbineID,
                    DauID = dau.DauID,
                    DAUName = dau.DAUName,
                    IP = dau.IP,
                    Port = dau.Port,
                    DeviceID = dau.DeviceID,
                    IsAvailable = dau.IsAvailable,
                    DAUType = dau.DAUType,
                    DAUSoftwareVersion = dau.DAUSoftwareVersion,
                    DAUMeasDefVersion = dau.DAUMeasDefVersion,
                    MeasDefVersion = dau.MeasDefVersion,
                    WindTurbineName = park.WindTurbineList.FirstOrDefault(t => t.WindTurbineID == dau.WindTurbineID)?.WindTurbineName,
                    DataAcquisitionInterval = dau.DataAcquisitionInterval,
                    DauOnOffStatus = dau.DauOnOffStatus,
                });
            }

            return Ok(res.OrderBy(t => t.WindTurbineID));
        }

        /// <summary>
        /// 添加采集单元
        /// </summary>
        /// <param name="WindParkID"></param>
        /// <param name="id"></param>
        /// <param name="name"></param>
        /// <param name="ip"></param>
        /// <param name="dataAcquisitionInterval"></param>
        /// <param name="trendSaveInterval"></param>
        /// <param name="enable"></param>
        /// <returns></returns>
        public bool AddDAU(string WindParkID, string id, string name, string ip, int dataAcquisitionInterval, int trendSaveInterval, bool enable, int dautype, int dauPort, int dauDeviceID)
        {
            try
            {
                //支持添加多采集单元。 modified by sq 20190222
                //判断dauName是否重复。
                if (!DauNameCanUse(id, name))
                {
                    return false;
                }
                //DAUId手动自增。
                int dauId = 1;
                WindDAU lastDau = new WindDAU();
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    lastDau = ctx.DAUnits.Where(p => p.WindTurbineID == id && p.WindParkID == WindParkID).OrderByDescending(item => item.DauID).FirstOrDefault();
                    if (lastDau != null)
                    {
                        dauId = Convert.ToInt32(lastDau.DauID) + 1;
                    }
                }

                WindDAU _dau = new WindDAU();
                _dau.TrendSaveInterval = trendSaveInterval;
                _dau.WaveSaveInterval = 0;
                _dau.DataAcquisitionInterval = dataAcquisitionInterval;
                _dau.WindTurbineID = id;
                _dau.DAUName = name;
                _dau.IP = ip;
                _dau.IsAvailable = enable;
                _dau.WindParkID = WindParkID;
                _dau.DauID = Convert.ToString(dauId);
                _dau.DAUType = (EnumDAUType)dautype;
                _dau.Port = dauPort;
                _dau.DeviceID = (byte)dauDeviceID;

                /*      _dau.DAUChannelList = CollectInputDAU_ChannelList(id);*/
                //不默认添加通道
                _dau.DAUChannelList = new List<DAUChannelV2>();

                #region ---转速采集通道列表---
                List<DAUChannel_RotSpeed> rotSpeedChannelList = new List<DAUChannel_RotSpeed>();
                if (!string.IsNullOrEmpty(id) && System.Configuration.ConfigurationManager.AppSettings["ViewModel"] != "BVM")
                {
                    DAUChannel_RotSpeed rotSpeedChannel = null;
                    // modified by ZhangMai Start: 没有测量位置也可以添加 
                    rotSpeedChannel = new DAUChannel_RotSpeed();
                    rotSpeedChannel.ChannelNumber = 13;
                    rotSpeedChannel.DauID = dauId.ToString();
                    List<MeasLoc_RotSpd> measLocList_RotSpd = DevTreeManagement.GetRotSpdMeasLocListByTurId(id);
                    rotSpeedChannel.MeasLocRotSpdID = measLocList_RotSpd[0].MeasLocationID;
                    rotSpeedChannel.WindTurbineID = id;
                    rotSpeedChannelList.Add(rotSpeedChannel);
                }
                // modified by ZhangMai End
                _dau.RotSpeedChannelList = rotSpeedChannelList;
                #endregion ---转速采集通道列表---
                DauManagement.AddDAU(_dau);
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = id;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = string.Format("编辑_DAU({0})", id);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddDAU]编辑DAU失败", ex);
                return false;
            }
            return true;
        }
        //---------------------------------------------------------------------
        /// <summary>
        /// 根据DAU名称判断DAU名称是否重复 by sq
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        public bool DauNameCanUse(string trubineId, string dauName)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var result = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == trubineId & p.DAUName == dauName);
                return result == null;
            }
        }
        //-----------------------------------------------------------------------------------------------------------------------
        // author: Yangming 
        // time: 2012-06-29
        /// <summary>
        /// 添加DAU时根据所选的机组下的振动测量位置添加默认通道
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <returns></returns>
        private List<DAUChannelV2> CollectInputDAU_ChannelList(string windTurID)
        {
            List<DAUChannelV2> channelList = new List<DAUChannelV2>();
            List<MeasLoc_Vib> _measLocList_Vib = DevTreeManagement.GetVibMeasLocationByTurId(windTurID).OrderBy(item => item.OrderSeq).ToList();
            DAUChannelV2 channel = null;
            int num = 1;
            foreach (MeasLoc_Vib measloc_Vib in _measLocList_Vib)
            {
                channel = new DAUChannelV2();
                if (num < (_measLocList_Vib.Count + 1))
                {
                    // 获取通道类型 
                    channel.MeasLocVibID = measloc_Vib.MeasLocationID;
                    //CMS采集单元灵敏系数初始值10，BVM灵敏系数初始值100 - GP 2018-2-25
                    if (System.Configuration.ConfigurationManager.AppSettings["ViewModel"] != "BVM")
                    {
                        channel.Coeff_a = 10;
                    }
                    else
                    {
                        channel.Coeff_a = 100;
                    }
                    channel.Coeff_b = 0;
                    channel.WindTurbineID = windTurID;
                    // 获取最低偏置电压
                    channel.MinBiasVolt = 8;
                    // 获取最高偏置电压
                    channel.MaxBiasVolt = 16;
                    channel.ChannelNumber = num;
                    channelList.Add(channel);
                    num++;
                }
            }
            return channelList;
        }

        /// <summary>
        /// 编辑dau
        /// </summary>
        /// <param name="dau"></param>
        /// <returns></returns>
        [HttpPost("EditDAU")]
        public IActionResult EditDAU(WindDAU dau)
        {
            //WindDAU _dau = DauManagement.GetDAUById(turbineId);
            WindDAU _dau = DAUSManageModel.GetDAUByTrubineIdAndDauId(dau.WindTurbineID, dau.DauID);
            _dau.WindTurbineID = dau.WindTurbineID;
            _dau.DAUName = dau.DAUName;
            _dau.IP = dau.IP;
            _dau.DataAcquisitionInterval = dau.DataAcquisitionInterval;
            _dau.TrendSaveInterval = dau.TrendSaveInterval;
            _dau.IsAvailable = dau.IsAvailable;
            _dau.Port = dau.Port;
            _dau.DeviceID = (byte)dau.DeviceID;
            try
            {
                DauManagement.EditDAUInfo(_dau);
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = dau.WindTurbineID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑_DAU({0})", dau.WindTurbineID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDAU]编辑DAU失败", ex);
                return Ok(ApiResponse<string>.Error("编辑DAU失败" + ex));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }


        /// <summary>
        /// 修改采集单元状态，支持多采集单元
        /// </summary>
        /// <param name="id"></param>
        /// <param name="stateName"></param>
        /// <param name="dauId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("ChangeDAUState")]
        public IActionResult ChangeDAUState(string WindTurbineID, string dauId, bool IsAvailable)
        {
            WindDAU _dau = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, dauId);
            _dau.IsAvailable = IsAvailable;
            try
            {
                DauManagement.EditDAUInfo(_dau);
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = WindTurbineID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑DAU状态_({0})", WindTurbineID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[ChangeDAUState]编辑DAU失败", ex);
                return Ok(ApiResponse<string>.Error("编辑DAU失败" + ex));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        [HttpGet("ChangeDAUStateBat")]
        public IActionResult ChangeDAUStateBat(string WindParkID, bool IsAvailable)
        {
            try
            {
                List<WindDAU> daulists = DauManagement.GetDAUListByWindParkID(WindParkID);
                foreach (var dau in daulists)
                {
                    dau.IsAvailable = IsAvailable;
                    DauManagement.EditDAUInfo(dau);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = WindParkID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑DAU状态_({0})", WindParkID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[ChangeDAUState]编辑DAU失败", ex);
                return Ok(ApiResponse<string>.Error("编辑DAU失败" + ex));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }


        /// <summary>
        /// 删除采集单元
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <returns></returns>
        public bool DeleteDAU(string turbineId, string dauId)
        {
            try
            {
                //DauManagement.DeleteDAU(_DAUID);
                DAUSManageModel.DeleteDAUByTrubineIdAndDauId(turbineId, dauId);
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = turbineId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_DAU({0})", turbineId);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDAU]删除DAU失败", ex);
                return false;
            }
            return true;
        }

        #endregion


        /// <summary>
        /// 根据机组ID和DAU ID获取采集单元的详细状态信息
        /// </summary>
        /// <param name="turbineId">机组ID</param>
        /// <param name="DAUID">DAU ID</param>
        /// <returns>DAU状态信息</returns>
        [HttpGet("GetDauStatusInfoByTurbineIDAndDauID")]
        public IActionResult GetDauStatusInfoByTurbineIDAndDauID(string turbineId, string DAUID)
        {
            try
            {
                if (string.IsNullOrEmpty(turbineId) || string.IsNullOrEmpty(DAUID))
                {
                    return BadRequest("机组ID和DAU ID不能为空");
                }

                // 获取DAU基本信息
                WindDAU dau = DAUSManageModel.GetDAUByTrubineIdAndDauId(turbineId, DAUID);
                if (dau == null)
                {
                    return NotFound($"未找到机组 {turbineId} 下的DAU {DAUID}");
                }

                // 获取DAU实时状态数据
                RTAlarmStatus_DAU data = DAUSManageModel.GetDAURTAlarmStatusByWindTurbineIdAndDAUID(turbineId, DAUID);
                if (data == null)
                {
                    return Ok(new DAUStatusInfoDTO
                    {
                        WindTurbineID = turbineId,
                        DAUID = DAUID,
                        DAUName = dau.DAUName,
                        AlarmState = 0,
                        AlarmStateDescription = "无数据",
                        StatusUpdateTime = DateTime.Now,
                        ChannelStatusList = new List<DAUChannelStatusDTO>(),
                        RotSpeedStatusList = new List<DAURotSpeedStatusDTO>()
                    });
                }

                // 转换为DTO格式
                var result = ConvertToDAUStatusDTO(data, dau, turbineId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetDauStatusInfoByTurbineIDAndDauID]获取DAU状态信息失败 - 机组:{turbineId}, DAU:{DAUID}", ex);
                return Ok(new DAUStatusInfoDTO
                {
                    WindTurbineID = turbineId ?? "",
                    DAUID = DAUID ?? "",
                    DAUName = "获取失败",
                    AlarmState = 0,
                    AlarmStateDescription = "获取数据失败",
                    StatusUpdateTime = DateTime.Now,
                    ChannelStatusList = new List<DAUChannelStatusDTO>(),
                    RotSpeedStatusList = new List<DAURotSpeedStatusDTO>()
                });
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 转换DAU状态数据为DTO格式
        /// </summary>
        /// <param name="data">DAU实时状态数据</param>
        /// <param name="dau">DAU基本信息</param>
        /// <param name="turbineId">机组ID</param>
        /// <returns>DAU状态信息DTO</returns>
        private DAUStatusInfoDTO ConvertToDAUStatusDTO(RTAlarmStatus_DAU data, WindDAU dau, string turbineId)
        {
            var result = new DAUStatusInfoDTO
            {
                WindTurbineID = turbineId,
                DAUID = dau.DauID,
                DAUName = dau.DAUName,
                AlarmState = (int)data.AlarmState,
                //AlarmStateDescription = GetDAUAlarmStateDescription(data.AlarmState),
                StatusUpdateTime = data.StatusUpdateTime,
                ChannelStatusList = new List<DAUChannelStatusDTO>(),
                RotSpeedStatusList = new List<DAURotSpeedStatusDTO>()
            };

            // 处理振动通道状态
            if (data.sensorRTList != null && data.sensorRTList.Count > 0)
            {
                // 获取测量位置信息用于排序
                List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbineId);
                List<DAUChannelV2> dauChannelList = dau.DAUChannelList;

                // 按测量位置顺序处理通道
                foreach (var loc in vibLoc.OrderBy(item => item.OrderSeq))
                {
                    var channelVib = dauChannelList.Find(item => item.MeasLocVibID == loc.MeasLocationID);
                    if (channelVib == null) continue;

                    var channel = data.sensorRTList.Find(item => item.ChannelNumber == channelVib.ChannelNumber);
                    if (channel != null)
                    {
                        var channelDto = new DAUChannelStatusDTO
                        {
                            ChannelNumber = channel.ChannelNumber,
                            MeasLocationID = loc.MeasLocationID,
                            MeasLocationName = loc.MeasLocName,
                            AlarmState = (int)channel.AlarmState,
                            //AlarmStateDescription = GetDAUAlarmStateDescription(channel.AlarmState),
                            DCDataValue = Math.Round(channel.DCDataValue, 1),
                            StatusUpdateTime = channel.StatusUpdateTime
                        };

                        result.ChannelStatusList.Add(channelDto);
                    }
                }
            }

            // 处理转速通道状态
            List<RTAlarmStatus_RSChannel> rotSpeedList = DauManagement.GetAlarmStatusRTRSSensorList(turbineId);
            if (rotSpeedList != null && rotSpeedList.Count > 0)
            {
                var dauRotSpeedList = rotSpeedList.Where(item => item.DauID == dau.DauID).ToList();
                foreach (var rotSpeed in dauRotSpeedList)
                {
                    var rotSpeedDto = new DAURotSpeedStatusDTO
                    {
                        ChannelNumber = rotSpeed.ChannelNumber,
                        MeasLocationID = "",
                        MeasLocationName = "发电机转速",
                        AlarmState = (int)rotSpeed.AlarmState,
                        //AlarmStateDescription = GetDAUAlarmStateDescription(rotSpeed.AlarmState),
                        RotSpeedValue = 0,
                        StatusUpdateTime = rotSpeed.StatusUpdateTime
                    };

                    result.RotSpeedStatusList.Add(rotSpeedDto);
                }
            }

            return result;
        }



        #endregion



        #region 风机相关

        /// <summary>
        /// 编辑机组下dau详细信息
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="windTurbineName"></param>
        /// <param name="IPAddress"></param>
        /// <param name="dataAcquisitionInterval"></param>
        /// <param name="trendSaveInterval"></param>
        /// <param name="enable"></param>
        /// <returns></returns>
        public bool EditDAUSeeting(string ID, string WindParkID, string IPAddress, int dataAcquisitionInterval, int trendSaveInterval, bool enable, string WindDauID, string WindDauName, int Port, int DeviceID)
        {
            WindDAU _dau = DAUSManageModel.GetDAUByTrubineIdAndDauId(ID, WindDauID);
            bool IsAdd = _dau == null;
            if (IsAdd)
            {
                _dau = new WindDAU();
            }
            _dau.WindTurbineID = ID;
            _dau.DAUName = WindDauName;
            _dau.IP = IPAddress;
            _dau.DataAcquisitionInterval = dataAcquisitionInterval;
            _dau.TrendSaveInterval = trendSaveInterval;
            _dau.IsAvailable = enable;
            _dau.WindParkID = WindParkID;
            _dau.DauID = WindDauID;
            _dau.Port = Port;
            _dau.DeviceID = (byte)DeviceID;
            string handleName = "编辑";
            try
            {
                // 如果机组dau信息不存在，则添加dau信息
                if (IsAdd)
                {
                    handleName = "添加";
                    DauManagement.AddDAU(_dau);
                }
                else
                {
                    DauManagement.EditDAUInfo(_dau);
                }
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = ID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_DAU({0})", ID, handleName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDAU]" + handleName + "DAU失败", ex);
                return false;
            }
            return true;
        }


        #region 振动通道

        /// <summary>
        /// 获取振动通道列表信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetDAUVibList")]
        public IActionResult GetDAUVibList(string WindTurbineID, string DAUID)
        {
            List<Dau_DAUChannelV2> ListTemp = new List<Dau_DAUChannelV2>();
            //List<DAUChannelV2> List = DauManagement.GetDAUById(WindTurbineID).DAUChannelList;
            WindDAU windDauData = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (windDauData == null)
            {
                return Ok(windDauData);
            }
            else
            {
                List<DAUChannelV2> List = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID).DAUChannelList;
                List<MeasLoc_Vib> measLocList_Vib = DevTreeManagement.GetVibMeasLocationByTurId(WindTurbineID);

                foreach (MeasLoc_Vib data in measLocList_Vib.OrderBy(item => item.OrderSeq))
                {
                    DAUChannelV2 daudata = List.Find(item => item.MeasLocVibID == data.MeasLocationID);
                    if (daudata != null)
                    {
                        Dau_DAUChannelV2 Channeldata = new Dau_DAUChannelV2()
                        {
                            ChannelNumber = daudata.ChannelNumber,
                            Coeff_a = daudata.Coeff_a,
                            WindTurbineID = daudata.WindTurbineID,
                            MinBiasVolt = daudata.MinBiasVolt,
                            MaxBiasVolt = daudata.MaxBiasVolt,
                            MeasLocVibID = daudata.MeasLocVibID,
                            PhysicalQuantityType = daudata.PhysicalQuantityType,
                            S2S_Coeff_a = daudata.S2S_Coeff_a,
                            S2S_Coeff_b = daudata.S2S_Coeff_b,
                            RegisterAddress = daudata.RegisterAddress,
                            Coeff_L0 = daudata.Coeff_L0,
                            MeasLocVibName = measLocList_Vib.Find(item => item.MeasLocationID == daudata.MeasLocVibID).MeasLocName
                        };
                        ListTemp.Add(Channeldata);
                    }
                }
                return Ok(ListTemp);
            }
        }

        /// <summary>
        /// 获取电流电压通道列表
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="DAUID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetDAUProcessList")]
        public IActionResult GetDAUProcessList(string WindTurbineID, string DAUID)
        {
            List<Dau_DAUChannelV2> ListTemp = new List<Dau_DAUChannelV2>();
            //List<DAUChannelV2> List = DauManagement.GetDAUById(WindTurbineID).DAUChannelList;
            WindDAU windDauData = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (windDauData == null)
            {
                return Ok(ListTemp);
            }
            else
            {
                List<DAUChannel_VoltageCurrent> List = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID).VoltageCurrentList;
                List<MeasLoc_VoltageCurrent> measLocList_Vib = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(WindTurbineID);

                foreach (MeasLoc_VoltageCurrent data in measLocList_Vib.OrderBy(item => item.OrderSeq))
                {
                    DAUChannel_VoltageCurrent daudata = List.Find(item => item.MeasLoc_ProcessId == data.MeasLocationID);
                    if (daudata != null)
                    {
                        Dau_DAUChannelV2 Channeldata = new Dau_DAUChannelV2()
                        {
                            ChannelNumber = daudata.ChannelNumber,
                            Coeff_a = daudata.Coeff_a,
                            WindTurbineID = daudata.WindTurbineID,
                            Coeff_b = daudata.Coeff_b,
                            MeasLocVibName = measLocList_Vib.Find(item => item.MeasLocationID == daudata.MeasLoc_ProcessId).MeasLocName,
                            MeasLocVibID = daudata.MeasLoc_ProcessId,
                            //Coeff_L0 = daudata.Coeff_Senser,
                        };
                        ListTemp.Add(Channeldata);
                    }
                }
                return Ok(ListTemp);
            }
        }


        /// <summary>
        /// 获取机组下的转速测量位置列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetRotSpdMeasLocList")]
        public IActionResult GetRotSpdMeasLocListByTurId(string WindTurbineID)
        {
            List<MeasLoc_RotSpd> rotSpdList = DevTreeManagement.GetRotSpdMeasLocListByTurId(WindTurbineID);
            return Ok(rotSpdList);
        }

        /// <summary>
        /// 获取通道编号列表信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetDAUVibChannelNumList")]
        public IActionResult GetDAUVibChannelNumList(string WindTurbineID, string DAUID)
        {
            List<string> emptyChanNums = new List<string>();
            //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
            WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (DAU == null)
            {
                return Ok(emptyChanNums);
            }
            else
            {
                //获取已经使用的通道列表

                for (int inx = 1; inx <= 96; inx++)
                {
                    //如果通道已使用，则排除掉
                    if (DAU.DAUChannelList.Find(item => item.ChannelNumber == inx) == null && DAU.VoltageCurrentList.Find(t => t.ChannelNumber == inx) == null && DAU.RotSpeedChannelList.Find(t => t.ChannelNumber == inx) == null && DAU.ProcessChannelList.Find(t => t.ChannelNumber == inx) == null)
                    {
                        emptyChanNums.Add(inx.ToString());
                    }
                }
                return Ok(emptyChanNums);
            }
        }
        /// <summary>
        /// 获取未定义的通道测量位置信息
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetmeasLocList")]
        public IActionResult GetmeasLocList(string WindTurbineID, string DAUID)
        {
            //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
            WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            //通道信息列表
            List<MeasLoc_Vib> measLocList_Vib = DevTreeManagement.GetVibMeasLocationByTurId(WindTurbineID);
            DAU.DAUChannelList.ForEach(
                item =>
                {
                    measLocList_Vib.RemoveAll(m => m.MeasLocationID == item.MeasLocVibID);
                }
            );
            if (measLocList_Vib.Count > 0)
            {
                measLocList_Vib = measLocList_Vib.OrderBy(item => item.OrderSeq).ToList();
            }
            return Ok(measLocList_Vib);
        }

        /// <summary>
        /// 获取未定义的电压电流测量位置信息
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="DAUID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetmeasLocProcessList")]
        public IActionResult GetmeasLocProcessList(string WindTurbineID, string DAUID)
        {
            //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
            WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            //通道信息列表
            List<MeasLoc_VoltageCurrent> measLocList_Vib = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(WindTurbineID);
            DAU.VoltageCurrentList.ForEach(
                item =>
                {
                    measLocList_Vib.RemoveAll(m => m.MeasLocationID == item.MeasLoc_ProcessId);
                }
            );
            if (measLocList_Vib.Count > 0)
            {
                measLocList_Vib = measLocList_Vib.OrderBy(item => item.OrderSeq).ToList();
            }
            return Ok(measLocList_Vib);
        }

        /// <summary>
        /// 删除振动通道信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocationID"></param>
        /// <returns></returns>
        public string DeleteDAUVibChanne(string WindTurbineID, int ChannelNumber, string MeasLocationID, string DauID)
        {
            string Message = "state:{0},msg:'{1}'";
            //删除之前查看关联
            //查看测量定义下  通道编号
            try
            {
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(WindTurbineID);
                List<WaveDefinition> waveDefList = MeasDefinitionManagement.GetWaveDefByTurId(WindTurbineID);
                //获取机组下测量定义
                for (int i = 0; i < measDefList.Count; i++)
                {
                    bool MeasDefIsUsed = false;
                    List<WaveDefinition> list = waveDefList.FindAll(item => item.MeasDefinitionID == measDefList[i].MeasDefinitionID && item.WaveFormType == EnumWaveFormType.WDF_Time);
                    if (list.Count() > 0 && list.Find(item => item.MeasLocationID == MeasLocationID) != null)
                    {
                        Message = string.Format(Message, 0, "此测量通道正在被测量定义(时域)使用，如需删除，请先解除使用关系！");
                        MeasDefIsUsed = true;
                    }
                    if (!MeasDefIsUsed)
                    {
                        List<WaveDefinition> EnvlopeList = waveDefList.FindAll(item => item.MeasDefinitionID == measDefList[i].MeasDefinitionID && item.WaveFormType == EnumWaveFormType.WDF_Envelope);
                        if (EnvlopeList.Count() > 0 && EnvlopeList.Find(item => item.MeasLocationID == MeasLocationID) != null)
                        {
                            Message = string.Format(Message, 0, "此测量通道正在被测量定义(高频包络)使用，如需删除，请先解除使用关系！");
                            MeasDefIsUsed = true;
                        }
                    }
                    if (MeasDefIsUsed)
                    {
                        return "{" + Message + "}";
                    }
                }
                DauManagement.DeleteChannelByDAUIDAndChannelNum(WindTurbineID, DauID, ChannelNumber);

                //删除超声波配置
                DauManagement.DeleteUltrasonicChannel(WindTurbineID, DauID, ChannelNumber);
                Message = string.Format(Message, 1, "");
                #region ---删除日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocationID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_DAU振动通道({0})", ChannelNumber);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("删除DAU振动通道({})失败 :", ChannelNumber) + ex.Message;
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDAUVibChanne]" + ErrorMsg, ex);
                Message = string.Format(Message, 0, ErrorMsg);
            }
            return "{" + Message + "}";
        }


        public string DeleteDAUProcessChanne(string WindTurbineID, int ChannelNumber, string MeasLocationID, string DauID)
        {
            string Message = "state:{0},msg:'{1}'";
            //删除之前查看关联
            //查看测量定义下  通道编号
            try
            {
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(WindTurbineID);
                List<WaveDef_VoltageCurrent> waveDefList = MeasDefinitionManagement.GetWaveDefVoltageCurrentByTurId(WindTurbineID);
                //获取机组下测量定义
                for (int i = 0; i < measDefList.Count; i++)
                {
                    bool MeasDefIsUsed = false;
                    List<WaveDef_VoltageCurrent> list = waveDefList.FindAll(item => item.MeasDefinitionID == measDefList[i].MeasDefinitionID && item.WaveFormType == EnumWaveFormType.WDF_Time);
                    if (list.Count() > 0 && list.Find(item => item.MeasLocationID == MeasLocationID) != null)
                    {
                        Message = string.Format(Message, 0, "此测量通道正在被测量定义使用，如需删除，请先解除使用关系！");
                        MeasDefIsUsed = true;
                    }
                    if (!MeasDefIsUsed)
                    {
                        List<WaveDef_VoltageCurrent> EnvlopeList = waveDefList.FindAll(item => item.MeasDefinitionID == measDefList[i].MeasDefinitionID && item.WaveFormType == EnumWaveFormType.WDF_Envelope);
                        if (EnvlopeList.Count() > 0 && EnvlopeList.Find(item => item.MeasLocationID == MeasLocationID) != null)
                        {
                            Message = string.Format(Message, 0, "此测量通道正在被测量定义使用，如需删除，请先解除使用关系！");
                            MeasDefIsUsed = true;
                        }
                    }
                    if (MeasDefIsUsed)
                    {
                        return "{" + Message + "}";
                    }
                }
                DauManagement.DeleteVoltageCurrentChannelByDAUIDAndChannelNum(WindTurbineID, DauID, ChannelNumber);

                Message = string.Format(Message, 1, "");
                #region ---删除日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocationID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_DAU电流电压通道({0})", ChannelNumber);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("删除DAU电流电压通道({})失败 :", ChannelNumber) + ex.Message;
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDAUVibChanne]" + ErrorMsg, ex);
                Message = string.Format(Message, 0, ErrorMsg);
            }
            return "{" + Message + "}";
        }

        /// <summary>
        /// 修改dau的振动通道
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocVibNameId"></param>
        /// <param name="Coeff"></param>
        /// <param name="MinBiasVolt"></param>
        /// <param name="MaxBiasVolt"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string EditDAUVibChanne(string WindTurbineID, int ChannelNumber, string MeasLocVibNameId, string Coeff, float MinBiasVolt, float MaxBiasVolt, string isAddType, string DAUID, double? PhysicalQuantityType, double? S2S_Coeff_a, double? S2S_Coeff_b, short? RegisterAddress, float? Coeff_L0)
        {
            string Message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                string refMeassage = "";
                //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
                if (CheckChannelIsUsed(WindTurbineID, ChannelNumber, MeasLocVibNameId, DAU, ref refMeassage))
                {
                    Message = string.Format(Message, 0, refMeassage);
                    return "{" + Message + "}";
                }
                DAUChannelV2 chan = DAU.DAUChannelList.Find(
                        item => item.ChannelNumber == ChannelNumber
                    );
                if (chan == null)
                {
                    chan = new DAUChannelV2();
                    chan.DauID = DAUID;
                }
                chan.ChannelNumber = Convert.ToInt32(ChannelNumber);
                chan.Coeff_a = Convert.ToDouble(Coeff);
                chan.Coeff_b = 0;
                chan.WindTurbineID = WindTurbineID;
                chan.MaxBiasVolt = MaxBiasVolt;
                chan.MeasLocVibID = MeasLocVibNameId;
                chan.MinBiasVolt = MinBiasVolt;

                // 应变添加
                chan.S2S_Coeff_a = S2S_Coeff_a ?? 0;
                chan.S2S_Coeff_b = S2S_Coeff_b ?? 0;
                chan.PhysicalQuantityType = (CMSFramework.TypeDef.EnumPhysicalQuantityType)PhysicalQuantityType;
                chan.RegisterAddress = RegisterAddress ?? 0;
                chan.Coeff_L0 = Coeff_L0 ?? 0;

                if (isAddType == "1")
                {
                    DauManagement.AddDAUVibChannel(chan);
                }
                else
                {
                    DauManagement.EditDAUVibChannel(chan);
                    actionName = "修改";
                }
                Message = string.Format(Message, 1, "");
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocVibNameId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_DAU振动通道({0})", MeasLocVibNameId, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion

                //添加超声波配置,ultrasonicchannelconfig
                if (DAU.DAUType == EnumDAUType.Ultrasonic || DAU.DAUType == EnumDAUType.IfastUltrasonic)
                {
                    int dispatcherChannelID = 0;
                    int dispatcherID = 0;
                    //if (MeasLocVibNameId.Contains("TOWFL"))
                    //{
                    //    dispatcherChannelID = Convert.ToInt32(MeasLocVibNameId.Substring(MeasLocVibNameId.Length -1, 1));
                    //    dispatcherID = Convert.ToInt32(MeasLocVibNameId.Substring(MeasLocVibNameId.Length -3, 1));
                    //}

                    if (DAU.DAUType == EnumDAUType.Ultrasonic)
                    {
                        dispatcherChannelID = Convert.ToInt32(MeasLocVibNameId.Substring(MeasLocVibNameId.Length - 1, 1));
                        dispatcherID = Convert.ToInt32(MeasLocVibNameId.Substring(MeasLocVibNameId.Length - 3, 1));
                    }
                    UltrasonicChannelConfig ultrasonic = new UltrasonicChannelConfig()
                    {
                        DauID = DAU.DauID,
                        ChannelNumber = Convert.ToInt32(ChannelNumber),
                        WindTurbineID = WindTurbineID,
                        PreloadLowerLimmit = 0,
                        PreloadUpperLimmit = 0,
                        PreloadCalCoeffs = "0",
                        TempCalibCoeff = 0,
                        DispatcherChannelID = dispatcherChannelID,
                        DispatcherID = dispatcherID,
                    };

                    if (isAddType == "1")
                    {

                        DauManagement.AddUltrasonic(ultrasonic);
                    }
                    else
                    {
                        DauManagement.EditUltrasonic(ultrasonic);
                    }

                }
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("{0}DAU振动通道失败 :", actionName) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDAUVibChanne]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }



        public string AddDAUVibChanneBatch(string channeldataStr, string WindTurbineID, string DAUID)
        {
            string Message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                List<DAUChannelV2> channeldatas = JsonConvert.DeserializeObject<List<DAUChannelV2>>(channeldataStr);
                string refMeassage = "";
                //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
                List<UltrasonicChannelConfig> ultrasonicChannelConfigs = new List<UltrasonicChannelConfig>();
                foreach (var channel in channeldatas)
                {
                    if (CheckChannelIsUsed(WindTurbineID, channel.ChannelNumber, channel.MeasLocVibID, DAU, ref refMeassage))
                    {
                        //Message = string.Format(Message, 0, refMeassage);
                        //return "{" + Message + "}";

                        continue;
                    }

                    channel.WindTurbineID = WindTurbineID;
                    channel.DauID = DAUID;

                    //添加超声波配置,ultrasonicchannelconfig
                    if (DAU.DAUType == EnumDAUType.Ultrasonic || DAU.DAUType == EnumDAUType.IfastUltrasonic)
                    {
                        int dispatcherChannelID = 0;
                        int dispatcherID = 0;
                        if (DAU.DAUType == EnumDAUType.Ultrasonic)
                        {
                            dispatcherChannelID = Convert.ToInt32(channel.MeasLocVibID.Substring(channel.MeasLocVibID.Length - 1, 1));
                            dispatcherID = Convert.ToInt32(channel.MeasLocVibID.Substring(channel.MeasLocVibID.Length - 3, 1));
                        }
                        UltrasonicChannelConfig ultrasonic = new UltrasonicChannelConfig()
                        {
                            DauID = DAU.DauID,
                            ChannelNumber = Convert.ToInt32(channel.ChannelNumber),
                            WindTurbineID = WindTurbineID,
                            PreloadLowerLimmit = 0,
                            PreloadUpperLimmit = 0,
                            PreloadCalCoeffs = "0",
                            TempCalibCoeff = 0,
                            DispatcherChannelID = dispatcherChannelID,
                            DispatcherID = dispatcherID,
                        };
                        ultrasonicChannelConfigs.Add(ultrasonic);
                    }
                }
                DauManagement.AddDAUVibChannel(channeldatas);
                DauManagement.AddUltrasonic(ultrasonicChannelConfigs);


                Message = string.Format(Message, 1, "添加成功");
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("{0}DAU振动通道失败 :", actionName) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDAUVibChanne]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }

        //public string EditDAUProcessChanne(string WindTurbineID, int ChannelNumber, string MeasLocVibNameId, string CoeffA, string CoeffB, string isAddType, string DAUID,string CoeffSenser)
        public string EditDAUProcessChanne(string WindTurbineID, int ChannelNumber, string MeasLocVibNameId, string CoeffA, string CoeffB, string isAddType, string DAUID)
        {
            string Message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                string refMeassage = "";
                //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
                if (CheckChannelIsUsed(WindTurbineID, ChannelNumber, MeasLocVibNameId, DAU, ref refMeassage))
                {
                    Message = string.Format(Message, 0, refMeassage);
                    return "{" + Message + "}";
                }
                DAUChannel_VoltageCurrent chan = DAU.VoltageCurrentList.Find(
                        item => item.ChannelNumber == ChannelNumber
                    );
                if (chan == null)
                {
                    chan = new DAUChannel_VoltageCurrent();
                    chan.DauID = DAUID;
                }
                chan.ChannelNumber = Convert.ToInt32(ChannelNumber);
                chan.Coeff_a = double.Parse(CoeffA);
                chan.Coeff_b = double.Parse(CoeffB);
                chan.WindTurbineID = WindTurbineID;
                chan.MeasLoc_ProcessId = MeasLocVibNameId;
                //chan.Coeff_Senser = int.Parse(CoeffSenser);


                if (isAddType == "1")
                {
                    DauManagement.AddDAUVoltageCurrentChannel(chan);
                }
                else
                {
                    DauManagement.EditDAUVoltageCurrentChannel(chan);
                    actionName = "修改";
                }
                Message = string.Format(Message, 1, "");
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocVibNameId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_DAU电流电压通道({0})", MeasLocVibNameId, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("{0}DAU电流电压通道失败 :", actionName) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDAUVibChanne]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }

        #endregion

        #region 转速通道

        /// <summary>
        ///  获取转速通道列表信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetRotSpeedList")]
        public IActionResult GetRotSpeedList(string WindTurbineID, string DAUID)
        {
            //List<DAUChannel_RotSpeed> List = DauManagement.GetDAUById(WindTurbineID).RotSpeedChannelList;
            //WindDAU windDauData = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            //if (windDauData == null)
            //{
            //  return windDauData.ToJson();
            //}
            List<DAUChannel_RotSpeedModel> rotSpdList = new List<DAUChannel_RotSpeedModel>();
            WindDAU dauRotspeed = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (dauRotspeed == null)
            {
                return Ok(rotSpdList);
            }
            else
            {
                List<DAUChannel_RotSpeed> List = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID).RotSpeedChannelList;

                List<MeasLoc_RotSpd> MeasLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(WindTurbineID);

                foreach (DAUChannel_RotSpeed data in List)
                {   //页面中输入波形线数，数据库中存储脉冲合并数  公式： 脉冲合并数=编码器线数/波形线数
                    // 显示的的时候必须做转化
                    //data. .LineCounts = data.MeasLoc_Rotspd.LineCounts;
                    DAUChannel_RotSpeedModel model = new DAUChannel_RotSpeedModel();
                    model.ChannelNumber = data.ChannelNumber;
                    model.MeasLocRotSpdID = data.MeasLocRotSpdID;
                    model.WindTurbineID = data.WindTurbineID;
                    model.MeasLocRotSpdName = MeasLocList.Find(item => item.MeasLocationID == data.MeasLocRotSpdID).MeasLocName;
                    rotSpdList.Add(model);
                }
                return Ok(rotSpdList);
            }
        }

        /// <summary>
        /// 修改dau的振动通道
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocVibNameId"></param>
        /// <param name="Coeff"></param>
        /// <param name="MinBiasVolt"></param>
        /// <param name="MaxBiasVolt"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string EditDAURotSpeed(string WindTurbineID, int RotSpeedchannelNumber, string MeasLocRotSpdID, string DAUID)
        {
            string Message = "state:{0},msg:'{1}'";
            string actionName = "修改";
            try
            {
                string refMeassage = "";
                //WindDAU dau = DauManagement.GetDAUById(WindTurbineID);
                WindDAU dau = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
                if (CheckChannelIsUsed(WindTurbineID, RotSpeedchannelNumber, MeasLocRotSpdID, dau, ref refMeassage))
                {
                    Message = string.Format(Message, 0, refMeassage);
                    return "{" + Message + "}";
                }
                //measLocRotspd.ChannelNumber = RotSpeedchannelNumber;
                //measLocRotspd.LineCounts = LineCounts;
                //页面中输入波形线数，数据库中存储脉冲合并数  公式： 脉冲合并数=编码器线数/波形线数
                // wangy 
                //measLocRotspd.PulseNumberPerCycle = LineCounts / WaveLineCounts; //Convert.ToInt32(myRotSpeed.PulseNumberPerCycle);//PulseNumberPerCycle
                //wangy 通道号，可以编辑
                //DevTreeManagement.EditRotSpdMeasLocation(measLocRotspd);
                List<DAUChannel_RotSpeed> RotChannelList = dau.RotSpeedChannelList;
                //如果无转速数据，就添加，否则就需要删除之前的转速数据，然后在添加
                if (RotChannelList == null || RotChannelList.Count == 0)
                {
                    DauManagement.AddDAURotSpeedChannel(new DAUChannel_RotSpeed()
                    {
                        ChannelNumber = RotSpeedchannelNumber,
                        WindTurbineID = WindTurbineID,
                        MeasLocRotSpdID = MeasLocRotSpdID
                    });
                }
                else
                {
                    foreach (DAUChannel_RotSpeed item in RotChannelList)
                    {
                        DauManagement.DeleteDAURotSpeedChannel(item);
                        item.ChannelNumber = RotSpeedchannelNumber;
                        DauManagement.AddDAURotSpeedChannel(item);
                    }
                }
                Message = string.Format(Message, 1, "");
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocRotSpdID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_DAU振动通道({0})", MeasLocRotSpdID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("{0}DAU振动通道失败 :", actionName) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDAURotSpeed]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }
        /// <summary>
        /// 判断机组通道编号，是否被占用
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="channleNum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private bool CheckChannelIsUsed(string _turbineId, int channleNum, string MeasLocationID, WindDAU dau, ref string message)
        {
            bool isUse = false;
            List<DAUChannelV2> meacLocVib = dau.DAUChannelList;
            if (meacLocVib.Count > 0)
            {
                if (meacLocVib.Find(item => item.ChannelNumber == channleNum && item.MeasLocVibID != MeasLocationID) != null)
                {
                    message = "通道编号在振动通道中存在！";
                    isUse = true;
                }
            }
            if (!isUse)
            {
                List<DAUChannel_Process> ProcessList = dau.ProcessChannelList;
                if (ProcessList.Count > 0)
                {
                    if (ProcessList.Find(item => item.ChannelNumber == channleNum && item.MeasLoc_ProcessId != MeasLocationID) != null)
                    {
                        message = "通道编号在工况通道中存在！";
                        isUse = true;
                    }
                }
            }
            if (!isUse)
            {
                List<DAUChannel_RotSpeed> rotspdList = dau.RotSpeedChannelList;
                if (rotspdList.Find(item => item.ChannelNumber == channleNum && item.MeasLocRotSpdID != MeasLocationID) != null)
                {
                    message = "通道编号在转速通道中存在！";
                    isUse = true;
                }
            }
            return isUse;
        }

        #endregion

        #region 工况通道
        /// <summary>
        /// 获取工况通道编号列表
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetworkConditionChannelNumList")]
        public IActionResult GetworkConditionChannelNumList(string WindTurbineID)
        {
            List<string> emptyChanNums = new List<string>();
            WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
            //获取已经使用的通道列表
            List<string> usedChanNums = new List<string>();
            if (DAU != null && DAU.DAUChannelList.Count > 0)
            {
                DAU.DAUChannelList.ForEach(
                        item =>
                        {
                            usedChanNums.Add(item.ChannelNumber.ToString());
                        }
                    );
            }
            if (DAU != null && DAU.ProcessChannelList.Count > 0)
            {
                DAU.ProcessChannelList.ForEach(
                        item =>
                        {
                            usedChanNums.Add(item.ChannelNumber.ToString());
                        }
                    );
            }

            // 初始振动通道下拉列表
            for (int inx = 1; inx <= 16; inx++)
            {
                string usedChanNum =
                    usedChanNums.Find(item => item == inx.ToString());

                if (string.IsNullOrEmpty(usedChanNum) == true)
                {
                    emptyChanNums.Add(inx.ToString());
                }
            }
            return Ok(emptyChanNums);
        }
        /// <summary>
        /// 获取工况通道下的测量位置信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWordConditionMeasLoc")]
        public IActionResult GetWordConditionMeasLoc(string WindTurbineID, string DAUID)
        {
            //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
            WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            // 当前DAU关联的机组ID
            // 获取机组下所有数据来源是DAU的工况测量位置列表
            List<MeasLoc_Process> measlocsPro_DAU = DevTreeManagement.GetMeaslocProcessListWithDAUByTurID(WindTurbineID);
            // 查找已经绑定了通道的测量位置列表
            List<string> usedChanLocList = new List<string>();
            DAU.ProcessChannelList.ForEach(
                item =>
                {
                    //如果已经绑定了通道信息，删除掉已有的
                    measlocsPro_DAU.RemoveAll(m => m.MeasLocationID == item.MeasLoc_ProcessId);
                }
            );
            List<MeasLoc_Process> processList = new List<MeasLoc_Process>();
            List<KeyValuePair<int, string>> parmeTypelist = EnumWorkCondParamTypeHelper.GetParamTypeList();
            foreach (var processName in parmeTypelist)
            {
                EnumWorkCondition_ParamType ParmaType = (EnumWorkCondition_ParamType)processName.Key;
                MeasLoc_Process measLocProcess = measlocsPro_DAU.Find(item => item.Param_Type_Code == ParmaType);
                if (measLocProcess != null)
                {
                    processList.Add(measLocProcess);
                }
            }
            return Ok(processList.OrderBy(item => item.OrderSeq));
        }
        /// <summary>
        /// 信号带宽 获取信号宽带
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("InitUpperLimitFreqList")]
        public IActionResult InitUpperLimitFreqList()
        {
            List<KeyValuePair<string, float>> _dataList = WebCommonRefData.GetUpperLimitFreqList();
            return Ok(_dataList);
        }

        /// <summary>
        /// 获取采样长度
        /// </summary>
        /// 
        [HttpGet("InitGetSampleLengthList")]
        public IActionResult InitGetSampleLengthList()
        {
            List<KeyValuePair<string, int>> _dataList = WebCommonRefData.GetSampleTimeLengthList();
            return Ok(_dataList);
        }

        /// <summary>
        /// 获取工况通道列表
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetworkConditionList")]
        public IActionResult GetworkConditionList(string WindTurbineID, string DAUID)
        {
            List<Dau_DAUChannel_Process> list = new List<Dau_DAUChannel_Process>();
            //List<DAUChannel_Process> ChannelList = DauManagement.GetDAUById(WindTurbineID).ProcessChannelList;
            WindDAU dauChannelData = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);
            if (dauChannelData == null)
            {
                return Ok(list);
            }
            else
            {
                List<DAUChannel_Process> ChannelList = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID).ProcessChannelList;
                List<MeasLoc_Process> ProcesList = DevTreeManagement.GetWorkCondMeasLocByTurID(WindTurbineID);

                ChannelList.ForEach(item =>
                {
                    Dau_DAUChannel_Process process = new Dau_DAUChannel_Process()
                    {
                        ChannelNumber = item.ChannelNumber,
                        Coeff_a = item.Coeff_a,
                        Coeff_b = item.Coeff_b,
                        WindTurbineID = item.WindTurbineID,
                        MeasLoc_ProcessId = item.MeasLoc_ProcessId,
                        MeasLocName = ProcesList.Find(loc => loc.MeasLocationID == item.MeasLoc_ProcessId).MeasLocName
                    };
                    list.Add(process);
                });
                return Ok(list);
            }
        }

        /// <summary>
        /// 编辑工况通道列表
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="channelNumber"></param>
        /// <param name="MeasLocationID"></param>
        /// <param name="UpperLimitFreqency"></param>
        /// <param name="SampleLength"></param>
        /// <param name="PowerCoeff_a"></param>
        /// <param name="PowerCoeff_b"></param>
        /// <param name="isAddType">1 添加 0 修改</param>
        /// <returns></returns>
        public string EditworkConditionSeeting(string WindTurbineID, int ChannelNumber, string MeasLocationID, float UpperLimitFreqency, int SampleLength, float PowerCoeff_a, float PowerCoeff_b, string isAddType, string DAUID)
        {
            string Message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                string refMeassage = "";
                //WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(WindTurbineID, DAUID);

                //编辑的时候先不判定通道，todo
                if (CheckChannelIsUsed(WindTurbineID, ChannelNumber, MeasLocationID, DAU, ref refMeassage))
                {
                    Message = string.Format(Message, 0, refMeassage);
                    return "{" + Message + "}";
                }

                DAUChannel_Process channel = new DAUChannel_Process();
                if (isAddType == "0")
                {
                    channel = DauManagement.GetDAUChannelProcessListByDAUId(WindTurbineID).Find(item => item.MeasLoc_ProcessId == MeasLocationID);
                    DauManagement.DeleteProcessChan(WindTurbineID, DAUID, channel.ChannelNumber);
                    actionName = "修改";
                }
                channel.DauID = DAUID;
                channel.ChannelNumber = ChannelNumber;
                channel.Coeff_a = PowerCoeff_a;
                channel.Coeff_b = PowerCoeff_b;
                channel.WindTurbineID = WindTurbineID;
                channel.MeasLoc_ProcessId = MeasLocationID;
                DauManagement.addDAUProcessChannel(channel);
                Message = string.Format(Message, 1, "");
                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocationID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_DAU工况通道({0})", MeasLocationID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("{0}DAU工况通道失败 :", actionName) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[EditworkConditionSeeting]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }

        /// <summary>
        /// 删除工况通道信息
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocationID"></param>
        /// <returns></returns>
        public string DeleteworkConditionChanne(string WindTurbineID, int ChannelNumber, string MeasLocationID, string DauID)
        {
            string Message = "state:{0},msg:'{1}'";
            //删除之前查看关联
            //查看测量定义下  通道编号
            try
            {
                WindDAU DAU = DauManagement.GetDAUById(WindTurbineID);
                DAUChannel_Process chan = DAU.ProcessChannelList.Find(
                           item => item.ChannelNumber == ChannelNumber
                       );
                //删除之前查看关联
                //查看测量定义下  通道编号
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(WindTurbineID);
                //获取机组下测量定义
                for (int i = 0; i < measDefList.Count; i++)
                {
                    List<MeasDef_Process> measLocList = MeasDefinitionManagement.GetMDFWorkCondLocListByMeasDefId(WindTurbineID, measDefList[i].MeasDefinitionID);
                    for (int j = 0; j < measLocList.Count; j++)
                    {
                        if (chan != null && measLocList[j].MeasLocationID == chan.MeasLoc_ProcessId)
                        {
                            Message = string.Format(Message, 0, "此工况测量通道正在被测量定义使用，如需删除，请先解除使用关系！");
                            return "{" + Message + "}";
                        }
                    }
                }
                DauManagement.DeleteProcessChan(WindTurbineID, DauID, ChannelNumber);
                Message = string.Format(Message, 1, "");
                #region ---删除日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocationID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_DAU工况通道({0})", ChannelNumber);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                string ErrorMsg = string.Format("删除DAU工况通道({0})失败 :", ChannelNumber) + ex.Message;
                Message = string.Format(Message, 0, ErrorMsg);
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteworkConditionChanne]" + ErrorMsg, ex);
            }
            return "{" + Message + "}";
        }

        #endregion

        #endregion

        /// <summary>
        /// 批量添加采集单元（全部成功或全部失败）
        /// </summary>
        /// <param name="dauList"></param>
        /// <returns></returns>
        [HttpPost("BatchAddDAU")]
        public IActionResult BatchAddDAU([FromBody] List<WindDAU> dauList)
        {
            if (dauList == null || dauList.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var dau in dauList)
                        {
                            // 判断DAU名称是否重复
                            if (ctx.DAUnits.Any(p => p.WindTurbineID == dau.WindTurbineID && p.DAUName == dau.DAUName))
                            {
                                errorMsgs.Add($"WindTurbineID={dau.WindTurbineID} 的DAU名称 {dau.DAUName} 已存在");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }

                        // 用于存储每个WindTurbineID和WindParkID组合的当前最大DAUId
                        var dauIdDict = new Dictionary<string, int>();
                        foreach (var dau in dauList)
                        {// 构造字典的键
                            var key = $"{dau.WindTurbineID}_{dau.WindParkID}";
                            // 如果字典中没有该键，则查询数据库获取当前最大DAUId
                            if (!dauIdDict.ContainsKey(key))
                            {
                                var lastDau = ctx.DAUnits.Where(p => p.WindTurbineID == dau.WindTurbineID && p.WindParkID == dau.WindParkID)
                                    .OrderByDescending(item => item.DauID).FirstOrDefault();
                                int dauId = 1;
                                if (lastDau != null)
                                {
                                    dauId = Convert.ToInt32(lastDau.DauID) + 1;
                                }
                                dauIdDict[key] = dauId;
                            }

                            // 从字典中获取当前的DAUId并递增
                            dau.DauID = dauIdDict[key].ToString();
                            dauIdDict[key]++;

                            ctx.DAUnits.Add(dau);
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量添加成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddDAU]批量添加采集单元失败", ex);
                        return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量编辑采集单元（全部成功或全部失败）
        /// </summary>
        /// <param name="dauList"></param>
        /// <returns></returns>
        [HttpPost("BatchEditDAU")]
        public IActionResult BatchEditDAU([FromBody] List<WindDAU> dauList)
        {
            if (dauList == null || dauList.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var dau in dauList)
                        {
                            var exist = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == dau.WindTurbineID && p.DauID == dau.DauID);
                            if (exist == null)
                            {
                                errorMsgs.Add($"WindTurbineID={dau.WindTurbineID} 的DauID={dau.DauID} 不存在，无法编辑");
                                continue;
                            }
                            // 判断DAU名称是否被其他DAU占用
                            var nameConflict = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == dau.WindTurbineID && p.DAUName == dau.DAUName && p.DauID != dau.DauID);
                            if (nameConflict != null)
                            {
                                errorMsgs.Add($"WindTurbineID={dau.WindTurbineID} 的DAU名称 {dau.DAUName} 已被其他DAU占用");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }
                        foreach (var dau in dauList)
                        {
                            var exist = ctx.DAUnits.First(p => p.WindTurbineID == dau.WindTurbineID && p.DauID == dau.DauID);
                            exist.DAUName = dau.DAUName;
                            exist.IP = dau.IP;
                            exist.DataAcquisitionInterval = dau.DataAcquisitionInterval;
                            exist.TrendSaveInterval = dau.TrendSaveInterval;
                            exist.IsAvailable = dau.IsAvailable;
                            exist.Port = dau.Port;
                            exist.DeviceID = dau.DeviceID;
                            ctx.Entry(exist).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量编辑成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchEditDAU]批量编辑采集单元失败", ex);
                        return Ok(ApiResponse<string>.Error("批量编辑失败: " + ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量修改DAU网络配置（IP和端口）
        /// </summary>
        /// <param name="request">批量修改请求</param>
        /// <returns></returns>
        [HttpPost("BatchUpdateDAUNetwork")]
        public IActionResult BatchUpdateDAUNetwork([FromBody] List<DAUNetworkUpdateItem> request)
        {
            if (request == null || request.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("请求参数不能为空"));
            }

            using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // 第一阶段：验证所有DAU是否存在，并检查IP冲突
                        var validationErrors = new List<string>();
                        var dauToUpdate = new List<(WindDAU dau, DAUNetworkUpdateItem updateItem)>();

                        foreach (var updateItem in request)
                        {
                            // 验证必填字段
                            if (string.IsNullOrWhiteSpace(updateItem.WindTurbineID) ||
                                string.IsNullOrWhiteSpace(updateItem.DauID) ||
                                string.IsNullOrWhiteSpace(updateItem.IP) ||
                                updateItem.Port <= 0 || updateItem.Port > 65535)
                            {
                                validationErrors.Add($"机组 {updateItem.WindTurbineID} DAU {updateItem.DauID} 参数无效");
                                continue;
                            }

                            // 查找DAU是否存在
                            var existingDAU = ctx.DAUnits.FirstOrDefault(p =>
                                p.WindTurbineID == updateItem.WindTurbineID && p.DauID == updateItem.DauID);

                            if (existingDAU == null)
                            {
                                validationErrors.Add($"机组 {updateItem.WindTurbineID} 的 DAU {updateItem.DauID} 不存在");
                                continue;
                            }

                            // 检查IP地址冲突（排除当前DAU）
                            //var ipConflict = ctx.DAUnits.FirstOrDefault(p =>
                            //    p.IP == updateItem.IP &&
                            //    !(p.WindTurbineID == updateItem.WindTurbineID && p.DauID == updateItem.DauID));

                            //if (ipConflict != null)
                            //{
                            //    validationErrors.Add($"IP地址 {updateItem.IP} 已被机组 {ipConflict.WindTurbineID} DAU {ipConflict.DauID} 使用");
                            //    continue;
                            //}

                            dauToUpdate.Add((existingDAU, updateItem));
                        }

                        // 如果有验证错误，回滚事务并返回错误
                        if (validationErrors.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<List<string>>.Error("修改失败", validationErrors));
                        }

                        // 第二阶段：执行更新操作
                        foreach (var (dau, updateItem) in dauToUpdate)
                        {
                            var result = new DAUNetworkUpdateResult
                            {
                                WindTurbineID = updateItem.WindTurbineID,
                                DauID = updateItem.DauID,
                                DAUName = updateItem.DAUName ?? dau.DAUName,
                                OriginalIP = dau.IP,
                                OriginalPort = dau.Port,
                                NewIP = updateItem.IP,
                                NewPort = updateItem.Port
                            };

                            try
                            {
                                // 更新DAU网络配置
                                dau.IP = updateItem.IP;
                                dau.Port = updateItem.Port;

                                ctx.Entry(dau).State = Microsoft.EntityFrameworkCore.EntityState.Modified;

                                result.Success = true;

                                // 记录操作日志
                                LogEntity logEntity = new LogEntity();
                                logEntity.LogDB = ConstDefine.UserManagementLog;
                                logEntity.LogTime = DateTime.Now;
                                logEntity.NodeID = updateItem.WindTurbineID;
                                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                                logEntity.OperationDescription =
                                    $"批量修改DAU网络配置_机组({updateItem.WindTurbineID})_DAU({updateItem.DauID})_IP({result.OriginalIP}->{updateItem.IP})_端口({result.OriginalPort}->{updateItem.Port})";
                                LogManagement.UserlogWrite(logEntity);
                            }
                            catch (Exception ex)
                            {
                                result.Success = false;
                                result.ErrorMessage = ex.Message;

                                CMSFramework.Logger.Logger.LogErrorMessage(
                                    $"[BatchUpdateDAUNetwork]更新DAU网络配置失败，机组: {updateItem.WindTurbineID}, DAU: {updateItem.DauID}", ex);
                            }

                        }

                        // 保存所有更改
                        ctx.SaveChanges();
                        tran.Commit();


                        return Ok(ApiResponse<string>.Success("OK"));

                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();

                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchUpdateDAUNetwork]批量修改DAU网络配置失败", ex);
                        return Ok(ApiResponse<string>.Error(ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除采集单元（全部成功或全部失败）
        /// </summary>
        /// <param name="deleteList"></param>
        /// <returns></returns>
        [HttpPost("BatchDeleteDAU")]
        public IActionResult BatchDeleteDAU([FromBody] List<DAUDeleteDTO> deleteList)
        {
            if (deleteList == null || deleteList.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var dto in deleteList)
                        {
                            var dau = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == dto.WindTurbineID && p.WindParkID == dto.WindParkID && p.DauID == dto.DauID);
                            if (dau == null)
                            {
                                errorMsgs.Add($"WindTurbineID={dto.WindTurbineID}, WindParkID={dto.WindParkID}, DauID={dto.DauID} 不存在，无法删除");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }
                        foreach (var dto in deleteList)
                        {
                            var dau = ctx.DAUnits.First(p => p.WindTurbineID == dto.WindTurbineID && p.WindParkID == dto.WindParkID && p.DauID == dto.DauID);

                            ctx.DAUnits.Remove(dau);

                            ctx.DAURotSpdChannels.RemoveRange(ctx.DAURotSpdChannels.Where(t => t.WindTurbineID == dto.WindTurbineID && t.DauID == dto.DauID));
                            ctx.DAUVibChannels.RemoveRange(ctx.DAUVibChannels.Where(t => t.WindTurbineID == dto.WindTurbineID && t.DauID == dto.DauID));
                            ctx.DAUProcessChannels.RemoveRange(ctx.DAUProcessChannels.Where(t => t.WindTurbineID == dto.WindTurbineID && t.DauID == dto.DauID));

                            ctx.DauChannelVoltageCurrents.RemoveRange(ctx.DauChannelVoltageCurrents.Where(t => t.WindTurbineID == dto.WindTurbineID && t.DauID == dto.DauID));
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量删除成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteDAU]批量删除采集单元失败", ex);
                        return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
                    }
                }
            }
        }

        #region 新增API方法

        /// <summary>
        /// 批量删除振动通道（原子性操作）
        /// </summary>
        /// <param name="request">批量删除振动通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteVibChannels")]
        [BatchOperation(nameof(BatchDeleteVibChannelsFun))]
        public IActionResult BatchDeleteVibChannels([FromBody] BatchDeleteVibChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var originalRecords = new List<DAUChannelV2>();
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                foreach (var channel in dto.Channels)
                {
                    var originalRecord = dauData.DAUChannelList.FirstOrDefault(c => c.ChannelNumber == channel.ChannelNumber);
                    if (originalRecord != null)
                    {
                        originalRecords.Add(originalRecord);
                    }
                }
            }
            HttpContext.Items["OriginalVibChannelRecords"] = originalRecords;

            try
            {
                // 预先检查所有通道是否被测量定义使用，确保原子性
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(dto.WindTurbineID);
                List<WaveDefinition> waveDefList = MeasDefinitionManagement.GetWaveDefByTurId(dto.WindTurbineID);

                // 先检查所有通道，如果有任何一个通道被使用，则全部失败
                foreach (var channel in dto.Channels)
                {
                    for (int i = 0; i < measDefList.Count; i++)
                    {
                        List<WaveDefinition> timeList = waveDefList.FindAll(item =>
                            item.MeasDefinitionID == measDefList[i].MeasDefinitionID &&
                            item.WaveFormType == EnumWaveFormType.WDF_Time);
                        if (timeList.Count() > 0 && timeList.Find(item => item.MeasLocationID == channel.MeasLocationID) != null)
                        {
                            return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义(时域)使用，批量删除失败！"));
                        }

                        List<WaveDefinition> envelopeList = waveDefList.FindAll(item =>
                            item.MeasDefinitionID == measDefList[i].MeasDefinitionID &&
                            item.WaveFormType == EnumWaveFormType.WDF_Envelope);
                        if (envelopeList.Count() > 0 && envelopeList.Find(item => item.MeasLocationID == channel.MeasLocationID) != null)
                        {
                            return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义(高频包络)使用，批量删除失败！"));
                        }
                    }
                }

                // 所有检查通过后，执行批量删除（原子性操作）
                List<int> channelNumbers = dto.Channels.Select(c => c.ChannelNumber).ToList();
                DauManagement.BatchDeleteVibChannels(dto.WindTurbineID, dto.DauID, channelNumbers);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量删除_DAU振动通道({string.Join(",", channelNumbers)})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量删除成功，共删除{channelNumbers.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteVibChannels]批量删除DAU振动通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量删除电流电压通道（原子性操作）
        /// </summary>
        /// <param name="request">批量删除电流电压通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteProcessChannels")]
        [BatchOperation(nameof(BatchDeleteProcessChannelsFun))]
        public IActionResult BatchDeleteProcessChannels([FromBody] BatchDeleteProcessChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var originalRecords = new List<DAUChannel_VoltageCurrent>();
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                foreach (var channel in dto.Channels)
                {
                    var originalRecord = dauData.VoltageCurrentList.FirstOrDefault(c => c.ChannelNumber == channel.ChannelNumber);
                    if (originalRecord != null)
                    {
                        originalRecords.Add(originalRecord);
                    }
                }
            }
            HttpContext.Items["OriginalProcessChannelRecords"] = originalRecords;

            try
            {
                // 预先检查所有通道是否被测量定义使用，确保原子性
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(dto.WindTurbineID);
                List<WaveDef_VoltageCurrent> waveDefList = MeasDefinitionManagement.GetWaveDefVoltageCurrentByTurId(dto.WindTurbineID);

                // 先检查所有通道，如果有任何一个通道被使用，则全部失败
                foreach (var channel in dto.Channels)
                {
                    for (int i = 0; i < measDefList.Count; i++)
                    {
                        List<WaveDef_VoltageCurrent> timeList = waveDefList.FindAll(item =>
                            item.MeasDefinitionID == measDefList[i].MeasDefinitionID &&
                            item.WaveFormType == EnumWaveFormType.WDF_Time);
                        if (timeList.Count() > 0 && timeList.Find(item => item.MeasLocationID == channel.MeasLocationID) != null)
                        {
                            return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义使用，批量删除失败！"));
                        }

                        List<WaveDef_VoltageCurrent> envelopeList = waveDefList.FindAll(item =>
                            item.MeasDefinitionID == measDefList[i].MeasDefinitionID &&
                            item.WaveFormType == EnumWaveFormType.WDF_Envelope);
                        if (envelopeList.Count() > 0 && envelopeList.Find(item => item.MeasLocationID == channel.MeasLocationID) != null)
                        {
                            return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义使用，批量删除失败！"));
                        }
                    }
                }

                // 所有检查通过后，执行批量删除（原子性操作）
                List<int> channelNumbers = dto.Channels.Select(c => c.ChannelNumber).ToList();
                DauManagement.BatchDeleteVoltageCurrentChannels(dto.WindTurbineID, dto.DauID, channelNumbers);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量删除_DAU电流电压通道({string.Join(",", channelNumbers)})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量删除成功，共删除{channelNumbers.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteProcessChannels]批量删除DAU电流电压通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加振动通道（原子性操作）
        /// </summary>
        /// <param name="request">批量添加振动通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddVibChannels")]
        [BatchOperation(nameof(BatchAddVibChannelsFunc))]
        public IActionResult BatchAddVibChannels([FromBody] BatchAddVibChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            try
            {
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                List<DAUChannelV2> channelsToAdd = new List<DAUChannelV2>();
                List<UltrasonicChannelConfig> ultrasonicChannelConfigs = new List<UltrasonicChannelConfig>();
                List<string> failedChannels = new List<string>();

                // 预先检查所有通道，确保原子性
                foreach (var channelDto in dto.Channels)
                {
                    string refMessage = "";
                    if (CheckChannelIsUsed(dto.WindTurbineID, channelDto.ChannelNumber, channelDto.MeasLocVibID, DAU, ref refMessage))
                    {
                        failedChannels.Add($"通道{channelDto.ChannelNumber}: {refMessage}");
                    }
                }

                // 如果有任何通道检查失败，则全部失败
                if (failedChannels.Count > 0)
                {
                    return Ok(ApiResponse<string>.Error($"批量添加失败：{string.Join("; ", failedChannels)}"));
                }

                // 准备所有要添加的数据
                foreach (var channelDto in dto.Channels)
                {
                    DAUChannelV2 channel = new DAUChannelV2
                    {
                        ChannelNumber = channelDto.ChannelNumber,
                        Coeff_a = channelDto.Coeff_a,
                        Coeff_b = 0,
                        WindTurbineID = dto.WindTurbineID,
                        MaxBiasVolt = channelDto.MaxBiasVolt,
                        MeasLocVibID = channelDto.MeasLocVibID,
                        MinBiasVolt = channelDto.MinBiasVolt,
                        S2S_Coeff_a = channelDto.S2S_Coeff_a ?? 0,
                        S2S_Coeff_b = channelDto.S2S_Coeff_b ?? 0,
                        PhysicalQuantityType = (CMSFramework.TypeDef.EnumPhysicalQuantityType)(channelDto.PhysicalQuantityType ?? 0),
                        RegisterAddress = channelDto.RegisterAddress ?? 0,
                        Coeff_L0 = channelDto.Coeff_L0 ?? 0,
                        DauID = dto.DauID
                    };

                    channelsToAdd.Add(channel);

                    // 准备超声波配置
                    if (DAU.DAUType == EnumDAUType.Ultrasonic || DAU.DAUType == EnumDAUType.IfastUltrasonic)
                    {
                        int dispatcherChannelID = 0;
                        int dispatcherID = 0;
                        if (DAU.DAUType == EnumDAUType.Ultrasonic)
                        {
                            dispatcherChannelID = Convert.ToInt32(channelDto.MeasLocVibID.Substring(channelDto.MeasLocVibID.Length - 1, 1));
                            dispatcherID = Convert.ToInt32(channelDto.MeasLocVibID.Substring(channelDto.MeasLocVibID.Length - 3, 1));
                        }
                        UltrasonicChannelConfig ultrasonic = new UltrasonicChannelConfig()
                        {
                            DauID = DAU.DauID,
                            ChannelNumber = channelDto.ChannelNumber,
                            WindTurbineID = dto.WindTurbineID,
                            PreloadLowerLimmit = 0,
                            PreloadUpperLimmit = 0,
                            PreloadCalCoeffs = "0",
                            TempCalibCoeff = 0,
                            DispatcherChannelID = dispatcherChannelID,
                            DispatcherID = dispatcherID,
                        };
                        ultrasonicChannelConfigs.Add(ultrasonic);
                    }
                }

                // 执行原子性批量添加
                if (channelsToAdd.Count > 0)
                {
                    DauManagement.BatchAddVibChannels(channelsToAdd, ultrasonicChannelConfigs);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量添加_DAU振动通道({channelsToAdd.Count}个)"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量添加成功，共添加{channelsToAdd.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddVibChannels]批量添加DAU振动通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加电流电压通道（原子性操作）
        /// </summary>
        /// <param name="request">批量添加电流电压通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddProcessChannels")]
        [BatchOperation(nameof(BatchAddProcessChannelsFun))]
        public IActionResult BatchAddProcessChannels([FromBody] BatchAddProcessChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            try
            {
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                List<DAUChannel_VoltageCurrent> channelsToAdd = new List<DAUChannel_VoltageCurrent>();
                List<string> failedChannels = new List<string>();

                // 预先检查所有通道，确保原子性
                foreach (var channelDto in dto.Channels)
                {
                    string refMessage = "";
                    if (CheckChannelIsUsed(dto.WindTurbineID, channelDto.ChannelNumber, channelDto.MeasLoc_ProcessId, DAU, ref refMessage))
                    {
                        failedChannels.Add($"通道{channelDto.ChannelNumber}: {refMessage}");
                    }
                }

                // 如果有任何通道检查失败，则全部失败
                if (failedChannels.Count > 0)
                {
                    return Ok(ApiResponse<string>.Error($"批量添加失败：{string.Join("; ", failedChannels)}"));
                }

                // 准备所有要添加的数据
                foreach (var channelDto in dto.Channels)
                {
                    DAUChannel_VoltageCurrent channel = new DAUChannel_VoltageCurrent
                    {
                        ChannelNumber = channelDto.ChannelNumber,
                        Coeff_a = channelDto.Coeff_a,
                        Coeff_b = channelDto.Coeff_b,
                        WindTurbineID = dto.WindTurbineID,
                        MeasLoc_ProcessId = channelDto.MeasLoc_ProcessId,
                        DauID = dto.DauID
                    };

                    channelsToAdd.Add(channel);
                }

                // 执行原子性批量添加
                if (channelsToAdd.Count > 0)
                {
                    DauManagement.BatchAddVoltageCurrentChannels(channelsToAdd);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量添加_DAU电流电压通道({channelsToAdd.Count}个)"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量添加成功，共添加{channelsToAdd.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddProcessChannels]批量添加DAU电流电压通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 编辑单个振动通道
        /// </summary>
        /// <param name="request">编辑振动通道请求</param>
        /// <returns></returns>
        [HttpPost("EditVibChannel")]
        [BatchOperation(nameof(BatchEditVibChannel))]
        public IActionResult EditVibChannel([FromBody] EditVibChannelRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                var originalRecord = dauData.DAUChannelList.FirstOrDefault(c => c.ChannelNumber == dto.ChannelNumber);
                if (originalRecord != null)
                {
                    HttpContext.Items["OriginalVibChannelRecord"] = originalRecord;
                }
            }

            try
            {
                string refMessage = "";
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                if (CheckChannelIsUsed(dto.WindTurbineID, dto.ChannelNumber, dto.MeasLocVibID, DAU, ref refMessage))
                {
                    return Ok(ApiResponse<string>.Error(refMessage));
                }

                DAUChannelV2 chan = DAU.DAUChannelList.Find(item => item.ChannelNumber == dto.ChannelNumber);
                if (chan == null)
                {
                    return Ok(ApiResponse<string>.Error("通道不存在"));
                }

                chan.ChannelNumber = dto.ChannelNumber;
                chan.Coeff_a = dto.Coeff_a;
                chan.Coeff_b = 0;
                chan.WindTurbineID = dto.WindTurbineID;
                chan.MaxBiasVolt = dto.MaxBiasVolt;
                chan.MeasLocVibID = dto.MeasLocVibID;
                chan.MinBiasVolt = dto.MinBiasVolt;
                chan.S2S_Coeff_a = dto.S2S_Coeff_a ?? 0;
                chan.S2S_Coeff_b = dto.S2S_Coeff_b ?? 0;
                chan.PhysicalQuantityType = (CMSFramework.TypeDef.EnumPhysicalQuantityType)(dto.PhysicalQuantityType ?? 0);
                chan.RegisterAddress = dto.RegisterAddress ?? 0;
                chan.Coeff_L0 = dto.Coeff_L0 ?? 0;

                DauManagement.EditDAUVibChannel(chan);

                // 更新超声波配置
                if (DAU.DAUType == EnumDAUType.Ultrasonic || DAU.DAUType == EnumDAUType.IfastUltrasonic)
                {
                    int dispatcherChannelID = 0;
                    int dispatcherID = 0;
                    if (DAU.DAUType == EnumDAUType.Ultrasonic)
                    {
                        dispatcherChannelID = Convert.ToInt32(dto.MeasLocVibID.Substring(dto.MeasLocVibID.Length - 1, 1));
                        dispatcherID = Convert.ToInt32(dto.MeasLocVibID.Substring(dto.MeasLocVibID.Length - 3, 1));
                    }
                    UltrasonicChannelConfig ultrasonic = new UltrasonicChannelConfig()
                    {
                        DauID = DAU.DauID,
                        ChannelNumber = dto.ChannelNumber,
                        WindTurbineID = dto.WindTurbineID,
                        PreloadLowerLimmit = 0,
                        PreloadUpperLimmit = 0,
                        PreloadCalCoeffs = "0",
                        TempCalibCoeff = 0,
                        DispatcherChannelID = dispatcherChannelID,
                        DispatcherID = dispatcherID,
                    };
                    DauManagement.EditUltrasonic(ultrasonic);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = dto.MeasLocVibID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = $"修改_DAU振动通道({dto.MeasLocVibID})";
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditVibChannel]编辑DAU振动通道失败", ex);
                return Ok(ApiResponse<string>.Error("编辑失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 编辑单个电流电压通道
        /// </summary>
        /// <param name="request">编辑电流电压通道请求</param>
        /// <returns></returns>
        [HttpPost("EditProcessChannel")]
        [BatchOperation(nameof(BatchEditProcessChannel))]
        public IActionResult EditProcessChannel([FromBody] EditProcessChannelRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                var originalRecord = dauData.VoltageCurrentList.FirstOrDefault(c => c.ChannelNumber == dto.ChannelNumber);
                if (originalRecord != null)
                {
                    HttpContext.Items["OriginalProcessChannelRecord"] = originalRecord;
                }
            }

            try
            {
                string refMessage = "";
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                if (CheckChannelIsUsed(dto.WindTurbineID, dto.ChannelNumber, dto.MeasLoc_ProcessId, DAU, ref refMessage))
                {
                    return Ok(ApiResponse<string>.Error(refMessage));
                }

                DAUChannel_VoltageCurrent chan = DAU.VoltageCurrentList.Find(item => item.ChannelNumber == dto.ChannelNumber);
                if (chan == null)
                {
                    return Ok(ApiResponse<string>.Error("通道不存在"));
                }

                chan.ChannelNumber = dto.ChannelNumber;
                chan.Coeff_a = dto.Coeff_a;
                chan.Coeff_b = dto.Coeff_b;
                chan.WindTurbineID = dto.WindTurbineID;
                chan.MeasLoc_ProcessId = dto.MeasLoc_ProcessId;

                DauManagement.EditDAUVoltageCurrentChannel(chan);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = dto.MeasLoc_ProcessId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription = $"修改_DAU电流电压通道({dto.MeasLoc_ProcessId})";
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditProcessChannel]编辑DAU电流电压通道失败", ex);
                return Ok(ApiResponse<string>.Error("编辑失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量删除工况通道（原子性操作）
        /// </summary>
        /// <param name="request">批量删除工况通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteWorkConditionChannels")]
        [BatchOperation(nameof(BatchDeleteWorkConditionChannels))]
        public IActionResult BatchDeleteWorkConditionChannels([FromBody] BatchDeleteWorkConditionChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var originalRecords = new List<DAUChannel_Process>();
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                foreach (var channel in dto.Channels)
                {
                    var originalRecord = dauData.ProcessChannelList.FirstOrDefault(c => c.ChannelNumber == channel.ChannelNumber);
                    if (originalRecord != null)
                    {
                        originalRecords.Add(originalRecord);
                    }
                }
            }
            HttpContext.Items["OriginalWorkConditionChannelRecords"] = originalRecords;

            try
            {
                // 预先检查所有通道是否被测量定义使用，确保原子性
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(dto.WindTurbineID);

                // 先检查所有通道，如果有任何一个通道被使用，则全部失败
                foreach (var channel in dto.Channels)
                {
                    for (int i = 0; i < measDefList.Count; i++)
                    {
                        List<MeasDef_Process> measLocList = MeasDefinitionManagement.GetMDFWorkCondLocListByMeasDefId(dto.WindTurbineID, measDefList[i].MeasDefinitionID);
                        for (int j = 0; j < measLocList.Count; j++)
                        {
                            if (measLocList[j].MeasLocationID == channel.MeasLocationID)
                            {
                                return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义使用，批量删除失败！"));
                            }
                        }
                    }
                }

                // 所有检查通过后，执行批量删除（原子性操作）
                List<int> channelNumbers = dto.Channels.Select(c => c.ChannelNumber).ToList();
                DauManagement.BatchDeleteWorkConditionChannels(dto.WindTurbineID, dto.DauID, channelNumbers);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量删除_DAU工况通道({string.Join(",", channelNumbers)})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量删除成功，共删除{channelNumbers.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteWorkConditionChannels]批量删除DAU工况通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加工况通道（原子性操作）
        /// </summary>
        /// <param name="request">批量添加工况通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddWorkConditionChannels")]
        [BatchOperation(nameof(BatchAddWorkConditionChannels))]
        public IActionResult BatchAddWorkConditionChannels([FromBody] BatchAddWorkConditionChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            try
            {
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                List<DAUChannel_Process> channelsToAdd = new List<DAUChannel_Process>();
                List<string> failedChannels = new List<string>();

                // 预先检查所有通道，确保原子性
                foreach (var channelDto in dto.Channels)
                {
                    string refMessage = "";
                    if (CheckChannelIsUsed(dto.WindTurbineID, channelDto.ChannelNumber, channelDto.MeasLoc_ProcessId, DAU, ref refMessage))
                    {
                        failedChannels.Add($"通道{channelDto.ChannelNumber}: {refMessage}");
                    }
                }

                // 如果有任何通道检查失败，则全部失败
                if (failedChannels.Count > 0)
                {
                    return Ok(ApiResponse<string>.Error($"批量添加失败：{string.Join("; ", failedChannels)}"));
                }

                // 准备所有要添加的数据
                foreach (var channelDto in dto.Channels)
                {
                    DAUChannel_Process channel = new DAUChannel_Process
                    {
                        DauID = dto.DauID,
                        ChannelNumber = channelDto.ChannelNumber,
                        Coeff_a = channelDto.PowerCoeff_a,
                        Coeff_b = channelDto.PowerCoeff_b,
                        WindTurbineID = dto.WindTurbineID,
                        MeasLoc_ProcessId = channelDto.MeasLoc_ProcessId
                    };

                    channelsToAdd.Add(channel);
                }

                // 执行原子性批量添加
                if (channelsToAdd.Count > 0)
                {
                    DauManagement.BatchAddWorkConditionChannels(channelsToAdd);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量添加_DAU工况通道({channelsToAdd.Count}个)"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量添加成功，共添加{channelsToAdd.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddWorkConditionChannels]批量添加DAU工况通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 编辑单个工况通道
        /// </summary>
        /// <param name="request">编辑工况通道请求</param>
        /// <returns></returns>
        [HttpPost("EditWorkConditionChannel")]
        [BatchOperation(nameof(BatchEditWorkConditionChannel))]
        public IActionResult EditWorkConditionChannel([FromBody] EditWorkConditionChannelRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                var originalRecord = dauData.ProcessChannelList.FirstOrDefault(c => c.ChannelNumber == dto.ChannelNumber);
                if (originalRecord != null)
                {
                    HttpContext.Items["OriginalWorkConditionChannelRecord"] = originalRecord;
                }
            }

            try
            {
                string refMessage = "";
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                if (CheckChannelIsUsed(dto.WindTurbineID, dto.ChannelNumber, dto.MeasLoc_ProcessId, DAU, ref refMessage))
                {
                    return Ok(ApiResponse<string>.Error(refMessage));
                }

                DAUChannel_Process channel = DauManagement.GetDAUChannelProcessListByDAUId(dto.WindTurbineID).Find(item => item.MeasLoc_ProcessId == dto.MeasLoc_ProcessId);
                if (channel == null)
                {
                    return Ok(ApiResponse<string>.Error("工况通道不存在"));
                }

                // 先删除原通道，再添加新通道（保持原有逻辑）
                DauManagement.DeleteProcessChan(dto.WindTurbineID, dto.DauID, channel.ChannelNumber);

                channel.DauID = dto.DauID;
                channel.ChannelNumber = dto.ChannelNumber;
                channel.Coeff_a = dto.PowerCoeff_a;
                channel.Coeff_b = dto.PowerCoeff_b;
                channel.WindTurbineID = dto.WindTurbineID;
                channel.MeasLoc_ProcessId = dto.MeasLoc_ProcessId;

                DauManagement.addDAUProcessChannel(channel);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.MeasLoc_ProcessId,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"修改_DAU工况通道({dto.MeasLoc_ProcessId})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWorkConditionChannel]编辑DAU工况通道失败", ex);
                return Ok(ApiResponse<string>.Error("编辑失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量删除转速通道（原子性操作）
        /// </summary>
        /// <param name="request">批量删除转速通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteRotSpeedChannels")]
        [BatchOperation(nameof(BatchDeleteRotSpeedChannelsFun))]
        public IActionResult BatchDeleteRotSpeedChannels([FromBody] BatchDeleteRotSpeedChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            var originalRecords = new List<DAUChannel_RotSpeed>();
            var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            if (dauData != null)
            {
                foreach (var channel in dto.Channels)
                {
                    var originalRecord = dauData.RotSpeedChannelList.FirstOrDefault(c => c.ChannelNumber == channel.ChannelNumber);
                    if (originalRecord != null)
                    {
                        originalRecords.Add(originalRecord);
                    }
                }
            }
            HttpContext.Items["OriginalRotSpeedChannelRecords"] = originalRecords;

            try
            {
                // 预先检查所有通道是否被测量定义使用，确保原子性
                List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(dto.WindTurbineID);

                // 先检查所有通道，如果有任何一个通道被使用，则全部失败
                foreach (var channel in dto.Channels)
                {
                    for (int i = 0; i < measDefList.Count; i++)
                    {
                        List<WaveDef_RotSpd> waveDefList = MeasDefinitionManagement.GetWaveDefListRotSpd(dto.WindTurbineID, measDefList[i].MeasDefinitionID);
                        for (int j = 0; j < waveDefList.Count; j++)
                        {
                            if (waveDefList[j].MeasLoc_RotSpdID == channel.MeasLocationID)
                            {
                                return Ok(ApiResponse<string>.Error($"通道{channel.ChannelNumber}正在被测量定义使用，批量删除失败！"));
                            }
                        }
                    }
                }

                // 所有检查通过后，执行批量删除（原子性操作）
                List<int> channelNumbers = dto.Channels.Select(c => c.ChannelNumber).ToList();
                DauManagement.BatchDeleteRotSpeedChannels(dto.WindTurbineID, dto.DauID, channelNumbers);

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量删除_DAU转速通道({string.Join(",", channelNumbers)})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量删除成功，共删除{channelNumbers.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteRotSpeedChannels]批量删除DAU转速通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加转速通道（原子性操作）
        /// </summary>
        /// <param name="request">批量添加转速通道请求</param>
        /// <returns></returns>
        [HttpPost("BatchAddRotSpeedChannels")]
        [BatchOperation(nameof(BatchAddRotSpeedChannels))]
        public IActionResult BatchAddRotSpeedChannels([FromBody] BatchAddRotSpeedChannelsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Channels == null || request.SourceData.Channels.Count == 0)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            try
            {
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                List<DAUChannel_RotSpeed> channelsToAdd = new List<DAUChannel_RotSpeed>();
                List<string> failedChannels = new List<string>();

                // 预先检查所有通道，确保原子性
                foreach (var channelDto in dto.Channels)
                {
                    string refMessage = "";
                    if (CheckChannelIsUsed(dto.WindTurbineID, channelDto.ChannelNumber, channelDto.MeasLocRotSpdID, DAU, ref refMessage))
                    {
                        failedChannels.Add($"通道{channelDto.ChannelNumber}: {refMessage}");
                    }
                }

                // 如果有任何通道检查失败，则全部失败
                if (failedChannels.Count > 0)
                {
                    return Ok(ApiResponse<string>.Error($"批量添加失败：{string.Join("; ", failedChannels)}"));
                }

                // 准备所有要添加的数据
                foreach (var channelDto in dto.Channels)
                {
                    DAUChannel_RotSpeed channel = new DAUChannel_RotSpeed
                    {
                        ChannelNumber = channelDto.ChannelNumber,
                        WindTurbineID = dto.WindTurbineID,
                        MeasLocRotSpdID = channelDto.MeasLocRotSpdID,
                        DauID = dto.DauID
                    };

                    channelsToAdd.Add(channel);
                }

                // 执行原子性批量添加
                if (channelsToAdd.Count > 0)
                {
                    DauManagement.BatchAddRotSpeedChannels(channelsToAdd);
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.WindTurbineID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"批量添加_DAU转速通道({channelsToAdd.Count}个)"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success($"批量添加成功，共添加{channelsToAdd.Count}个通道"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddRotSpeedChannels]批量添加DAU转速通道失败", ex);
                return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 编辑单个转速通道
        /// </summary>
        /// <param name="request">编辑转速通道请求</param>
        /// <returns></returns>
        [HttpPost("EditRotSpeedChannel")]
        [BatchOperation(nameof(BatchEditRotSpeedChannel))]
        public IActionResult EditRotSpeedChannel([FromBody] EditRotSpeedChannelRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var dto = request.SourceData;

            // 缓存原始记录信息，供批量操作使用
            //var dauData = DauManagement.GetDAUById(dto.WindTurbineID);
            //if (dauData != null)
            //{
            //    var originalRecord = dauData.RotSpeedChannelList.FirstOrDefault(c => c.ChannelNumber == dto.ChannelNumber);
            //    if (originalRecord != null)
            //    {
            //        HttpContext.Items["OriginalRotSpeedChannelRecord"] = originalRecord;
            //    }
            //}

            try
            {
                string refMessage = "";
                WindDAU DAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(dto.WindTurbineID, dto.DauID);
                if (DAU == null)
                {
                    return Ok(ApiResponse<string>.Error("采集单元不存在"));
                }

                if (CheckChannelIsUsed(dto.WindTurbineID, dto.ChannelNumber, dto.MeasLocRotSpdID, DAU, ref refMessage))
                {
                    return Ok(ApiResponse<string>.Error(refMessage));
                }

                List<DAUChannel_RotSpeed> rotChannelList = DAU.RotSpeedChannelList;

                // 如果无转速数据，就添加，否则就需要删除之前的转速数据，然后再添加（保持原有逻辑）
                if (rotChannelList == null || rotChannelList.Count == 0)
                {
                    DauManagement.AddDAURotSpeedChannel(new DAUChannel_RotSpeed()
                    {
                        ChannelNumber = dto.ChannelNumber,
                        WindTurbineID = dto.WindTurbineID,
                        MeasLocRotSpdID = dto.MeasLocRotSpdID,
                        DauID = dto.DauID
                    });
                }
                else
                {
                    foreach (DAUChannel_RotSpeed item in rotChannelList)
                    {
                        DauManagement.DeleteDAURotSpeedChannel(item);
                        item.ChannelNumber = dto.ChannelNumber;
                        item.MeasLocRotSpdID = dto.MeasLocRotSpdID;
                        DauManagement.AddDAURotSpeedChannel(item);
                    }
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity
                {
                    LogDB = ConstDefine.UserManagementLog,
                    LogTime = DateTime.Now,
                    NodeID = dto.MeasLocRotSpdID,
                    UserName = Request.Cookies["WindCMSUserName"],
                    OperationDescription = $"修改_DAU转速通道({dto.MeasLocRotSpdID})"
                };
                LogManagement.UserlogWrite(logEntity);
                #endregion

                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRotSpeedChannel]编辑DAU转速通道失败", ex);
                return Ok(ApiResponse<string>.Error("编辑失败: " + ex.Message));
            }
        }

        #endregion

        #region 批量操作方法

        /// <summary>
        /// 批量删除振动通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除振动通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteVibChannelsFun(string targetTurbineId, BatchDeleteVibChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                var originalRecords = HttpContext.Items["OriginalVibChannelRecords"] as List<DAUChannelV2>;
                if (originalRecords == null || originalRecords.Count == 0)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }

                // 获取当前的dau
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                if (targetDAU == null)
                {
                    results.Add($"未找到DAU信息");
                    return results;
                }

                // 为目标机组查找对应的通道
                var targetChannels = new List<VibChannelDeleteItem>();
                foreach (var originalRecord in originalRecords)
                {
                    // 获取原始测量位置信息
                    var originalMeasLoc = DevTreeManagement.GetVibMeasLocByID(originalRecord.MeasLocVibID);
                    if (originalMeasLoc == null) continue;

                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = DevTreeManagement.GetVibMeasLocationByTurId(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == originalMeasLoc.MeasLocName &&
                                           m.SectionName == originalMeasLoc.SectionName &&
                                           m.Orientation == originalMeasLoc.Orientation);

                    if (targetMeasLoc != null)
                    {
                        // 查找目标机组中对应的通道
                        var targetChannel = targetDAU.DAUChannelList.FirstOrDefault(c => c.MeasLocVibID == targetMeasLoc.MeasLocationID);
                        if (targetChannel != null)
                        {
                            targetChannels.Add(new VibChannelDeleteItem
                            {
                                ChannelNumber = targetChannel.ChannelNumber,
                                MeasLocationID = targetMeasLoc.MeasLocationID
                            });
                        }
                    }
                }

                if (targetChannels.Count == 0)
                {
                    results.Add($"未找到对应的振动通道");
                    return results;
                }

                // 执行删除操作
                var channelNumbers = targetChannels.Select(c => c.ChannelNumber).ToList();
                DauManagement.BatchDeleteVibChannels(targetTurbineId, request.SourceData.DauID, channelNumbers);

                results.Add($"成功删除 {channelNumbers.Count} 个振动通道");
            }
            catch (Exception ex)
            {
                results.Add($"删除失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量添加振动通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加振动通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddVibChannelsFunc(string targetTurbineId, BatchAddVibChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 检查目标机组的DAU是否存在
                var curDAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(request.SourceData.WindTurbineID, request.SourceData.DauID);
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                if (targetDAU == null)
                {
                    results.Add($"DAU不存在");
                    return results;
                }

                var channelsToAdd = new List<DAUChannelV2>();
                var ultrasonicChannelConfigs = new List<UltrasonicChannelConfig>();

                foreach (var channelDto in request.SourceData.Channels)
                {
                    // 在目标机组中查找对应的测量位置
                    var sourceMeasLoc = DevTreeManagement.GetVibMeasLocByID(channelDto.MeasLocVibID);
                    if (sourceMeasLoc == null) continue;

                    var targetMeasLoc = DevTreeManagement.GetVibMeasLocationByTurId(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName &&
                                           m.SectionName == sourceMeasLoc.SectionName &&
                                           m.Orientation == sourceMeasLoc.Orientation);

                    if (targetMeasLoc != null)
                    {
                        // 检查通道是否已存在
                        var targetDAUData = DauManagement.GetDAUById(targetTurbineId);
                        var existingChannel = targetDAUData?.DAUChannelList.FirstOrDefault(c => c.ChannelNumber == channelDto.ChannelNumber);
                        if (existingChannel == null)
                        {
                            var newChannel = new DAUChannelV2
                            {
                                WindTurbineID = targetTurbineId,
                                DauID = request.SourceData.DauID,
                                ChannelNumber = channelDto.ChannelNumber,
                                MeasLocVibID = targetMeasLoc.MeasLocationID,
                                Coeff_a = channelDto.Coeff_a,
                                MinBiasVolt = channelDto.MinBiasVolt,
                                MaxBiasVolt = channelDto.MaxBiasVolt,
                                PhysicalQuantityType = (CMSFramework.TypeDef.EnumPhysicalQuantityType)(channelDto.PhysicalQuantityType ?? 0),
                                S2S_Coeff_a = channelDto.S2S_Coeff_a ?? 0,
                                S2S_Coeff_b = channelDto.S2S_Coeff_b ?? 0,
                                RegisterAddress = channelDto.RegisterAddress ?? 0,
                                Coeff_L0 = channelDto.Coeff_L0 ?? 0
                            };
                            channelsToAdd.Add(newChannel);
                        }
                    }
                    else
                    {
                        results.Add($"测量位置{sourceMeasLoc.MeasLocName}不存在");
                    }
                }

                if (channelsToAdd.Count > 0)
                {
                    DauManagement.BatchAddVibChannels(channelsToAdd, ultrasonicChannelConfigs);
                    results.Add($"成功添加 {channelsToAdd.Count} 个振动通道");
                }
            }
            catch (Exception ex)
            {
                results.Add($"添加失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量编辑振动通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">编辑振动通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditVibChannel(string targetTurbineId, EditVibChannelRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                var originalRecord = HttpContext.Items["OriginalVibChannelRecord"] as DAUChannelV2;
                if (originalRecord == null)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }
                // 获取当前的dau
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                var targetMeaslocID = request.SourceData.MeasLocVibID.Replace(request.SourceData.WindTurbineID, targetTurbineId);
                var targetChannel = targetDAU?.DAUChannelList.FirstOrDefault(c => c.ChannelNumber == request.SourceData.ChannelNumber
                && c.MeasLocVibID == targetMeaslocID);
                if (targetChannel == null)
                {
                    results.Add($"未找到对应的振动通道");
                    return results;
                }
                else
                {
                    // 更新通道信息
                    //targetChannel.MeasLocVibID = targetMeasLoc;
                    targetChannel.Coeff_a = request.SourceData.Coeff_a;
                    targetChannel.MinBiasVolt = request.SourceData.MinBiasVolt;
                    targetChannel.MaxBiasVolt = request.SourceData.MaxBiasVolt;
                    targetChannel.PhysicalQuantityType = (CMSFramework.TypeDef.EnumPhysicalQuantityType)(request.SourceData.PhysicalQuantityType ?? 0);
                    targetChannel.S2S_Coeff_a = request.SourceData.S2S_Coeff_a ?? 0;
                    targetChannel.S2S_Coeff_b = request.SourceData.S2S_Coeff_b ?? 0;
                    targetChannel.RegisterAddress = request.SourceData.RegisterAddress ?? 0;
                    targetChannel.Coeff_L0 = request.SourceData.Coeff_L0 ?? 0;

                    DauManagement.EditDAUVibChannel(targetChannel);
                    results.Add($"成功编辑振动通道 {request.SourceData.ChannelNumber}");
                }
            }
            catch (Exception ex)
            {
                results.Add($"编辑失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除电流电压通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除电流电压通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteProcessChannelsFun(string targetTurbineId, BatchDeleteProcessChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                var originalRecords = HttpContext.Items["OriginalProcessChannelRecords"] as List<DAUChannel_VoltageCurrent>;
                if (originalRecords == null || originalRecords.Count == 0)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }

                // 获取目标机组的DAU数据
                var targetDAU = DauManagement.GetDAUById(targetTurbineId);
                if (targetDAU == null)
                {
                    results.Add($"未找到DAU信息");
                    return results;
                }

                // 为目标机组查找对应的通道
                var targetChannels = new List<ProcessChannelDeleteItem>();
                foreach (var originalRecord in originalRecords)
                {
                    // 获取原始测量位置信息
                    var originalMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(request.SourceData.WindTurbineID)
                        .FirstOrDefault(m => m.MeasLocationID == originalRecord.MeasLoc_ProcessId);
                    if (originalMeasLoc == null) continue;

                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == originalMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 查找目标机组中对应的通道
                        var targetChannel = targetDAU.VoltageCurrentList.FirstOrDefault(c => c.MeasLoc_ProcessId == targetMeasLoc.MeasLocationID);
                        if (targetChannel != null)
                        {
                            targetChannels.Add(new ProcessChannelDeleteItem
                            {
                                ChannelNumber = targetChannel.ChannelNumber,
                                MeasLocationID = targetMeasLoc.MeasLocationID
                            });
                        }
                    }
                }

                if (targetChannels.Count == 0)
                {
                    results.Add($"未找到对应的电流电压通道");
                    return results;
                }

                // 执行删除操作
                var channelNumbers = targetChannels.Select(c => c.ChannelNumber).ToList();
                // 注意：这里需要使用正确的删除方法，可能需要逐个删除
                foreach (var channelNumber in channelNumbers)
                {
                    DauManagement.DeleteVoltageCurrentChannelByDAUIDAndChannelNum(targetTurbineId, request.SourceData.DauID, channelNumber);
                }

                results.Add($"成功删除 {channelNumbers.Count} 个电流电压通道");
            }
            catch (Exception ex)
            {
                results.Add($"删除失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量添加电流电压通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加电流电压通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddProcessChannelsFun(string targetTurbineId, BatchAddProcessChannelsRequest request)
        {
            var results = new List<string>();
            try
            {
                // 检查目标机组的DAU是否存在
                // 获取当前的dau
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                if (targetDAU == null)
                {
                    results.Add($"DAU不存在");
                    return results;
                }

                var channelsToAdd = new List<DAUChannel_VoltageCurrent>();

                foreach (var channelDto in request.SourceData.Channels)
                {
                    var targetMeaslocID = channelDto.MeasLoc_ProcessId.Replace(request.SourceData.WindTurbineID, targetTurbineId);
                    var targetMeasLoc = DevTreeManagement.GetVoltageCurrentMeasLocByID(targetMeaslocID);

                    if (targetMeasLoc != null)
                    {
                        var existingChannel = targetDAU?.VoltageCurrentList.FirstOrDefault(c => c.ChannelNumber == channelDto.ChannelNumber);
                        if (existingChannel == null)
                        {
                            var newChannel = new DAUChannel_VoltageCurrent
                            {
                                WindTurbineID = targetTurbineId,
                                DauID = request.SourceData.DauID,
                                ChannelNumber = channelDto.ChannelNumber,
                                MeasLoc_ProcessId = targetMeasLoc.MeasLocationID,
                                Coeff_a = channelDto.Coeff_a,
                                Coeff_b = channelDto.Coeff_b
                            };
                            channelsToAdd.Add(newChannel);
                        }
                    }
                    else
                    {
                        results.Add($"不存在测量位置：{targetMeaslocID}");
                    }
                }

                if (channelsToAdd.Count > 0)
                {
                    // 逐个添加电流电压通道
                    foreach (var channel in channelsToAdd)
                    {
                        DauManagement.AddDAUVoltageCurrentChannel(channel);
                    }
                    results.Add($"成功添加 {channelsToAdd.Count} 个电流电压通道");
                }
            }
            catch (Exception ex)
            {
                results.Add($"添加失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量编辑电流电压通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">编辑电流电压通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditProcessChannel(string targetTurbineId, EditProcessChannelRequest request)
        {
            var results = new List<string>();
            try
            {
                // 获取原始记录信息
                var originalRecord = HttpContext.Items["OriginalProcessChannelRecord"] as DAUChannel_VoltageCurrent;
                if (originalRecord == null)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }
                var targetMeasLocID = request.SourceData.MeasLoc_ProcessId.Replace(request.SourceData.WindTurbineID, targetTurbineId);
                // 在目标机组中查找对应的通道
                // 获取当前的dau
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                var targetChannel = targetDAU?.VoltageCurrentList.FirstOrDefault(c => c.ChannelNumber == request.SourceData.ChannelNumber && c.MeasLoc_ProcessId == targetMeasLocID);
                if (targetChannel == null)
                {
                    results.Add($"未找到对应的电流电压通道");
                    return results;
                }
                else
                {
                    // 更新通道信息
                    //targetChannel.MeasLoc_ProcessId = targetMeasLocID;
                    targetChannel.Coeff_a = request.SourceData.Coeff_a;
                    targetChannel.Coeff_b = request.SourceData.Coeff_b;

                    DauManagement.EditDAUVoltageCurrentChannel(targetChannel);
                    results.Add($"成功编辑电流电压通道 {request.SourceData.ChannelNumber}");
                }

            }
            catch (Exception ex)
            {
                results.Add($"编辑失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除工况通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除工况通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWorkConditionChannels(string targetTurbineId, BatchDeleteWorkConditionChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                var originalRecords = HttpContext.Items["OriginalWorkConditionChannelRecords"] as List<DAUChannel_Process>;
                if (originalRecords == null || originalRecords.Count == 0)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }

                // 获取目标机组的DAU数据
                var targetDAU = DauManagement.GetDAUById(targetTurbineId);
                if (targetDAU == null)
                {
                    results.Add($"未找到DAU信息");
                    return results;
                }

                // 为目标机组查找对应的通道
                var targetChannels = new List<WorkConditionChannelDeleteItem>();
                foreach (var originalRecord in originalRecords)
                {
                    // 获取原始测量位置信息
                    var originalMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(request.SourceData.WindTurbineID)
                        .FirstOrDefault(m => m.MeasLocationID == originalRecord.MeasLoc_ProcessId);
                    if (originalMeasLoc == null) continue;

                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == originalMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 查找目标机组中对应的通道
                        var targetChannel = targetDAU.ProcessChannelList.FirstOrDefault(c => c.MeasLoc_ProcessId == targetMeasLoc.MeasLocationID);
                        if (targetChannel != null)
                        {
                            targetChannels.Add(new WorkConditionChannelDeleteItem
                            {
                                ChannelNumber = targetChannel.ChannelNumber,
                                MeasLocationID = targetMeasLoc.MeasLocationID
                            });
                        }
                    }
                }

                if (targetChannels.Count == 0)
                {
                    results.Add($"未找到对应的工况通道");
                    return results;
                }

                // 执行删除操作
                var channelNumbers = targetChannels.Select(c => c.ChannelNumber).ToList();
                // 逐个删除工况通道
                foreach (var channelNumber in channelNumbers)
                {
                    DauManagement.DeleteProcessChan(targetTurbineId, request.SourceData.DauID, channelNumber);
                }

                results.Add($"成功删除 {channelNumbers.Count} 个工况通道");
            }
            catch (Exception ex)
            {
                results.Add($"删除失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量添加工况通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加工况通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddWorkConditionChannels(string targetTurbineId, BatchAddWorkConditionChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 检查目标机组的DAU是否存在
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(targetTurbineId, request.SourceData.DauID);
                if (targetDAU == null)
                {
                    results.Add($"DAU不存在");
                    return results;
                }

                var channelsToAdd = new List<DAUChannel_Process>();

                foreach (var channelDto in request.SourceData.Channels)
                {
                    // 在目标机组中查找对应的测量位置
                    var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(request.SourceData.WindTurbineID)
                        .FirstOrDefault(m => m.MeasLocationID == channelDto.MeasLoc_ProcessId);
                    if (sourceMeasLoc == null) continue;

                    var targetMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 检查通道是否已存在
                        var targetDAUData = DauManagement.GetDAUById(targetTurbineId);
                        var existingChannel = targetDAUData?.ProcessChannelList.FirstOrDefault(c => c.ChannelNumber == channelDto.ChannelNumber);
                        if (existingChannel == null)
                        {
                            var newChannel = new DAUChannel_Process
                            {
                                WindTurbineID = targetTurbineId,
                                DauID = request.SourceData.DauID,
                                ChannelNumber = channelDto.ChannelNumber,
                                MeasLoc_ProcessId = targetMeasLoc.MeasLocationID,
                                Coeff_a = channelDto.PowerCoeff_a,
                                Coeff_b = channelDto.PowerCoeff_b
                            };
                            channelsToAdd.Add(newChannel);
                        }
                    }
                }

                if (channelsToAdd.Count > 0)
                {
                    // 逐个添加工况通道
                    foreach (var channel in channelsToAdd)
                    {
                        DauManagement.addDAUProcessChannel(channel);
                    }
                    results.Add($"成功添加 {channelsToAdd.Count} 个工况通道");
                }
                else
                {
                    results.Add($"没有需要添加的工况通道");
                }
            }
            catch (Exception ex)
            {
                results.Add($"添加失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量编辑工况通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">编辑工况通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditWorkConditionChannel(string targetTurbineId, EditWorkConditionChannelRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                var originalRecord = HttpContext.Items["OriginalWorkConditionChannelRecord"] as DAUChannel_Process;
                if (originalRecord == null)
                {
                    results.Add($"未找到原始记录信息");
                    return results;
                }

                // 在目标机组中查找对应的通道
                var targetDAU = DauManagement.GetDAUById(targetTurbineId);
                var targetChannel = targetDAU?.ProcessChannelList.FirstOrDefault(c => c.ChannelNumber == request.SourceData.ChannelNumber);
                if (targetChannel == null)
                {
                    results.Add($"未找到对应的工况通道");
                    return results;
                }

                // 在目标机组中查找对应的测量位置
                var sourceMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(request.SourceData.WindTurbineID)
                    .FirstOrDefault(m => m.MeasLocationID == request.SourceData.MeasLoc_ProcessId);
                if (sourceMeasLoc != null)
                {
                    var targetMeasLoc = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 更新通道信息
                        targetChannel.MeasLoc_ProcessId = targetMeasLoc.MeasLocationID;
                        targetChannel.Coeff_a = request.SourceData.PowerCoeff_a;
                        targetChannel.Coeff_b = request.SourceData.PowerCoeff_b;

                        DauManagement.editDAUProcessChannel(targetChannel);
                        results.Add($"成功编辑工况通道 {request.SourceData.ChannelNumber}");
                    }
                    else
                    {
                        results.Add($"未找到对应的测量位置");
                    }
                }
                else
                {
                    results.Add($"未找到源测量位置");
                }
            }
            catch (Exception ex)
            {
                results.Add($"编辑失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除转速通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除转速通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteRotSpeedChannelsFun(string targetTurbineId, BatchDeleteRotSpeedChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 获取原始记录信息
                //var originalRecords = HttpContext.Items["OriginalRotSpeedChannelRecords"] as List<DAUChannel_RotSpeed>;
                //if (originalRecords == null || originalRecords.Count == 0)
                //{
                //    results.Add($"未找到原始记录信息");
                //    return results;
                //}

                // 获取目标机组的DAU数据
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);
                if (targetDAU == null)
                {
                    results.Add($"未找到DAU:{curDAU.DAUName}信息");
                    return results;
                }

                // 为目标机组查找对应的通道
                var targetChannels = new List<RotSpeedChannelDeleteItem>();
                foreach (var originalRecord in request.SourceData.Channels)
                {
                    // 获取原始测量位置信息
                    var originalMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(originalRecord.MeasLocationID);
                    if (originalMeasLoc == null) continue;

                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocListByTurId(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == originalMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 查找目标机组中对应的通道
                        var targetChannel = targetDAU.RotSpeedChannelList.FirstOrDefault(c => c.MeasLocRotSpdID == targetMeasLoc.MeasLocationID);
                        if (targetChannel != null)
                        {
                            targetChannels.Add(new RotSpeedChannelDeleteItem
                            {
                                ChannelNumber = targetChannel.ChannelNumber,
                                MeasLocationID = targetMeasLoc.MeasLocationID
                            });
                        }
                    }
                }

                if (targetChannels.Count == 0)
                {
                    results.Add($"未找到对应的转速通道");
                    return results;
                }

                // 执行删除操作
                var channelNumbers = targetChannels.Select(c => c.ChannelNumber).ToList();
                // 执行删除操作
                DauManagement.BatchDeleteRotSpeedChannels(targetTurbineId, request.SourceData.DauID, channelNumbers);

                results.Add($"成功删除 {channelNumbers.Count} 个转速通道");
            }
            catch (Exception ex)
            {
                results.Add($"删除失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量添加转速通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加转速通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddRotSpeedChannels(string targetTurbineId, BatchAddRotSpeedChannelsRequest request)
        {
            var results = new List<string>();

            try
            {
                // 检查目标机组的DAU是否存在
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauId(targetTurbineId, request.SourceData.DauID);
                if (targetDAU == null)
                {
                    results.Add($"DAU不存在");
                    return results;
                }

                var channelsToAdd = new List<DAUChannel_RotSpeed>();

                foreach (var channelDto in request.SourceData.Channels)
                {
                    // 在目标机组中查找对应的测量位置
                    var sourceMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(channelDto.MeasLocRotSpdID);
                    if (sourceMeasLoc == null) continue;

                    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocListByTurId(targetTurbineId)
                        .FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                    if (targetMeasLoc != null)
                    {
                        // 检查通道是否已存在
                        var targetDAUData = DauManagement.GetDAUById(targetTurbineId);
                        var existingChannel = targetDAUData?.RotSpeedChannelList.FirstOrDefault(c => c.ChannelNumber == channelDto.ChannelNumber);
                        if (existingChannel == null)
                        {
                            var newChannel = new DAUChannel_RotSpeed
                            {
                                WindTurbineID = targetTurbineId,
                                DauID = request.SourceData.DauID,
                                ChannelNumber = channelDto.ChannelNumber,
                                MeasLocRotSpdID = targetMeasLoc.MeasLocationID
                            };
                            channelsToAdd.Add(newChannel);
                        }
                    }
                }

                if (channelsToAdd.Count > 0)
                {
                    // 批量添加转速通道
                    DauManagement.BatchAddRotSpeedChannels(channelsToAdd);
                    results.Add($"成功添加 {channelsToAdd.Count} 个转速通道");
                }
                else
                {
                    results.Add($"没有需要添加的转速通道");
                }
            }
            catch (Exception ex)
            {
                results.Add($"添加失败 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量编辑转速通道到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">编辑转速通道请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditRotSpeedChannel(string targetTurbineId, EditRotSpeedChannelRequest request)
        {
            var results = new List<string>();

            try
            {
                var targetMeasLoc = request.SourceData.MeasLocRotSpdID.Replace(request.SourceData.WindTurbineID, targetTurbineId);
                // 在目标机组中查找对应的通道
                var curDAU = DauManagement.GetDAUNameById(request.SourceData.WindTurbineID, request.SourceData.DauID);
                // 获取目标机组的DAU数据
                var targetDAU = DAUSManageModel.GetDAUByTrubineIdAndDauName(targetTurbineId, curDAU.DAUName);

                //var targetChannel = targetDAU?.RotSpeedChannelList.FirstOrDefault(c => c.ChannelNumber == request.SourceData.ChannelNumber
                //&& c.MeasLocRotSpdID == targetMeasLoc);

                // 转速只通过测量位置查询，通道已被编辑，无法查找
                var targetChannel = targetDAU?.RotSpeedChannelList.FirstOrDefault(c=>c.MeasLocRotSpdID == targetMeasLoc);
                if (targetChannel == null)
                {
                    results.Add($"未找到对应的转速通道");
                    return results;
                }
                else
                {
                    // 删除旧通道，添加新通道（保持原有逻辑）
                    DauManagement.DeleteDAURotSpeedChannel(targetChannel);
                    targetChannel.MeasLocRotSpdID = targetMeasLoc;
                    targetChannel.ChannelNumber = request.SourceData.ChannelNumber;
                    targetChannel.DauID = targetDAU.DauID;
                    targetChannel.WindTurbineID = targetTurbineId;

                    DauManagement.AddDAURotSpeedChannel(targetChannel);
                    results.Add($"成功编辑转速通道 {request.SourceData.ChannelNumber}");
                }

                // 在目标机组中查找对应的测量位置,以下逻辑为严格按照测量位置名称修改
                //var sourceMeasLoc = DevTreeManagement.GetRotSpdMeasLocation(request.SourceData.MeasLocRotSpdID);
                //if (sourceMeasLoc != null)
                //{
                //    var targetMeasLoc = DevTreeManagement.GetRotSpdMeasLocListByTurId(targetTurbineId)
                //        .FirstOrDefault(m => m.MeasLocName == sourceMeasLoc.MeasLocName);

                //    if (targetMeasLoc != null)
                //    {
                //        // 删除旧通道，添加新通道（保持原有逻辑）
                //        DauManagement.DeleteDAURotSpeedChannel(targetChannel);
                //        targetChannel.MeasLocRotSpdID = targetMeasLoc.MeasLocationID;
                //        DauManagement.AddDAURotSpeedChannel(targetChannel);
                //        results.Add($"成功编辑转速通道 {request.SourceData.ChannelNumber}");
                //    }
                //    else
                //    {
                //        results.Add($"未找到对应的测量位置");
                //    }
                //}
                //else
                //{
                //    results.Add($"未找到源测量位置");
                //}


            }
            catch (Exception ex)
            {
                results.Add($"编辑失败 - {ex.Message}");
            }

            return results;
        }

        #endregion

    }

    // 辅助类定义
    public class VibChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    public class ProcessChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    public class WorkConditionChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }

    public class RotSpeedChannelDeleteItem
    {
        public int ChannelNumber { get; set; }
        public string MeasLocationID { get; set; }
    }
}
