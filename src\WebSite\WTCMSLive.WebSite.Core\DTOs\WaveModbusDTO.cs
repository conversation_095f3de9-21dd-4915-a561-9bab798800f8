﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class WaveModbusDTO
    {
        public string WindTurbineID { get; set; }
        public string? MeasDefinitionID { get; set; }
        public string WaveDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public int SingleType { get; set; }
        public string WaveDefinitionName { get; set; }
        public decimal SampleRate { get; set; }
        public decimal SampleLength { get; set; }

        public List<string> evLists { get; set; }
        public int ModbusDeviceID { get; set; }

    }
}
