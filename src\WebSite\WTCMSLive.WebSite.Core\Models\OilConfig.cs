﻿using CMSFramework.BusinessEntity;
using Dapper;
using MySql.Data.MySqlClient;
using System.Data;
using WTCMSLive.WebSite.Helpers;

namespace WTCMSLive.WebSite.Models
{
    public class OilConfig
    {

        // 删除油液配置相关
        public static void DelOilBasic(string turbineID)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var oil = ctx.SerialServers.FirstOrDefault(item => item.WindTurbineID == turbineID);
                var oil2 = ctx.ModbusUnits.FirstOrDefault(item => item.WindTurbineID == turbineID);
                //oilnuit查询配置。
                var oil3 = ctx.OilUnits.FirstOrDefault(item => item.WindTurbineID == turbineID);

                if (oil != null)
                {
                    ctx.SerialServers.Remove(oil);
                }

                if (oil2 != null)
                {
                    ctx.ModbusUnits.Remove(oil2);
                }

                if (oil3 != null)
                {
                    ctx.OilUnits.Remove(oil3);
                }
                ctx.SaveChanges();
            }
        }

        // 删除油液配置相关
        public static void DelOilConfig(string turbineID)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                // 油液磨粒区间配置
                var oilConfig = ctx.OilWearParticleIntervals.Where(item => item.WindTurbineID == turbineID).ToList();
                if (oilConfig.Count > 0)
                {

                    ctx.OilWearParticleIntervals.RemoveRange(oilConfig);
                    ctx.SaveChanges();
                }
            }

            try
            {
                // 油液组合配置
                IDbConnection conn = new MySqlConnection(MySqlConnect._MysqlBaseDB);
                string delsql = $"DELETE FROM oilanalyzeconfig WHERE WindTurbineID = '{turbineID}'";
                conn.Execute(delsql);
            }
            catch (Exception ex)
            {

            }
        }

        // 删除modbus相关
        public static void DelModbus(string turbineID)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                var list = ctx.MeasDefinitions_Exs.Where(item => item.WindTurbineID == turbineID).ToList();

                var mdfmodbus = ctx.ModbusDefs.Where(item => item.WindTurbineID == turbineID).ToList();

                if (list != null)
                {
                    ctx.MeasDefinitions_Exs.RemoveRange(list);
                }

                if (mdfmodbus != null)
                {
                    ctx.ModbusDefs.RemoveRange(mdfmodbus);
                }

                ctx.SaveChanges();
            }
        }

        // 查询机组DAU列表
        public List<WindDAU> GetDAUList(string TurbineID)
        {

            var daulist = new List<WindDAU>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                daulist = ctx.DAUnits.Where(item => item.WindTurbineID == TurbineID).ToList();
            }
            return daulist;
        }

        // 查询机组油液信息
        public Object GetOilUnitById(string TurbineID)
        {
            List<MyValue> oilInfo = new List<MyValue>();
            OilDataObj oilInfos = new OilDataObj();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var oil = ctx.SerialServers.FirstOrDefault(item => item.WindTurbineID == TurbineID);
                var oil2 = ctx.ModbusUnits.FirstOrDefault(item => item.WindTurbineID == TurbineID);
                //oilnuit查询配置。
                var oil3 = ctx.OilUnits.FirstOrDefault(item => item.WindTurbineID == TurbineID);
                //获得方式固件暂无实现DAU方式，故获取方式都为串口服务器。
                oilInfo.Add(new MyValue() { key = "获取方式", value = "串口服务器" });
                oilInfos.Type = "串口服务器";
                if (oil != null)
                {
                    oilInfo.Add(new MyValue() { key = "Ip", value = oil.SerialServerIP });
                    oilInfo.Add(new MyValue() { key = "Port", value = oil.SerialServerPort.ToString() });
                    oilInfos.Ip = oil.SerialServerIP;
                    oilInfos.Port = oil.SerialServerPort.ToString();
                }
                if (oil2 != null) {
                    oilInfo.Add(new MyValue() { key = "Modbus", value = oil2.ModbusUnitID });
                    oilInfos.Modbus = oil2.ModbusUnitID;
                }
                if(oil3 != null)
                {
                    List<string> useList = new List<string>();
                    if (oil3.ViscositySensorEnabled) useList.Add("粘度");
                    if (oil3.WaterSensorEnabled) useList.Add("水分");
                    if (oil3.WearParticleSensorEnabled) useList.Add("磨粒");

                    oilInfos.ViscositySensorEnabled = oil3.ViscositySensorEnabled;
                    oilInfos.WaterSensorEnabled = oil3.WaterSensorEnabled;
                    oilInfos.WearParticleSensorEnabled = oil3.WearParticleSensorEnabled;

                    oilInfo.Add(new MyValue()
                    {
                        key="开启功能",
                        value = String.Join(",", useList.ToArray())
                    });
                    oilInfos.Action = String.Join(",", useList.ToArray());
                }
            }
            return oilInfos;
        }

        private class OilDataObj
        {
            public string Type { get; set; }
            public string Ip { get; set; }
            public string Port { get; set; }
            public string Modbus { get; set; }
            public string Action { get; set; }
            public bool ViscositySensorEnabled { get; set; }
            public bool WaterSensorEnabled { get; set; }
            public bool WearParticleSensorEnabled { get; set; }
        }

        public OilUnit GetOilUnit(string TurbineID) {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName)) {
                // 查询 oilunit
                var oilUnit = ctx.OilUnits.FirstOrDefault(o => o.WindTurbineID == TurbineID);
                if (oilUnit != null)
                {
                    return oilUnit;
                }
                return null;
            }
        }

        // 查询机组油液配置信息
        public List<OilWearParticleInterval>  GetOilConfig(string TurbineID)
        {
            var oilConfig = new List<OilWearParticleInterval>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
              oilConfig = ctx.OilWearParticleIntervals.Where(item => item.WindTurbineID == TurbineID).ToList();
            }
            return oilConfig;
        }

        private class MyValue
        {
            public string key { get; set; }
            public string value { get; set; }
        }
        // 保存油液信息更改
        public Boolean SaveOilUnit(string TurbineID, string dauID,string modbusAddr, string ip, int port, string type, int WearParticleSensorEnabled, int ViscositySensorEnabled, int WaterSensorEnabled)
        {

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                try
                {
                    // 保存oilunit OilUnitID自动生成！！
                    #region OilUnit
                    var myOilUnit = ctx.OilUnits.FirstOrDefault(i => i.WindTurbineID == TurbineID);
                    if (myOilUnit != null)
                    {
                        ctx.OilUnits.Remove(myOilUnit);
                        
                    }
                    //else
                    //{
                    //    myOilUnit.WearParticleSensorEnabled = Convert.ToBoolean(WearParticleSensorEnabled);
                    //    myOilUnit.ViscositySensorEnabled = Convert.ToBoolean(ViscositySensorEnabled);
                    //    myOilUnit.WaterSensorEnabled = Convert.ToBoolean(WaterSensorEnabled);
                    //}
                    var myOilUnitOption = new OilUnit { WindTurbineID = TurbineID, OilUnitID = modbusAddr, WearParticleSensorEnabled = Convert.ToBoolean(WearParticleSensorEnabled), ViscositySensorEnabled = Convert.ToBoolean(ViscositySensorEnabled), WaterSensorEnabled = Convert.ToBoolean(WaterSensorEnabled) };
                    ctx.OilUnits.Add(myOilUnitOption);
                    #endregion

                    #region ModbusUnit
                    ModbusUnit MyModbusUnit = ctx.ModbusUnits.FirstOrDefault(p => p.WindTurbineID == TurbineID);
                    if (MyModbusUnit != null)
                    {
                        // 添加
                        //ctx.ModbusUnits.Add(modbus);
                        ctx.ModbusUnits.Remove(MyModbusUnit);
                    }
                    //else
                    //{
                    //    //修改
                    //    MyModbusUnit.DauID = dauID;
                    //    MyModbusUnit.ModbusUnitID = modbusAddr;
                    //}
                    var modbus = new ModbusUnit { WindTurbineID = TurbineID, CommunicationMode = EnumCommunicationMode.BySerialServer, ModbusUnitID = modbusAddr, DauID = dauID,ModbusDevType = EnumModbusDevType.DynamicSVM };
                    ctx.ModbusUnits.Add(modbus);
                    #endregion

                    #region SerialServers
                    if (type == "Series")
                    {
                        //SerialServer MySerialServer = ctx.SerialServers.FirstOrDefault(p => p.WindTurbineID == TurbineID && p.ModbusUnitID == modbusAddr);
                        SerialServer MySerialServer = ctx.SerialServers.FirstOrDefault(p => p.WindTurbineID == TurbineID );
                        if (MySerialServer != null)
                        {
                            // 添加
                            //var serial = new SerialServer { WindTurbineID = TurbineID, ModbusUnitID = modbusAddr, SerialServerIP = ip, SerialServerPort = port };
                            //ctx.SerialServers.Add(serial);
                            ctx.SerialServers.Remove(MySerialServer);
                        }
                        //else
                        //{
                        //    //修改
                        //    MySerialServer.ModbusUnitID = modbusAddr;
                        //    MySerialServer.SerialServerIP = ip;
                        //    MySerialServer.SerialServerPort = port;
                        //}
                        var serial = new SerialServer { WindTurbineID = TurbineID, ModbusUnitID = modbusAddr, SerialServerIP = ip, SerialServerPort = port };
                        ctx.SerialServers.Add(serial);
                    }

                    // type == DAUID 固件暂不支持
                    #endregion
                    ctx.SaveChanges();
                    return true;
                }
            catch (Exception ex)
            {
                return false;
            }
        }
    }

        // 保存油液区间信息
        public Boolean SaveOilSection(string TurbineID, string[] datalist)
            {
                using ( CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    try
                    {
                        // 保存铁磁
                        // 获取OilUnit ID  
                        var _oilID = ctx.OilUnits.FirstOrDefault(o => o.WindTurbineID == TurbineID);

                        if (_oilID == null) {
                            return false;
                        }
                        string oilID = _oilID.OilUnitID;
                        // 1. 判断 是否存在
                        OilWearParticleInterval ferdata = ctx.OilWearParticleIntervals.FirstOrDefault(e => e.WindTurbineID == TurbineID && e.WearParticleType == EnumWearParticleType.Ferromagnetic);
                        if (ferdata == null)
                        {
                            // 添加
                            ferdata = new OilWearParticleInterval {
                                WindTurbineID = TurbineID,
                                OilUnitID = oilID,
                                WearParticleType = EnumWearParticleType.Ferromagnetic,
                                IntervalNumber = 3,
                                Boundary1 =  Convert.ToInt32(datalist[0] == "" ? "0" : datalist[0]),
                                Boundary2 = Convert.ToInt32(datalist[1] == "" ? "0" : datalist[1]),
                                Boundary3 = Convert.ToInt32(datalist[2]== "" ? "0" : datalist[2]),
                                Boundary4 = Convert.ToInt32(datalist[3] == "" ? "0" : datalist[3]),
                            };
                            ctx.OilWearParticleIntervals.Add(ferdata);
                        }
                        else {
                            // 更新
                            //ferdata.OilUnitID = oilID;
                            ferdata.Boundary1 = Convert.ToInt32(datalist[0] == "" ? "0" : datalist[0]);
                            ferdata.Boundary2 = Convert.ToInt32(datalist[1] == "" ? "0" : datalist[1]);
                            ferdata.Boundary3 = Convert.ToInt32(datalist[2] == "" ? "0" : datalist[2]);
                            ferdata.Boundary4 = Convert.ToInt32(datalist[3] == "" ? "0" : datalist[3]);
                            //ctx.SaveChanges();
                        }
                        // 保存非铁磁
                        // 1. 判断 是否存在
                        OilWearParticleInterval nonFdata = ctx.OilWearParticleIntervals.FirstOrDefault(e => e.WindTurbineID == TurbineID && e.WearParticleType == EnumWearParticleType.NonFerromagnetic);
                        if (nonFdata == null)
                        {
                        // 添加
                        nonFdata = new OilWearParticleInterval
                        {
                            WindTurbineID = TurbineID,
                            OilUnitID = (Convert.ToInt32(oilID) + 1).ToString(),
                                WearParticleType = EnumWearParticleType.NonFerromagnetic,
                                IntervalNumber = 3,
                                Boundary1 = Convert.ToInt32(datalist[4] == "" ? "0" : datalist[4]),
                                Boundary2 = Convert.ToInt32(datalist[5] == "" ? "0" : datalist[5]),
                                Boundary3 = Convert.ToInt32(datalist[6] == "" ? "0" : datalist[6]),
                                Boundary4 = Convert.ToInt32(datalist[7] == "" ? "0" : datalist[7]),
                            };
                            ctx.OilWearParticleIntervals.Add(nonFdata);
                        }
                        else
                        {
                            // 更新
                            //nonFdata.OilUnitID = oilID;
                            nonFdata.Boundary1 = Convert.ToInt32(datalist[4] == "" ? "0" : datalist[4]);
                            nonFdata.Boundary2 = Convert.ToInt32(datalist[5] == "" ? "0" : datalist[5]);
                            nonFdata.Boundary3 = Convert.ToInt32(datalist[6] == "" ? "0" : datalist[6]);
                            nonFdata.Boundary4 = Convert.ToInt32(datalist[7] == "" ? "0" : datalist[7]);
                            //ctx.SaveChanges();
                        }

                        ctx.SaveChanges();
                        return true;
                    }
                    catch (Exception ex)
                {
                    return false;

                }
            }
            }


        public class OilDataList
        {
            public string name { get; set; }
            public string value { get; set; }
        }

    }
}