﻿using CMSFramework.BusinessEntity.SVM;
using System.Collections.Generic;
using AppFramework.Utility;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 晃度仪公共参考数据
    /// </summary>
    public class SVMCommonRefData
    {
        /// <summary>
        /// 获取晃度仪特征值类型
        /// </summary>
        /// <returns></returns>
        public static List<KeyValuePair<string, int>> GetSVMEigenValueType()
        {
                return new List<KeyValuePair<string, int>> { 
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.MaxValue), (int)EnumSVMEigenValueType.MaxValue),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.MinValue), (int)EnumSVMEigenValueType.MinValue),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.AvgValue), (int)EnumSVMEigenValueType.AvgValue),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.SwingAngle), (int)EnumSVMEigenValueType.SwingAngle),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.ACCRMS), (int)EnumSVMEigenValueType.ACCRMS),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.NaturalFrequency), (int)EnumSVMEigenValueType.NaturalFrequency),
                    new KeyValuePair<string, int>(EnumHelper.GetDescription(EnumSVMEigenValueType.NFAmplitude), (int)EnumSVMEigenValueType.NFAmplitude),
                };
        }
    }
}
