﻿using CMSFramework.BusinessEntity;
using CMSFramework.FSDB;
using CMSFramework.EF;
using DataSecurity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.WebSite.Controllers
{
    public partial class WaveController : Controller
    {
        #region 文件获取波形信息
        /// <summary>
        /// 获取转速波形数组
        /// </summary>
        /// <param name="_waveDataPath"></param>
        /// <returns></returns>
        private byte[] GetRotSpdWaveBytes(RotSpeedWaveData waveFormData)
        {
            using (IFSDBStorage firebird = FSDBStorageFactory.GetFSDBStorage(waveFormData.WaveDataPath))
            {
                return firebird.GetRotSpdWave();
            }
        }
        private double[] GetWkWaveBytes(WorkConditionWaveFormData waveFormData)
        {
            using (IFSDBStorage firebird = FSDBStorageFactory.GetFSDBStorage(waveFormData.WaveDataPath))
            {
                byte[] bytedata = firebird.GetWorkConditionWaveFormData((int)waveFormData.Param_Type_Code);
                // float
                float[] dWaveData = new float[bytedata.Length / sizeof(float)];
                Buffer.BlockCopy(bytedata, 0, dWaveData, 0, bytedata.Length);


                //List<float> list = new List<float>();
                //for (int i = 0; i < bytedata.Length; i += 4)
                //{
                //    list.Add(ConvertToFloat(bytedata, i));
                //}

                //// double
                //double[] db = new double[dWaveData.Length];
                //return list;

                // double
                double[] db = new double[dWaveData.Length];

                for (int inx = 0; inx < dWaveData.Length; inx++)
                {
                    //ConvertCoefficient  转换系数，如果波形类型是电压电流的，需要做转换，如果是其他类型的 ConvertCoefficient=1
                    db[inx] = dWaveData[inx];
                }

                return db;

            }
        }

        public static float ConvertToFloat(byte[] data, int index)
        {
            var nTemp = new float[1];

            Buffer.BlockCopy(data, index, nTemp, 0, 4);

            return nTemp[0];
        }
        /// <summary>
        /// 获取波形数组
        /// </summary>
        /// <param name="waveFormData"></param>
        /// <returns></returns>
        private double[] GetWaveDataDouble(VibWaveFormData waveFormData)
        {
            string unit = UnitConfig.GetUnitNameBySignalType(waveFormData.SignalType);

            using (IFSDBStorage firebird = FSDBStorageFactory.GetFSDBStorage(waveFormData.WaveDataPath))
            {
                byte[] bytedata = firebird.GetVibWave(int.Parse(waveFormData.WaveDefinitionID));
                
                if(bytedata != null)
                {
                    bytedata = DataEncryption.DecryptData(bytedata);
                }
                // float
                float[] dWaveData = new float[bytedata.Length / sizeof(float)];
                Buffer.BlockCopy(bytedata, 0, dWaveData, 0, bytedata.Length);

                // double
                double[] db = new double[dWaveData.Length];

                for (int inx = 0; inx < dWaveData.Length; inx++)
                {
                    //ConvertCoefficient  转换系数，如果波形类型是电压电流的，需要做转换，如果是其他类型的 ConvertCoefficient=1
                    db[inx] = dWaveData[inx];

                    // 加速度可能需要改变单位 * waveFormData.ConvertCoefficient
                    //如果信号类型是加速度，且单位是g，则需要做单位转换
                    if (waveFormData.SignalType == "0" && unit == "g")
                    {
                        db[inx] = dWaveData[inx] / 9.8;
                    }
                }

                return db;
            }
        }
        #endregion

        #region 数据库获取波形信息
        /// <summary>
        /// 波形实时获取数据
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="_measLocID"></param>
        /// <param name="_WFType"></param>
        /// <returns></returns>
        private VibWaveFormData GetRealWaveFromData(string windTurbineId, string _measLocID, EnumWaveFormType _WFType)
        {
            VibWaveFormData waveFormData = null;
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.RealTimeDB, ConfigInfo.DBConnName))
            {
                waveFormData = ctx.VibWaveForms.AsNoTracking().FirstOrDefault(wf =>
                    wf.WindTurbineID == windTurbineId && wf.MeasLocationID == _measLocID);
            }
            return waveFormData;
        }
        /// <summary>
        /// 波形趋势获取数据
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="_measLocID"></param>
        /// <param name="_WFType"></param>
        /// <param name="_acqTime"></param>
        /// <returns></returns>
        private VibWaveFormData GetDayDbWaveFromData(string windTurbineId, string _measLocID, DateTime _acqTime)
        {
            VibWaveFormData waveFormData = null;
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HisDB, ConfigInfo.DBConnName))
            {
                waveFormData = ctx.VibWaveForms.AsNoTracking().FirstOrDefault(wf =>
                    wf.WindTurbineID == windTurbineId && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
            }

            //查找day表
            if(waveFormData == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.VibWaveForms.AsNoTracking().FirstOrDefault(wf =>
                        wf.WindTurbineID == windTurbineId && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            //查找小时表
            if(waveFormData == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.VibWaveForms.AsNoTracking().FirstOrDefault(wf =>
                        wf.WindTurbineID == windTurbineId && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            return waveFormData;
        }
        /// <summary>
        /// 获取机组波形列表
        /// </summary>
        /// <param name="_beginTime"></param>
        /// <param name="_endTime"></param>
        /// <returns></returns>
        private List<VibWaveFormData> GetDayDbWaveFromDataList(string windTurbineId, DateTime _beginTime, DateTime _endTime)
        {
            List<VibWaveFormData> waveFormData = new List<VibWaveFormData>();
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
            {
                waveFormData = ctx.VibWaveForms.AsNoTracking().Where(wf => wf.WindTurbineID == windTurbineId &&
                    wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
            }
            return waveFormData;
        }
        /// <summary>
        /// 获取机组转速波形列表
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="_beginTime"></param>
        /// <param name="_endTime"></param>
        /// <returns></returns>
        private List<RotSpeedWaveData> GetDayDbRotSpdWaveFromDataList(string windTurbineId, DateTime _beginTime, DateTime _endTime)
        {
            List<RotSpeedWaveData> rotSpdWaveFormData = new List<RotSpeedWaveData>();
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
            {
                rotSpdWaveFormData = ctx.RotSpdWaves.AsNoTracking().Where(wf => wf.WindTurbineID == windTurbineId &&
                 wf.AcquisitionTime >= _beginTime && wf.AcquisitionTime < _endTime).ToList();
            }
            return rotSpdWaveFormData;
        }

        #endregion

        private string GetDataUnit(string componentName)
        {
            string UnitName = "";
            switch (componentName)
            {
                case "电压":
                    UnitName = "(V)";
                    break;
                case "电流":
                    UnitName = "(A)";
                    break;
                case "温度":
                    UnitName = "(℃)";
                    break;
                case "转速":
                    UnitName = "(RPM)";
                    break;
                case "驱动端轴承":
                case "非驱动端轴承":
                    UnitName = "(m/s^2)";
                    break;
            }
            return UnitName;
        }

        #region echart 相关
        /// <summary>
        /// 创建chart 数据
        /// </summary>
        /// <param name="wave"></param>
        /// <param name="chartTitle"></param>
        /// <returns></returns>
        private static eChartsData CreateChartData()
        {
            eChartsData echartData = new eChartsData();
            echartData.legendData = new List<string>();
            echartData.series = new List<seriesData>();
            //echartData.primarykey = wave.AcquisitionTime.ToString();
            //echartData.title = "时域分析" + "(" + wave.AcquisitionTime.ToString() + ")";
            return echartData;
        }
        #endregion

        #region  转速波形分析
        private RotSpeedWaveData GetRotSpdWaveFromData(string WindTurbineID, string _measLocID, DateTime _acqTime)
        {
            RotSpeedWaveData waveFormData = null;
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
            {
                waveFormData = ctx.RotSpdWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
            }

            //hour 表
            if(waveFormData == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.RotSpdWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            //his表
            if(waveFormData == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HisDB, ConfigInfo.DBConnName))
                {
                    waveFormData = ctx.RotSpdWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            return waveFormData;
        }


        //工况波形
        private WorkConditionWaveFormData GetWkWaveFromData(string WindTurbineID, string _measLocID, DateTime _acqTime) {
            WorkConditionWaveFormData data = new WorkConditionWaveFormData();
            using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.DayDB, ConfigInfo.DBConnName))
            {
                data = ctx.WorkConditionWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
            }

            //hour 表
            if (data == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HourDB, ConfigInfo.DBConnName))
                {
                    data = ctx.WorkConditionWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            //his表
            if (data == null)
            {
                using (WFDataContext ctx = WFDataContext.Factory(EnumDataSource.HisDB, ConfigInfo.DBConnName))
                {
                    data = ctx.WorkConditionWaves.AsNoTracking().FirstOrDefault(wf => wf.WindTurbineID == WindTurbineID && wf.MeasLocationID == _measLocID && wf.AcquisitionTime == _acqTime);
                }
            }
            return data;
        }

        private void SetRotWaveData(RotSpeedWaveData rotWave, seriesData serData)
        {
            // 波形线数
            int lc = rotWave.LineCounts == null ? 1 : rotWave.LineCounts.Value;
            float gr = rotWave.GearRatio == null ? 1 : rotWave.GearRatio.Value;
            rotWave.WaveData = this.GetRotSpdWaveBytes(rotWave); 
            double[] timeTikeArray = CMSFramework.Utility.RotSpdWaveHelper.ConvertToTimeTicks(rotWave.WaveData);
            float[] rpmArray = CMSFramework.Utility.RotSpdWaveHelper.ConvertToRotSpdRPM(rotWave.WaveData, 1);
            // 每转都有 LineCounts 个点。
            for (int i = 0; i < rpmArray.Length; i++)
            {
                List<double> valueArr = new List<double>();
                valueArr.Add(timeTikeArray[i]);
                valueArr.Add(rpmArray[i] / lc * gr);
                serData.data.Add(valueArr);
            }
        }
        #endregion

        //private void SetWkWaveData(WorkConditionWaveFormData wkWave,seriesData serData)
        //{
        //    // 波形线数
        //    //int lc = rotWave.LineCounts == null ? 1 : wkWave.LineCounts.Value;
        //    //float gr = rotWave.GearRatio == null ? 1 : rotWave.GearRatio.Value;
        //    wkWave.WaveData = this.GetWkWaveBytes(wkWave); 
        //    double[] timeTikeArray = CMSFramework.Utility.RotSpdWaveHelper.ConvertToTimeTicks(wkWave.WaveData);
        //    float[] rpmArray = CMSFramework.Utility.RotSpdWaveHelper.ConvertToRotSpdRPM(wkWave.WaveData, 1);
        //    // 每转都有 LineCounts 个点。
        //    for (int i = 0; i < rpmArray.Length; i++)
        //    {
        //        List<double> valueArr = new List<double>();
        //        valueArr.Add(timeTikeArray[i]);
        //        valueArr.Add(rpmArray[i] / 1);
        //        serData.data.Add(valueArr);
        //    }
        //}


    }
    public class eChartsData
    {
        public string title { get; set; }
        public string primarykey { get; set; }
        public List<string> legendData { get; set; }
        public List<seriesData> series { get; set; }
        public string UnitName { get; set; }
    }
    public class seriesData
    {
        public string name { get; set; }
        public string type { get; set; }
        // public bool showAllSymbol { get; set; }
        public string symbol { get; set; }
        public string sampling { get; set; }
        public List<List<double>> data { get; set; }
    }
    public class WaveType : CMSFramework.BusinessEntity.VibWaveFormData
    {
        public string GUID { get; set; }
        public string TrainName { get; set; }
        public string RotSpdValue { get; set; }
        public string WaveformTypeName { get; set; }
        public string MeasLocName { get; set; }
        public float UpperLimitFreqency { get; set; }
        public string StrAcquisitionTime { get; set; }
        public string StringSampleRate { get; set; }
    }
    public class WaveDataFileInfo
    {
        public string fileName { get; set; }
        public string fileData { get; set; }
    }
    public class JsonWaveInfo
    {
        public string WindTurbineID { get; set; }
        public string TrainName { get; set; }
        public string MeasLocationID { get; set; }
        public string MeasLocName { get; set; }
        public DateTime AcquisitionTime { get; set; }
        public string WaveType { get; set; }
    }
}
