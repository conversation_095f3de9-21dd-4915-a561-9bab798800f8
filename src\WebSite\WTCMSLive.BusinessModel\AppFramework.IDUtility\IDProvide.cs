﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppFramework.IDUtility
{
    /// <summary>
    /// ID提供类
    /// </summary>
    public static class IDProvide
    {
        /// <summary>
        /// 返回风场编码
        /// </summary>
        /// <param name="groupCompanyCode">集团公司编码</param>
        /// <param name="windParkNumber">风场序列</param>
        /// <returns></returns>
        public static string GetWindParkId(string groupCompanyCode,string windParkNumber)
        {
            Dictionary<string, string> dic = CodeProvide.GetGroupCompanyDic();
            if (dic.ContainsKey(groupCompanyCode))
            {
                return dic[groupCompanyCode] + ConvertNumberToCode(windParkNumber, 3);
            }
            return groupCompanyCode + ConvertNumberToCode(windParkNumber, 3);
        }
        /// <summary>
        /// 机组ID
        /// </summary>
        /// <param name="windParkID">风场ID</param>
        /// <param name="code">机组序列</param>
        /// <returns></returns>
        public static string GetTurbineId(string windParkID, string code)
        {
       
                return windParkID + ConvertNumberToCode(code, 4);
           
        }
        /// <summary>
        /// 部件ID
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="componentName">部件编码</param>
        /// <returns></returns>
        public static string GetCompotentID(string turbineID, string componentName)
        {
            Dictionary<string, string> dic = CodeProvide.GetComponentDic();
            if (dic.ContainsKey(componentName))
            {
                return turbineID+dic[componentName];
            }
            return turbineID + componentName;
        }
        /// <summary>
        /// 获取测量位置ID
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="compotent">部件名称</param>
        /// <param name="section">截面</param>
        /// <param name="origentation">方向</param>
        /// <returns></returns>
        public static string GetVibMeasLocID(string turbineID, string compotent, string section, string origentation)
        {
            string id = turbineID;

            Dictionary<string, string> dic = new Dictionary<string, string>();
            //部件
            dic = CodeProvide.GetComponentDic();
            if (dic.ContainsKey(compotent))
            {
                id += dic[compotent];
            }
            else { id += compotent; }
            //截面
            dic = CodeProvide.GetSectionDic(compotent);
            if (dic.ContainsKey(section))
            {
                id += dic[section];
            }
            else { id += section; }
            //方向
            dic = CodeProvide.GetOrientationDic();
            if (dic.ContainsKey(origentation))
            {
                id += dic[origentation];
            }
            else {
                // 法兰间隙自定义输入60度 转换为 060D
                //if (int.TryParse(origentation, out int outOrigentation))
                //{
                //    id += string.Format("{0:000}", outOrigentation) + "D";
                //}

                // 约定法兰间隙输入 为 90D， 自动补全为 090D
                if (origentation.ToUpper().Contains("D"))
                {
                    id += origentation.ToUpper().PadLeft(4, '0');
                }
                else
                {
                    id += origentation;
                }

            }
            return id;
        }
        /// <summary>
        /// 转速测量位置ID
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="compotent">部件</param>
        /// <returns></returns>
        public static string GetRotSpdLocID(string turbineID, string compotent)
        {
            string id = turbineID;
            Dictionary<string, string> dic = CodeProvide.GetComponentDic();
            if (dic.ContainsKey(compotent))
            {
                id += dic[compotent];
            }
            else { id += compotent; }
            return id + "RSPD";
        }
        /// <summary>
        /// 晃度测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="compotent"></param>
        /// <param name="parm"></param>
        /// <param name="sectionID"></param>
        /// <returns></returns>
        public static string GetSVMLocID(string turbineID, string compotent,string sectionID, string parm)
        {
            string id = turbineID+compotent + sectionID;
            Dictionary<string, string> dic = CodeProvide.GetSVMLocDic();
            if (dic.ContainsKey(parm))
            {
                id += dic[parm];
            }
            else { id += parm; }

            return id;
        }

        /// <summary>
        /// 工况参数ID
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="type">数据来源1 DAU,0 MCS</param>
        /// <param name="parm"></param>
        /// <returns></returns>
        public static string GetWorkCondID(string turbineID, string type, string parm)
        {
            string id = turbineID;
            if (type == "1")
            {
                id += "DAU";
            }
            else {
                id += "MCS";
            }

            Dictionary<string, string> dic = CodeProvide.GetWorkCondDic();
            if (dic.ContainsKey(parm))
            {
                id += dic[parm];
            }
            else {
                id += parm;
            }
            return id;
        }


        /// <summary>
        /// 转换指定数字为对应长度编号
        /// </summary>
        /// <param name="number"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        private static string ConvertNumberToCode(string number, int length)
        {
        
            string code = string.Empty;
            int index = number.Length;
            for (; index < length; index++)
            {
                number = "0" + number;
            }
            return number;
        }
    }

}
