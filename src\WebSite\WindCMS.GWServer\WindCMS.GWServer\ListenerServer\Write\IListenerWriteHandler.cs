﻿namespace WindCMS.GWServer.ListenerServer;

public interface IListenerWriteHandler
{
    /// <summary>
    /// 启动采集
    /// </summary>
    /// <returns></returns>
    public bool StartDaq();
    
    /// <summary>
    /// 停止采集
    /// </summary>
    /// <returns></returns>
    public bool StopDaq();
    
    /// <summary>
    /// 设置配置工具网络参数(IP、Port)
    /// </summary>
    /// <returns></returns>
    public bool SetConfigToolNetworkParameter();

    /// <summary>
    /// 获取配置工具网络参数(IP、Port)
    /// </summary>
    /// <returns></returns>
    public bool GetConfigToolNetworkParameter();

    /// <summary>
    /// 设置DAU基础信息(配置风场ID、机组ID、设备类型)
    /// </summary>
    public bool SetDauBaseParameter();

    /// <summary>
    /// 获取DAU基础信息(配置风场ID、机组ID、设备类型)
    /// </summary>
    /// <returns></returns>
    public bool GetDauBaseParameter();

    /// <summary>
    /// 设置采集策略(波形间隔、特征值采集间隔、录播天数、录播功能是否开启、启动采集延时、是否存储触发采集、对时服务IP地址、对时服务端口)
    /// </summary>
    /// <returns></returns>
    public bool SetDauCollectionStrategyRequest();

    /// <summary>
    /// 获取采集策略(波形间隔、特征值采集间隔、录播天数、录播功能是否开启、启动采集延时、是否存储触发采集、对时服务IP地址、对时服务端口)
    /// </summary>
    /// <returns></returns>
    public bool GetDauCollectionStrategy();

    /// <summary>
    /// 设置DAU推送信息(SFTP服务IP、端口、用户名字节数、密码字节数、路径字节数、用户名、密码、路径)
    /// </summary>
    /// <returns></returns>
    public bool SetDauPushParameter();

    /// <summary>
    /// 获取DAU推送信息(SFTP服务IP、端口、用户名字节数、密码字节数、路径字节数、用户名、密码、路径)
    /// </summary>
    /// <returns></returns>
    public bool GetDauPushParameter();

    /// <summary>
    /// 设置是否坏标志
    /// </summary>
    /// <returns></returns>
    public bool SetDauEquipmentWarranty();

    /// <summary>
    /// 获取是否坏标志
    /// </summary>
    /// <returns></returns>
    public bool GetDauEquipmentWarranty();

    /// <summary>
    /// 开始获取录播数据
    /// </summary>
    /// <returns></returns>
    public bool StartGetRecorded();

    /// <summary>
    /// 下发测量定义
    /// </summary>
    /// <returns></returns>
    public bool SendMDF(byte[] wtLiveDbByteArray);
    
    /// <summary>
    /// 设置DAU上下文信息
    /// </summary>
    /// <param name="wtLiveDbByteArray"></param>
    /// <param name="dauContextConfigRequestArrayLength"></param>
    /// <returns></returns>
    public bool SetDauContext(byte[] wtLiveDbByteArray, int dauContextConfigRequestArrayLength = 1010);
    
    /// <summary>
    /// 获取DAU上下文信息
    /// </summary>
    /// <returns></returns>
    public byte[] GetDauContext();
}