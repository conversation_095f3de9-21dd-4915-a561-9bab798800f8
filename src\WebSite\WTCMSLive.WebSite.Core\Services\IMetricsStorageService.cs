using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 性能数据存储服务接口
    /// </summary>
    public interface IMetricsStorageService
    {
        /// <summary>
        /// 存储性能数据
        /// </summary>
        /// <param name="data">性能数据</param>
        /// <returns>是否存储成功</returns>
        Task<bool> StoreMetricsDataAsync(MetricsDataDTO data);

        /// <summary>
        /// 批量存储性能数据
        /// </summary>
        /// <param name="dataList">性能数据列表</param>
        /// <returns>是否存储成功</returns>
        Task<bool> StoreBatchMetricsDataAsync(List<MetricsDataDTO> dataList);

        /// <summary>
        /// 查询性能数据
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>性能数据列表</returns>
        Task<List<MetricsDataDTO>> QueryMetricsDataAsync(MetricsQueryRequestDTO request);

        /// <summary>
        /// 获取性能数据文件信息
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>文件信息列表</returns>
        Task<List<MetricsFileInfoDTO>> GetMetricsFileInfoAsync(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 下载性能数据文件
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>下载响应</returns>
        Task<MetricsDownloadResponseDTO> DownloadMetricsDataAsync(MetricsQueryRequestDTO request);

        /// <summary>
        /// 下载所有性能数据文件
        /// </summary>
        /// <returns>下载响应</returns>
        Task<MetricsDownloadResponseDTO> DownloadAllMetricsDataAsync();

        /// <summary>
        /// 清理过期数据
        /// </summary>
        /// <returns>清理的文件数量</returns>
        Task<int> CleanupExpiredDataAsync();

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        /// <returns>存储统计信息</returns>
        Task<object> GetStorageStatisticsAsync();
    }
}
