﻿namespace WTCMSLive.WebSite.Core.DTOs
{
    public class ModbusDeviceDTO
    {

        public string TurbineID { get; set; }
        public string? DauID { get; set; }
        public string ModbusID { get; set; }
        public string ModbusType { get; set;}
        public string? ComIP { get; set; }
        public int? ComPort { get; set; }
        public string? ComType { get; set; }

        public string? PortName { get; set; }
        public int? BaudRate { get; set; }
        public int? DataBit { get; set; }
        public short? Parity { get; set; }
        public short? StopBit { get; set; }

        public string? ModbusDeviceName { get; set; }
    }

    /// <summary>
    /// 编辑Modbus设备DTO
    /// </summary>
    public class EditModbusDeviceDTO
    {
        public int ModbusDeviceID { get; set; }
        public string TurbineID { get; set; }
        public string ModbusID { get; set; }
        public string? DauID { get; set; }
        public string? ModbusType { get; set; }
        public string? ComIP { get; set; }
        public int? ComPort { get; set; }
        public string? ComType { get; set; }
        public string? PortName { get; set; }
        public int? BaudRate { get; set; }
        public int? DataBit { get; set; }
        public short? Parity { get; set; }
        public short? StopBit { get; set; }
        public string? ModbusDeviceName { get; set; }
    }

    /// <summary>
    /// 批量删除Modbus设备DTO
    /// </summary>
    public class BatchDeleteModbusDeviceDTO
    {
        public List<ModbusDeviceIdentifier> Devices { get; set; } = new List<ModbusDeviceIdentifier>();
    }

    /// <summary>
    /// Modbus设备标识符
    /// </summary>
    public class ModbusDeviceIdentifier
    {
        public string TurbineID { get; set; }
        public int ModbusDeviceID { get; set; }
        public string? ModbusID { get; set; }
    }

    /// <summary>
    /// Modbus设备列表查询响应DTO
    /// </summary>
    public class ModbusDeviceListDTO
    {
        public int ModbusDeviceID { get; set; }

        public string TurbineID { get; set; }
        public string? DauID { get; set; }
        public string ModbusID { get; set; }
        public string ModbusType { get; set; }
        public string ModbusTypeName { get; set; }
        public string ComType { get; set; }
        public string ComTypeName { get; set; }
        public string? ComIP { get; set; }
        public int? ComPort { get; set; }
        public string? PortName { get; set; }
        public int? BaudRate { get; set; }
        public int? DataBit { get; set; }
        public short? Parity { get; set; }
        public short? StopBit { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }

        public string? ModbusDeviceName { get; set; }
    }
}
