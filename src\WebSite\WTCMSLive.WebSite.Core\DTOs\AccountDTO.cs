using System.ComponentModel.DataAnnotations;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 修改密码请求DTO
    /// </summary>
    public class ChangePasswordRequestDTO
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage = "账号不能为空")]
        public string Account { get; set; }

        /// <summary>
        /// 旧密码
        /// </summary>
        [Required(ErrorMessage = "旧密码不能为空")]
        public string OldPassword { get; set; }

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码不能为空")]
        public string NewPassword { get; set; }
    }

    /// <summary>
    /// 重置密码请求DTO
    /// </summary>
    public class ResetPasswordRequestDTO
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage = "账号不能为空")]
        public string Account { get; set; }
    }

    /// <summary>
    /// 添加用户请求DTO
    /// </summary>
    public class AddUserRequestDTO
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage = "账号不能为空")]
        public string Account { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        public string UserName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        [Required(ErrorMessage = "角色不能为空")]
        public string Role { get; set; }
    }

    /// <summary>
    /// 编辑用户请求DTO
    /// </summary>
    public class EditUserRequestDTO
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage = "账号不能为空")]
        public string Account { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        public string UserName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        public string? Role { get; set; }
    }



    /// <summary>
    /// 删除用户请求DTO
    /// </summary>
    public class DeleteUserRequestDTO
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage = "账号不能为空")]
        public string Account { get; set; }
    }

    /// <summary>
    /// 检查用户名响应DTO
    /// </summary>
    public class CheckUserNameResponseDTO
    {
        /// <summary>
        /// 状态（0-失败，1-成功）
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }

    /// <summary>
    /// 模块信息DTO
    /// </summary>
    public class ModuleDTO
    {
        /// <summary>
        /// 模块ID
        /// </summary>
        public string ModuleID { get; set; }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; }
    }

    /// <summary>
    /// 角色信息DTO
    /// </summary>
    public class RoleDTO
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleID { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        public string RoleDescription { get; set; }

        /// <summary>
        /// 是否系统角色
        /// </summary>
        public bool IsSystemRole { get; set; }

        /// <summary>
        /// 角色绑定的模块列表
        /// </summary>
        public List<ModuleDTO> Modules { get; set; } = new List<ModuleDTO>();
    }

    /// <summary>
    /// 添加角色请求DTO
    /// </summary>
    public class AddRoleRequestDTO
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        [Required(ErrorMessage = "角色名称不能为空")]
        public string RoleName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        public string RoleDescription { get; set; }

        /// <summary>
        /// 选择的模块ID列表
        /// </summary>
        public List<string> ModuleIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// 编辑角色请求DTO
    /// </summary>
    public class EditRoleRequestDTO
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required(ErrorMessage = "角色ID不能为空")]
        public string RoleID { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        [Required(ErrorMessage = "角色名称不能为空")]
        public string RoleName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        public string RoleDescription { get; set; }

        /// <summary>
        /// 选择的模块ID列表
        /// </summary>
        public List<string> ModuleIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// 删除角色请求DTO
    /// </summary>
    public class DeleteRoleRequestDTO
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required(ErrorMessage = "角色ID不能为空")]
        public string RoleID { get; set; }
    }
}
