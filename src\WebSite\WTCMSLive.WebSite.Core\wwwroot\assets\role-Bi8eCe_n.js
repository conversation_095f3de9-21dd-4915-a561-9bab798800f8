import{W as F}from"./table-DznTy2O5.js";import{O as A}from"./index-BKL_RKUZ.js";import{co as B,r as i,X as G,w as q,f as v,d as w,u as E,o as g,i as x,b as _,m as a}from"./index-BedJHPLx.js";import{u as J}from"./role-Cm5IINmK.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as V}from"./ActionButton-FpgTQOJj.js";import{M as W}from"./index-BD0EDEeZ.js";import"./tools-DC78Tda0.js";import"./styleChecker-D6uzjM95.js";import"./index-CTEgH1Bv.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const j={__name:"role",setup(z){const d=J(),k=B(),u=i(""),n=i(""),s=i({}),I=i([]),p=i(!1);let r=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).userRole:{};const T=[{title:"角色名称",dataIndex:"roleName",width:150,align:"center"},{title:"描述",dataIndex:"roleDescription",inputType:"textarea",maxLength:100,width:300,align:"center"},{title:"页面权限",dataIndex:"modules",inputType:"",customRender:({text:e,record:o})=>o.modules&&o.modules.length?o.modules.map(t=>t.moduleName).join("，"):""}],S=[{title:"角色名称",dataIndex:"roleName",isrequired:!0},{title:"描述",dataIndex:"roleDescription",inputType:"textarea",maxLength:100,width:800},{title:"页面权限",slotName:"permissionTitle",hidden:!0},{dataIndex:"permission2",inputType:"checkboxGroup",width:800,title:"厂站级",isrequired:!0,selectOptions:[{label:"设备管理",value:"38",checked:!0,disabled:!0},{label:"设备配置",value:"33"}]},{dataIndex:"permission3",inputType:"checkboxGroup",width:800,title:"设备级",isrequired:!0,selectOptions:[{label:"设备管理",value:"411",checked:!0,disabled:!0},{label:"设备配置",value:"44"},{label:"主控配置",value:"45"},{label:"采集单元配置",value:"46"},{label:"Modbus配置",value:"47"},{label:"测量定义配置",value:"48"},{label:"测量方案配置",value:"410"},{label:"数据验证",value:"412"}]},{dataIndex:"permission4",inputType:"checkboxGroup",width:800,title:"系统设置",selectOptions:[{label:"密码设定",value:"11"},{label:"角色管理",value:"12"},{label:"用户管理",value:"13"}]}],b=i(!1),y=E(),h=G({table:[],tableColumns:T}),m=async()=>{p.value=!0,h.table=await d.fetchGetrolelist(),p.value=!1};q(()=>y.params.id,()=>{m()},{immediate:!0});const D=()=>{b.value=!0},c=e=>{b.value=!1,I.value=[],s.value={},u.value="",n.value=""},C=e=>{u.value="添加角色",n.value="add",s.value={permission1:["21"],permission2:["38"],permission3:["411"]},D()},R=async e=>{const{record:o}=e;if(r&&r.roleID&&r.roleID===o.roleID){a.error("当前角色不能删除!当前用户正在使用！");return}let t=await d.fetchDeleterole({roleID:o.roleID});t&&t.code===1?(m(),a.success("提交成功")):a.error("提交失败:"+t.msg)},N=e=>{const{rowData:o}=e;u.value="编辑角色",n.value="edit";let t={permission1:[],permission2:[],permission3:[],permission4:[]};o.modules&&o.modules.length&&o.modules.map(l=>{switch(l.moduleID.split("")[0]){case"2":t.permission1.push(l.moduleID);break;case"3":t.permission2.push(l.moduleID);break;case"4":t.permission3.push(l.moduleID);break;case"1":t.permission4.push(l.moduleID);break}}),s.value={...o,...t},D()},M=async e=>{switch(console.log(e),n.value){case"add":let o=await d.fetchAddrole({...e,roleDescription:e.roleDescription||"",moduleIds:[...e.permission1||[],...e.permission2,...e.permission3,...e.permission4||[]]});o&&o.code===1?(m(),a.success("提交成功"),c()):a.error("提交失败:"+o.msg);break;case"edit":let t=[...e.permission1||[],...e.permission2,...e.permission3,...e.permission4||[]];t=[...new Set(t)];let l=await d.fetchEditrole({...e,roleDescription:e.roleDescription||"",roleID:s.value.roleID,moduleIds:t});if(l&&l.code===1){if(r&&r.roleID&&r.roleID===s.value.roleID){let f=window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[];if(f.length!==t.length||f.some(O=>!t.includes(O))){a.info("当前角色权限已修改，请重新登录！"),k.logout();return}}m(),a.success("提交成功"),c()}else a.error("提交失败:"+l.msg);break}};return(e,o)=>{const t=W,l=V;return g(),v(l,{spinning:p.value,size:"large"},{default:w(()=>[x("div",null,[_(F,{tableTitle:"角色列表","table-key":"0","table-columns":h.tableColumns,noBatchApply:!0,"table-operate":["edit","delete","add"],"record-key":"ModbusUnitID","table-datas":h.table,onAddRow:C,onDeleteRow:R,onEditRow:N,actionCloumnProps:{width:120,align:"center"}},null,8,["table-columns","table-datas"])]),_(t,{maskClosable:!1,width:"1000px",open:b.value,title:u.value,footer:"",onCancel:c},{default:w(()=>[(g(),v(A,{titleCol:S,initFormData:s.value,key:n.value,currentColumns:I.value,onCancelForm:c,onSubmit:M},{permissionTitle:w(()=>o[0]||(o[0]=[x("div",{class:"permissionTitle"}," 页面权限： ",-1)])),_:1},8,["initFormData","currentColumns"]))]),_:1},8,["open","title"])]),_:1},8,["spinning"])}}},re=L(j,[["__scopeId","data-v-b0194c73"]]);export{re as default};
