using Microsoft.AspNetCore.Mvc.Filters;

namespace WTCMSLive.WebSite.Core.Middleware
{
    /// <summary>
    /// Action上下文中间件，用于存储ActionExecutingContext以供批量操作使用
    /// </summary>
    public class ActionContextMiddleware : IActionFilter
    {
        /// <summary>
        /// Action执行前
        /// </summary>
        public void OnActionExecuting(ActionExecutingContext context)
        {
            // 将ActionExecutingContext存储到HttpContext.Items中，供批量操作服务使用
            context.HttpContext.Items["ActionExecutingContext"] = context;
        }

        /// <summary>
        /// Action执行后
        /// </summary>
        public void OnActionExecuted(ActionExecutedContext context)
        {
            // 清理存储的上下文
            context.HttpContext.Items.Remove("ActionExecutingContext");
        }
    }
}
