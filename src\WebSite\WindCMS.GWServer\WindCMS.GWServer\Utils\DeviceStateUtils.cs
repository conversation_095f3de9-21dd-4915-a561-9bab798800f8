﻿using System.Security.Cryptography;
using System.Text;

namespace WindCMS.GWServer.Utils;

/// <summary>
/// 设备状态工具类
/// </summary>
public static class DeviceStateUtils
{
    // 异常状态的概率阈值（10%）
    private const double AbnormalThreshold = 0.1;
    
    // 缓存已计算过的设备状态（可选，提高性能）
    private static readonly Dictionary<string, bool> _stateCache = new();

    /// <summary>
    /// 检查设备是否应该处于异常状态（基于一致性哈希）
    /// </summary>
    public static bool IsDeviceAbnormal(string snCode)
    {
        // 如果已经计算过，直接返回缓存结果
        if (_stateCache.TryGetValue(snCode, out var cachedState))
        {
            return cachedState;
        }

        // 使用设备ID计算一致性哈希值
        double hashValue = ComputeConsistentHash(snCode);
        
        // 根据阈值判断是否异常
        bool isAbnormal = hashValue < AbnormalThreshold;
        
        // 缓存结果（可选）
        _stateCache[snCode] = isAbnormal;
        
        return isAbnormal;
    }

    /// <summary>
    /// 计算一致性哈希值（0.0到1.0范围）
    /// </summary>
    private static double ComputeConsistentHash(string input)
    {
        using (MD5 md5 = MD5.Create())
        {
            // 计算MD5哈希
            byte[] hashBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            
            // 取前8字节转换为ulong（更大的数值范围比uint更均匀）
            ulong hash = BitConverter.ToUInt64(hashBytes, 0);
            
            // 归一化到0.0-1.0范围
            return (double)hash / ulong.MaxValue;
        }
    }
}