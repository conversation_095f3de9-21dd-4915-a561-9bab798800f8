﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertEntityBusinessToDAWTM
    {
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 机组型号表
        /// </summary>
        /// <param name="_entity">WindTurbineModel</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.WTModel ConvertWTModel(WTCMSLive.BusinessEntity.WindTurbineModel _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.WTModel _WTModel = new WTCMSLive.Entity.Models.WTModel();

            _WTModel.WindTurbineModel = _entity.TurbineModel;
            _WTModel.StructureType = ((int)_entity.StructureType).ToString();
            _WTModel.StructureDiagram = _entity.StructureDiagram;
            _WTModel.Manufactory = _entity.Manufactory;
            _WTModel.FactedPower =(decimal) _entity.RatedPower;
            _WTModel.Description = _entity.Description;
            _WTModel.BladeNum = (short)_entity.BladeNum;
            return _WTModel;
        }

        public static List<WTCMSLive.Entity.Models.WTModel> ConvertWTModelList(List<WTCMSLive.BusinessEntity.WindTurbineModel> _entityList)
        {
            List<WTCMSLive.Entity.Models.WTModel> wtModelList = new List<Entity.Models.WTModel>();
            foreach (WTCMSLive.BusinessEntity.WindTurbineModel item in _entityList)
            {
                WTCMSLive.Entity.Models.WTModel wtModel = ConvertWTModel(item);
                wtModelList.Add(wtModel);
            }
            return wtModelList;
        }

    }
}
