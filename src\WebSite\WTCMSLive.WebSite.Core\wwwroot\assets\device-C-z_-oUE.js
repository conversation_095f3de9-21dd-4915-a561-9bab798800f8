import{u as re,C as de,W as ce}from"./table-DznTy2O5.js";import{S as ue,T as pe,U as me,V as ve,W as fe,j as G,r,u as be,X as he,h as we,w as ye,f as O,d as w,Y as Ie,Z as F,a as De,o as P,i as V,b as R,c as ge,F as ke,e as Pe,g as U,t as Te,q as _e,m as p}from"./index-BedJHPLx.js";import{O as Oe}from"./index-BKL_RKUZ.js";import{W as Ce}from"./index-C1FH5xdO.js";import{g as D,a as xe,b as z,t as We}from"./tools-DC78Tda0.js";import{u as Le}from"./model-Db0y2bgw.js";import{u as Fe}from"./devTree-Dgv5CE1u.js";import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as Ne}from"./ActionButton-FpgTQOJj.js";import{D as Me,a as Se}from"./index-BPV4rCOw.js";import{B as Ee}from"./index-CTEgH1Bv.js";import{M as Ve}from"./index-BD0EDEeZ.js";import"./styleChecker-D6uzjM95.js";import"./index-D7Z91OP6.js";import"./shallowequal-jVPYMrcC.js";import"./index-BUdCa0Ne.js";import"./index-E_bOH47g.js";import"./index-3RlmUHX7.js";const qe="YYYY-MM-DD",Ae=[{value:"河北",label:"河北"},{value:"山西",label:"山西"},{value:"黑龙江",label:"黑龙江"},{value:"吉林",label:"吉林"},{value:"辽宁",label:"辽宁"},{value:"江苏",label:"江苏"},{value:"浙江",label:"浙江"},{value:"安徽",label:"安徽"},{value:"福建",label:"福建"},{value:"江西",label:"江西"},{value:"山东",label:"山东"},{value:"河南",label:"河南"},{value:"湖北",label:"湖北"},{value:"湖南",label:"湖南"},{value:"广东",label:"广东"},{value:"海南",label:"海南"},{value:"四川",label:"四川"},{value:"贵州",label:"贵州"},{value:"云南",label:"云南"},{value:"陕西",label:"陕西"},{value:"甘肃",label:"甘肃"},{value:"青海",label:"青海"},{value:"台湾",label:"台湾"},{value:"内蒙古",label:"内蒙古"},{value:"广西",label:"广西"},{value:"西藏",label:"西藏"},{value:"宁夏",label:"宁夏"},{value:"新疆",label:"新疆"},{value:"北京",label:"北京"},{value:"天津",label:"天津"},{value:"上海",label:"上海"},{value:"重庆",label:"重庆"},{value:"香港",label:"香港"},{value:"澳门",label:"澳门"}];let d=320;const H=(o={isEdit:!1},l,n)=>[{title:"id",dataIndex:"windParkID",formItemWidth:d,isdisplay:!1},{title:"集团公司",isdisplay:o&&!o.isEdit,dataIndex:"windParkGroupName",formItemWidth:d,inputType:"select",selectOptions:[],isrequired:o&&!o.isEdit,isdisplay:o&&!o.isEdit},{title:"厂站编号",dataIndex:"windParkCode",formItemWidth:d,isrequired:o&&!o.isEdit,disabled:o&&o.isEdit,validateRules:[{pattern:/^\d{1,3}$/,message:"厂站编号是1至3位的数字"}]},{title:"厂站名称",dataIndex:"windParkName",formItemWidth:d,isrequired:!0},{title:"投运日期",dataIndex:"operationalDate",formItemWidth:d,inputType:"datepicker",isrequired:!0,timeFormat:qe},{title:"联系人",dataIndex:"contactMan",formItemWidth:d},{title:"联系人电话",dataIndex:"contactTel",formItemWidth:d,validateRules:D({title:"联系人电话",type:"phone"})},{title:"国家",dataIndex:"country",formItemWidth:d,inputType:"select",selectOptions:[{label:"中国",value:"中国"}],validateRules:D({title:"国家",required:!0})},{title:"省份 ",dataIndex:"area",formItemWidth:d,noColon:!0,inputType:"select",selectOptions:Ae,validateRules:D({title:"省份",required:!0})},{title:"厂站地址",dataIndex:"address",formItemWidth:d,isrequired:!0},{title:"经纬度",dataIndex:"location1",formItemWidth:140,validateRules:[...D({title:"经度",type:"number"}),{validator:(m,c)=>l(m,c),trigger:"change"}]},{title:"",dataIndex:"location2",formItemWidth:140,formItemClass:"weidu",validateRules:[...D({title:"经度",type:"number"}),{validator:(m,c)=>n(m,c),trigger:"change"}]},{title:"邮编",dataIndex:"postCode",formItemWidth:d,validateRules:D({title:"邮编",type:"postCode"})},{title:"厂站概况",dataIndex:"description",formItemWidth:d}];H();const $e=ue("configRoot",{state:()=>({parkList:[],parkOptions:[],editWindpark:{},groupCompanyList:[]}),actions:{async fetchParkList(){try{const o=await fe();return this.parkList=o,this.parkOptions=z(o,{label:"windParkName",value:"windParkID"},{nother:!0}),o}catch(o){throw console.error("获取厂站失败:",o),o}},async fetchEditWindparkInformation(o={}){try{const l=await ve(o);return l&&l.length>0,l}catch(l){throw console.error("编辑厂站失败:",l),l}},async fetchGroupCompanyList(o={}){try{const l=await me(o);let n=xe(l,!0);return this.groupCompanyList=n,n}catch(l){throw console.error("获取集团公司失败:",l),l}},async fetchDeletetPark(o={}){try{return await pe(o)}catch(l){throw console.error("删除厂站失败:",l),l}}}}),je={class:"border"},I=320,C="YYYY-MM-DD",Be={__name:"device",setup(o){const l=Le(),n=re(),m=$e(),c=Fe();De();const x=e=>[{title:"设备编号",dataIndex:"windTurbineCode",columnWidth:"130",formItemWidth:I,disabled:e&&e.edit,isrequired:!0,headerOperations:{sorter:!0},columnOperate:{type:"number"},validateRules:[{pattern:/^\d{1,4}$/,message:"设备编号是1至4位的数字"}]},{title:"设备名称",dataIndex:"windTurbineName",columnWidth:"130",formItemWidth:I,isrequired:!0,columnOperate:{type:"number"}},{title:"设备型号",dataIndex:"windTurbineModel",columnWidth:"140",formItemWidth:I,inputType:"select",isrequired:!0,selectOptions:[],disabled:e&&e.edit&&!e.canEditModel,headerOperations:{filters:[]}},{title:"部件",dataIndex:"componentIds",columnWidth:"200",formItemWidth:I,inputType:"select",mode:"tags",isrequired:!0,selectOptions:[],slotName:"part",hidden:e&&e.edit,...e&&e.isForm?{}:{customRender:({record:t})=>t.componentName?Ie("span",{style:{textAlign:"left",display:"block"},title:t.componentName.join("，")},t.componentName.join("，")):""}},{title:"投递日期",dataIndex:"operationalDate",columnWidth:"120",formItemWidth:I,inputType:"datepicker",timeFormat:C,headerOperations:{sorter:!0,date:!0}},{title:"主控IP",dataIndex:"mcsIP",columnWidth:"170",formItemWidth:I,hidden:e&&e.edit,columnOperate:{type:"ip"},validateRules:D({type:"ip",title:"主控IP"})},{title:"设备坐标",dataIndex:"location",formItemWidth:I,columnWidth:"100",validateRules:[{pattern:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/,message:"请输入两个数字并用英文逗号隔开!"}]}],X=G(()=>["edit","delete","batchDelete"]);window.localStorage.getItem("templateManagement");const g=r(null);r([]);const T=r(""),u=r(""),y=r({}),q=r([]),v=r([]),W=r(!1),A=be(),f=r(A.params.id),Z=r({}),N=r(!1),_=he({partValue:"",partList:[],noOperatesOfUser:["copyDevice","exportConfigers","add"]});let M=[...H({isEdit:!0},async(e,t)=>{let a=g.value.getFieldsValue();if(!a)return;const i=parseFloat(a.location1);return parseFloat(a.location2)&&!i?Promise.reject(new Error("请输入经度")):(g.value.clearValidate("location2"),Promise.resolve())},async(e,t)=>{let a=g.value.getFieldsValue();if(!a)return;const i=parseFloat(a.location1);return!parseFloat(a.location2)&&i?Promise.reject(new Error("请输入纬度")):(g.value.clearValidate("location1"),Promise.resolve())})];const $=(e={})=>[{label:"厂站名称",value:e.windParkName},{label:"厂站编号",value:e.windParkCode},{label:"投运日期",value:e.operationalDate?F(e.operationalDate).format(C):""},{label:"联系人",value:e.contactMan},{label:"联系人电话",value:e.contactTel},{label:"区域",value:`${e.country} - ${e.area}`},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"邮编",value:e.postCode},{label:"厂站概况",value:e.description}],j=r(x()),J=G(()=>u.value==="batchAdd"?"1200px":u.value==="edit"?"680px":"600px"),B=r([$({})]),k=async e=>{f.value&&(N.value=!0,q.value=await n.fetchDevTreedDevicelist({windParkID:f.value}),N.value=!1,j.value=x())},S=async()=>{if(f.value){const e=await n.fetchParkInfo({windParkID:f.value});Z.value=e,B.value=$(e)}};we(()=>{S(),k()}),ye(()=>A.params.id,e=>{n.reset(),f.value=e,S(),k()});const K=async()=>{u.value="editPark",T.value="编辑厂站信息";const e=n.parkInfo;let t=e.operationalDate?F(e.operationalDate,C):"",a=e.location?e.location.split(","):[];y.value={...e,location1:a[0],location2:a[1],operationalDate:t},m.groupCompanyList&&m.groupCompanyList.length>0?M[1].selectOptions=m.groupCompanyList:await Q(),v.value=[...M],E()},Q=async()=>{const e=await m.fetchGroupCompanyList();e&&e.length>0&&(M[1].selectOptions=e)},Y=async()=>{l.modelOptions&&l.modelOptions.length>0||await l.fetchModellist()},ee=async()=>{(!n.comPonentList||n.comPonentList.length<1)&&await n.fetchGetAllComPonentList()},E=()=>{W.value=!0},b=e=>{W.value=!1,_.partList=[],_.partValue="",y.value={},v.value=[],T.value="",u.value=""},te=async e=>{const{operateType:t}=e;u.value=t,T.value="批量添加设备",await Y(),await ee();let a=x({isForm:!0});a[2].selectOptions=l.modelOptions,a[3].selectOptions=n.comPonentList,v.value=[...a],E()},ae=async(e={})=>{const{selectedkeys:t}=e,a=await n.fetchDeletetDevice(t);a&&a.code===1?(k(),b(),p.success("提交成功"),c.getDevTreeDatas()):p.error("提交失败:"+a.msg)},oe=async e=>{const{rowData:t,operateType:a}=e;u.value=a;let i=t.operationalDate?F(t.operationalDate,C):"";y.value={...t,componentIds:t.componentIds&&t.componentIds.length?t.componentIds.split(","):[],operationalDate:i},T.value="编辑设备",await Y();let s=x({edit:!0,isForm:!0,canEditModel:_.noOperatesOfUser&&_.noOperatesOfUser.length});s[2].selectOptions=l.modelOptions,s[3].inputType="checkboxGroup",v.value=[...s],E()},le=async(e,t,a)=>{if(e.dataIndex==="windParkID"){if(e.value==c.templateDeviceList[0].windParkID)v.value[0].rows[1].cols[0].selectOptions=c.templateDevicoptions||[];else{let i=await n.fetchDevTreedDevicelist({windParkID:e.value});if(i&&i.length>0){let s=z(i,{label:"windTurbineName",value:"windTurbineID"},{nother:!0});v.value[0].rows[1].cols[0].selectOptions=s}else v.value[0].rows[1].cols[0].selectOptions=[]}g.value.setFieldValue("_windTurbineIDOld",null)}},ne=async e=>{if(u.value=="editPark"){const t=await m.fetchEditWindparkInformation({...e,location:`${e.location1},${e.location2}`,windParkID:f.value,windParkGroupName:e.windParkName});t&&t.code===1?(S(),p.success("提交成功"),e.windParkName!==n.parkInfo.windParkName&&c.getDevTreeDatas(),b()):p.error("提交失败:"+t.msg)}else if(u.value=="copyDevice"){const t=await n.fetchCopyTurbine({...e,_curParkId:f.value,_prefix:e._prefix||"",_suffix:e._suffix||"",startNum:e.startNum||""});t&&t.code===1?(p.success("复制成功"),k(),b(),c.getDevTreeDatas()):p.error("提交失败:"+t.msg)}else{let t=[{...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:y.value.windTurbineID,windParkID:y.value.windParkId}];const a=await n.fetchEditDevices(t);a&&a.code===1?(k(),p.success("提交成功"),e.windTurbineName!==y.value.windTurbineName&&c.getDevTreeDatas(),b()):p.error("提交失败:"+a.msg)}},ie=async e=>{if(u.value=="batchAdd"){let t={WindTurbineID:"",WindParkId:f.value},i=We(e).map(h=>({...h,...t,componentIds:h.componentIds&&h.componentIds.length?h.componentIds.join(","):"",operationalDate:h.operationalDate?F(h.operationalDate).format(C):""}));const s=await n.fetchAddDevice(i);s&&s.code===1?(k(),b(),p.success("提交成功"),c.getDevTreeDatas()):p.error("提交失败:"+s.msg)}};return(e,t)=>{const a=Ee,i=Se,s=Me,h=Ve,se=Ne;return P(),O(se,{spinning:N.value,size:"large"},{default:w(()=>[V("div",null,[R(de,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{rightButtons:w(()=>[_.noOperatesOfUser.includes("editPark")?_e("",!0):(P(),O(a,{key:0,type:"primary",onClick:t[0]||(t[0]=L=>K())},{default:w(()=>t[1]||(t[1]=[U(" 编辑 ",-1)])),_:1,__:[1]}))]),content:w(()=>[V("div",je,[R(s,{column:5,size:"small"},{default:w(()=>[(P(!0),ge(ke,null,Pe(B.value,L=>(P(),O(i,{label:L.label,key:L.label},{default:w(()=>[U(Te(L.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),V("div",null,[R(ce,{ref:"table",size:"default","table-key":"1","table-title":"设备列表","table-columns":j.value,"table-operate":X.value,"record-key":"windTurbineID",noPagination:!0,"table-datas":q.value,onAddRow:te,onDeleteRow:ae,onEditRow:oe,noBatchApply:!0},null,8,["table-columns","table-operate","table-datas"])]),R(h,{width:J.value,open:W.value,title:T.value,footer:"",maskClosable:!1,onCancel:b},{default:w(()=>[u.value==="batchAdd"?(P(),O(Ce,{key:0,ref:"table",size:"default","table-key":"0","table-columns":v.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:ie,onCancel:b},{footer:w(()=>t[2]||(t[2]=[])),_:1},8,["table-columns"])):(P(),O(Oe,{key:W.value,titleCol:v.value,initFormData:y.value,ref_key:"operateFormRef",ref:g,onChange:le,onSubmit:ne,onCancelForm:b,formlayout:u.value=="copyDevice"?"table":"horizontal"},null,8,["titleCol","initFormData","formlayout"]))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}},ct=Re(Be,[["__scopeId","data-v-a7907080"]]);export{ct as default};
