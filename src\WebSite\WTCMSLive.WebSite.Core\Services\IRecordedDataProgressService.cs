using WTCMSLive.WebSite.Core.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 录波数据接收进度监控服务接口
    /// </summary>
    public interface IRecordedDataProgressService
    {
        /// <summary>
        /// 开始监控录波数据接收进度
        /// </summary>
        /// <param name="request">监控请求</param>
        /// <param name="connectionId">SignalR连接ID</param>
        /// <returns>监控响应</returns>
        Task<RecordedDataMonitorResponseDTO> StartMonitoringAsync(RecordedDataMonitorRequestDTO request, string connectionId);

        /// <summary>
        /// 停止监控录波数据接收进度
        /// </summary>
        /// <param name="parkID">风场ID</param>
        /// <param name="connectionId">SignalR连接ID</param>
        /// <returns>是否成功停止</returns>
        Task<bool> StopMonitoringAsync(string parkID, string connectionId);

        /// <summary>
        /// 获取当前进度状态
        /// </summary>
        /// <param name="parkID">风场ID</param>
        /// <returns>进度列表</returns>
        Task<List<RecordedDataProgressDTO>> GetCurrentProgressAsync(string parkID);

        /// <summary>
        /// 清理连接相关的监控
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <returns></returns>
        Task CleanupConnectionAsync(string connectionId);

        /// <summary>
        /// 手动触发进度更新检查
        /// </summary>
        /// <param name="parkID">风场ID</param>
        /// <returns></returns>
        Task TriggerProgressUpdateAsync(string parkID);
    }
}
