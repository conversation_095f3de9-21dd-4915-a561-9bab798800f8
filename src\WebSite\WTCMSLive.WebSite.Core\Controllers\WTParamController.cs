﻿using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MySqlX.XDevAPI.Common;
using WindCMS.EF.WTParameter;
using WTCMSLive.BusinessModel;
using WTCMSLive.BusinessModel.WTParm;
using WTCMSLive.WebSite.Core.Models;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WTParamController : ControllerBase
    {

        /// <summary>
        /// 通过机型ID获取塔筒部件参数
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("WtTower")]
        public async Task<IActionResult> GetWtTowerParameter(string id)
        {
            var parameter = await WTParmManagement.GetWtTowerParameterByIdAsync(id);

            if (parameter == null)
            {
                return Ok(new WtTowerParameter());
            }

            return Ok(parameter);
        }


        /// <summary>
        /// 新增和修改塔筒部件参数
        /// </summary>
        /// <param name="wtTowerParameter"></param>
        /// <returns></returns>
        [HttpPost("AddOrUpdateWtTowerParameter")]
        public async Task<IActionResult> AddOrUpdateWtTowerParameter([FromBody] WtTowerParameter wtTowerParameter)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var result = await WTParmManagement.AddOrUpdateWtTowerParameterAsync(wtTowerParameter);
                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (InvalidOperationException ex)
            {
                return Ok(ApiResponse<string>.Error(ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }


        // GET: api/WtNacelleParameter/{id}
        /// <summary>
        /// 查询机舱部件参数
        /// </summary>
        /// <param name="id">机型ID</param>
        /// <returns></returns>
        [HttpGet("WtNacelle")]
        public async Task<IActionResult> GetWtNacelleParameter(string id)
        {
            var parameter = await WTParmManagement.GetWtNacelleParameterByIdAsync(id);
            if (parameter == null)
            {
                return Ok(new WtNacelleParameter());
            }
            return Ok(parameter);
        }

        // POST: api/WtNacelleParameter
        /// <summary>
        /// 新增和修改机舱部件参数
        /// </summary>
        /// <param name="wtNacelleParameter"></param>
        /// <returns></returns>
        [HttpPost("AddOrUpdateWtNacelleParameter")]
        public async Task<IActionResult> AddOrUpdateWtNacelleParameter([FromBody] WtNacelleParameter wtNacelleParameter)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var result = await WTParmManagement.AddOrUpdateWtNacelleParameterAsync(wtNacelleParameter);
                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (InvalidOperationException ex)
            {
                return Ok(ApiResponse<string>.Error(ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }


        // GET: api/WtBladeParameter/{id}
        /// <summary>
        /// 查询叶片部件参数
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("WtBlade")]
        public async Task<IActionResult> GetWtBladeParameter(string id)
        {
            var parameter = await WTParmManagement.GetWtBladeParameterByIdAsync(id);
            if (parameter == null)
            {
                return Ok(new WtBladeParameter());
            }
            return Ok(parameter);
        }


        // POST: api/WtBladeParameter
        /// <summary>
        /// 新增修改部件参数
        /// </summary>
        /// <param name="wtBladeParameter"></param>
        /// <returns></returns>
        [HttpPost("AddOrUpdateWtBladeParameter")]
        public async Task<IActionResult> AddOrUpdateWtBladeParameter([FromBody] WtBladeParameter wtBladeParameter)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var result = await WTParmManagement.AddOrUpdateWtBladeParameterAsync(wtBladeParameter);
                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (InvalidOperationException ex)
            {
                return Ok(ApiResponse<string>.Error(ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }


        [HttpPost("BatchDelTrubineModel")]
        public async Task<IActionResult> BatchDelTrubineModel([FromBody] List<string> WindTurbineModelNames)
        {
            if (WindTurbineModelNames == null || WindTurbineModelNames.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空！"));
            var allWindTurbine = WTCMSLive.BusinessModel.DevTreeManagement.GetTurbinesList();
            var inUseModels = new List<string>();
            foreach (var modelName in WindTurbineModelNames)
            {
                if (allWindTurbine.FirstOrDefault(t => t.WindTurbineModel == modelName) != null)
                {
                    inUseModels.Add(modelName);
                }
            }
            if (inUseModels.Count > 0)
            {
                return Ok(ApiResponse<string>.Error($"以下机型正在被使用，无法删除，请先删除相应机组：{string.Join(",", inUseModels)}"));
            }
            // 全部校验通过后再删除
            foreach (var modelName in WindTurbineModelNames)
            {
                bool res = await WTParmManagement.DeleteTurbineModel(modelName);
                if (!res)
                {
                    return Ok(ApiResponse<string>.Error($"机型 {modelName} 删除失败！"));
                }
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 获取齿轮箱厂家和型号
        /// </summary>
        /// <returns></returns>
        [HttpGet("GearboxModels")]
        public async Task<IActionResult> GetWtGearboxModels()
        {
            var models = await WTParmManagement.GetGearboxModelsAsync();
            // 将数据转换为所需的页面数据结构
            var result = models.GroupBy(m => m.Manufacturer)
                               .Select(group => new
                               {
                                   key = group.Key,
                                   values = group.GroupBy(m => m.Model)
                                                 .Select(subGroup => new
                                                 {
                                                     key = subGroup.Key,
                                                     values = subGroup.Select(g => new
                                                     {
                                                         key = g.GearRatio,
                                                         CreatedAt = g.CreatedAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                                                         UpdatedAt = g.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                                                     }).ToList()
                                                 }).ToList()
                               }).ToList();

            return Ok(result);
        }



    }
}
