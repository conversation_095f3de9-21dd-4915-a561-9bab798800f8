﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Models;
using WTCMSLive.IDALService;
using WTCMSLive.IDALService.MeasData;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertDAToBusinessEntityRTStatus
    {
        private static IDALService.IMDFService mdfService =
                       AppFramework.ServiceBus.ServiceLocator.GetService<IMDFService>();

        private static IDALService.IDeviceService devService =
                       AppFramework.ServiceBus.ServiceLocator.GetService<IDeviceService>();

        private static IEVDataRTService evRTService =
                      AppFramework.ServiceBus.ServiceLocator.GetService<IEVDataRTService>();

        private static IWFDataRTService wfDataRTService =
                      AppFramework.ServiceBus.ServiceLocator.GetService<IWFDataRTService>();

        

        /// <summary>
        /// 机组实时状态
        /// </summary>
        /// <param name="_entity">AlarmStatusRTTurbine</param>
        /// <returns></returns>
        public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_Turbine(WTCMSLive.Entity.Models.AlarmStatusRTTurbine _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            DeviceRTAlarmStatus _DeviceRTAlarmStatus = new DeviceRTAlarmStatus();

            _DeviceRTAlarmStatus.AlarmDegree = Convert.ToInt32(_entity.AlarmDegree);
            _DeviceRTAlarmStatus.AlarmUpdateTime =(DateTime) _entity.AlarmUpdateTime;
            _DeviceRTAlarmStatus.DevSegmentID = _entity.WindTurbineID.ToString();
            _DeviceRTAlarmStatus.DevSegmentName = _entity.DevWindTurbine.WindTurbineName;
            _DeviceRTAlarmStatus.AlarmType = GetAlarmType((short)_entity.AlarmDegree); 
            return _DeviceRTAlarmStatus;
        }

        /// <summary>
        /// 机组实时状态列表
        /// </summary>
        /// <param name="_list">AlarmStatusRTTurbine</param>
        /// <returns></returns>
        public static List<DeviceRTAlarmStatus> ConvertDeviceRTAlarmStatus_TurbineList(List<WTCMSLive.Entity.Models.AlarmStatusRTTurbine> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<DeviceRTAlarmStatus> turRTList = new List<DeviceRTAlarmStatus>();

            foreach (WTCMSLive.Entity.Models.AlarmStatusRTTurbine item in _list)
            {
                DeviceRTAlarmStatus _DeviceRTAlarmStatus = new DeviceRTAlarmStatus();

                _DeviceRTAlarmStatus.AlarmDegree = Convert.ToInt32(item.AlarmDegree);
                _DeviceRTAlarmStatus.AlarmUpdateTime = (DateTime)item.AlarmUpdateTime;
                _DeviceRTAlarmStatus.DevSegmentID = item.WindTurbineID.ToString();
                _DeviceRTAlarmStatus.DevSegmentName = item.DevWindTurbine.WindTurbineName;
                turRTList.Add(_DeviceRTAlarmStatus);
            }
            return turRTList;
        }

        public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_Component(AlarmStatusRTComponent _AlarmStatusRTComponent)
        {
            DeviceRTAlarmStatus devRTAlarmStatus = null;

            if (_AlarmStatusRTComponent != null)
            {
                devRTAlarmStatus = new DeviceRTAlarmStatus();
                devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTComponent.AlarmDegree.ToString());
                devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTComponent.AlarmDegree);
                devRTAlarmStatus.AlarmUpdateTime = (DateTime)_AlarmStatusRTComponent.AlarmUpdateTime;
                devRTAlarmStatus.DevSegmentID = _AlarmStatusRTComponent.ComponentID.ToString();
                devRTAlarmStatus.ComponentID = _AlarmStatusRTComponent.ComponentID.ToString();
                devRTAlarmStatus.DevSegmentName = _AlarmStatusRTComponent.DevTurComponent.ComponentType;
                devRTAlarmStatus.WindTurbineID = _AlarmStatusRTComponent.WindTurbineID.ToString();
                devRTAlarmStatus.ParentSegmentID = _AlarmStatusRTComponent.WindTurbineID.ToString();
            }

            return devRTAlarmStatus;
        }
        /// <summary>
        /// 转换零件测量位置
        /// </summary>
        /// <param name="_AlarmStatusRTMeasLoc"></param>
        /// <returns></returns>
        public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_MeasLocation(AlarmStatusRTMeasLoc _AlarmStatusRTMeasLoc)
        {
            DeviceRTAlarmStatus devRTAlarmStatus = null;

            if (_AlarmStatusRTMeasLoc != null)
            {
                devRTAlarmStatus = new DeviceRTAlarmStatus();
                devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTMeasLoc.AlarmDegree.ToString());
                devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTMeasLoc.AlarmDegree);
                devRTAlarmStatus.AlarmUpdateTime = (DateTime)_AlarmStatusRTMeasLoc.AlarmUpdateTime;
                devRTAlarmStatus.DevSegmentID = _AlarmStatusRTMeasLoc.MeasLocationID.ToString();
                if (_AlarmStatusRTMeasLoc.DevMeasLocVibration != null)
                {
                    devRTAlarmStatus.DevSegmentName = _AlarmStatusRTMeasLoc.DevMeasLocVibration.MeasLocationName;
                }
                devRTAlarmStatus.ComponentID = _AlarmStatusRTMeasLoc.ComponentID.ToString();
                devRTAlarmStatus.WindTurbineID = _AlarmStatusRTMeasLoc.WindTurbineID.ToString();
                devRTAlarmStatus.ParentSegmentID = _AlarmStatusRTMeasLoc.ComponentID.ToString();
            }

            return devRTAlarmStatus;
        }

        /// <summary>
        /// 转换晃度测量位置报警状态
        /// </summary>
        /// <param name="_AlarmStatusRTMeasLocSVM"></param>
        /// <returns></returns>
        public static DeviceRTAlarmStatus ConvertDeviceRTAlarmStatus_MeasLocSVM(AlarmStatusRTMeasLocSVM _AlarmStatusRTMeasLocSVM)
        {
            DeviceRTAlarmStatus devRTAlarmStatus = null;

            if (_AlarmStatusRTMeasLocSVM != null)
            {
                devRTAlarmStatus = new DeviceRTAlarmStatus();
                devRTAlarmStatus.AlarmDegree = int.Parse(_AlarmStatusRTMeasLocSVM.AlarmDegree.ToString());
                devRTAlarmStatus.AlarmType = GetAlarmType((short)_AlarmStatusRTMeasLocSVM.AlarmDegree);
                devRTAlarmStatus.AlarmUpdateTime = (DateTime)_AlarmStatusRTMeasLocSVM.AlarmUpdateTime;
                devRTAlarmStatus.DevSegmentID = _AlarmStatusRTMeasLocSVM.MeasLocationID.ToString();
                if (_AlarmStatusRTMeasLocSVM.SVMMeasLocation != null)
                {
                    devRTAlarmStatus.DevSegmentName = _AlarmStatusRTMeasLocSVM.SVMMeasLocation.MeasurementLocationName;
                }
                devRTAlarmStatus.ComponentID = _AlarmStatusRTMeasLocSVM.ComponentID.ToString();
                devRTAlarmStatus.WindTurbineID = _AlarmStatusRTMeasLocSVM.WindTurbineID.ToString();
            }

            return devRTAlarmStatus;
        }

        /// <summary>
        /// 转换零件测量位置列表
        /// </summary>
        /// <param name="_entityList"></param>
        /// <returns></returns>
        public static List<DeviceRTAlarmStatus> ConvertDeviceRT_MeasLocList(List<AlarmStatusRTMeasLoc> _entityList)
        {
            List<DeviceRTAlarmStatus> _devRTAlarmStatus = new List<DeviceRTAlarmStatus>();
            foreach (AlarmStatusRTMeasLoc item in _entityList)
            {
                _devRTAlarmStatus.Add(ConvertDeviceRTAlarmStatus_MeasLocation(item));
            }
            return _devRTAlarmStatus;
        }

        private static AlarmType GetAlarmType(short _alarmDegree)
        {
            AlarmType type = AlarmType.AlarmType_Unknown;

            if (_alarmDegree == (short)AlarmType.AlarmType_Alarm.AlarmDegree)
            {
                type = AlarmType.AlarmType_Alarm;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_CommunicationError.AlarmDegree)
            {
                type = AlarmType.AlarmType_CommunicationError;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_Normal.AlarmDegree)
            {
                type = AlarmType.AlarmType_Normal;
            }
            else if (_alarmDegree == (short)AlarmType.AlarmType_Warning.AlarmDegree)
            {
                type = AlarmType.AlarmType_Warning;
            }

            return type;

        }

        public static List<DeviceRTAlarmStatus> ConvertTurCompRTList(List<WTCMSLive.Entity.Models.AlarmStatusRTComponent> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<DeviceRTAlarmStatus> turRTList = new List<DeviceRTAlarmStatus>();

            foreach (WTCMSLive.Entity.Models.AlarmStatusRTComponent item in _list)
            {
                DeviceRTAlarmStatus _DeviceRTAlarmStatus = ConvertDeviceRTAlarmStatus_Component(item);
                turRTList.Add(_DeviceRTAlarmStatus);
                //alarmStatusRT_Turbine.AlarmType;
            }
            return turRTList;
        }

        public static WTCMSLive.BusinessEntity.AlarmEvent ConvertAlarmEvent(WTCMSLive.Entity.Models.AlarmEvent _alarmEventModel)
        {
            if (_alarmEventModel == null) return null;

            WTCMSLive.BusinessEntity.AlarmEvent alarmEvent = new WTCMSLive.BusinessEntity.AlarmEvent();

            if (!string.IsNullOrEmpty(_alarmEventModel.WindTurbineID.ToString()))
            {
                alarmEvent.WindTurbineID = _alarmEventModel.WindTurbineID.ToString();
            }
            alarmEvent.AlarmTime = _alarmEventModel.AlarmTime;
            alarmEvent.AlarmDegree = short.Parse((_alarmEventModel.AlarmDegree).ToString());
            alarmEvent.WindParkID = _alarmEventModel.WindParkID.ToString();
            alarmEvent.AlarmEventState = 
                _alarmEventModel.AlarmEventState.ToString();

            alarmEvent.WindTurbineName = devService.GetDevWindTurbineByTurID(alarmEvent.WindTurbineID).WindTurbineName;
            

            return alarmEvent;
        }

        public static List<WTCMSLive.BusinessEntity.AlarmEvent> ConvertAlarmEventList(List<Entity.Models.AlarmEvent> _alarmEventModelList)
        {
            List<WTCMSLive.BusinessEntity.AlarmEvent> alarmEventList = new List<WTCMSLive.BusinessEntity.AlarmEvent>();

            _alarmEventModelList.ForEach(
                item =>
                {
                    alarmEventList.Add(
                        ConvertAlarmEvent(item)
                        );
                }
                );
            return alarmEventList;
        }

        public static AlarmItem ConvertAlarmEventItem(AlarmEventItem _alarmEvtItem)
        {
            if (_alarmEvtItem == null) return null;

            AlarmItem alarmItem = new AlarmItem();

            if (!string.IsNullOrEmpty(_alarmEvtItem.WindTurbineID.ToString()))
            {
                alarmItem.WindTurbineID = _alarmEvtItem.WindTurbineID.ToString();
            }

            alarmItem.AlarmTime = _alarmEvtItem.AlarmTime;
            alarmItem.BeginTime = _alarmEvtItem.BeginTime;
            alarmItem.EndTime =(DateTime) _alarmEvtItem.EndTime;
            alarmItem.AlarmDegree = (int)_alarmEvtItem.AlarmDegree;

            return alarmItem;
        }

        public static List<AlarmItem> ConvertAlarmEventItemList(List<AlarmEventItem> _alarmEvtItems)
        {
            List<AlarmItem> alarmItems = new List<AlarmItem>();

            _alarmEvtItems.ForEach(
                item =>
                {
                    alarmItems.Add(
                         ConvertAlarmEventItem(item)
                        );
                }
                );
            return alarmItems;
        }
        /// <summary>
        /// 转换AlarmEventHandleLog
        /// </summary>
        /// <param name="_entity">WTCMSLive.Entity.Models.AlarmEventHandleLog实体</param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.AlarmEventHandleLog ConvertAlarmEventHanLog(
            WTCMSLive.Entity.Models.AlarmEventHandleLog _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.BusinessEntity.AlarmEventHandleLog item = new WTCMSLive.BusinessEntity.AlarmEventHandleLog();

            item.AlarmTime = _entity.AlarmTime;
            item.HandleTime = _entity.HandleTime;
            item.HandleUser = _entity.HandleUser;

            if (!string.IsNullOrEmpty(_entity.WindTurbineID))
            {
                item.WindTurbineID = _entity.WindTurbineID.ToString();
                item.WindTurbineName =devService.GetDevWindTurbineByTurID(_entity.WindTurbineID).WindTurbineName;
            }
            item.WorkDescription = _entity.WorkDescription;

            return item;
        }
        /// <summary>
        /// 转换AlarmEventHandleLog列表
        /// </summary>
        /// <param name="_list">List<WTCMSLive.Entity.Models.AlarmEventHandleLog>列表</param>
        /// <returns></returns>
        public static List<WTCMSLive.BusinessEntity.AlarmEventHandleLog> ConvertAlarmEventHanLogList(
            List<WTCMSLive.Entity.Models.AlarmEventHandleLog> _list)
        {
            List<WTCMSLive.BusinessEntity.AlarmEventHandleLog> itemList = new List<WTCMSLive.BusinessEntity.AlarmEventHandleLog>();

            foreach (WTCMSLive.Entity.Models.AlarmEventHandleLog item in _list)
            {
                itemList.Add(ConvertAlarmEventHanLog(item));
            }

            return itemList;
        }

        /// <summary>
        /// 转换诊断报告
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static TurbineDiagnosisReport ConvertTurReportDiagnosi(WTCMSLive.Entity.Models.TurbineReportDiagnosi _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            TurbineDiagnosisReport item = new TurbineDiagnosisReport();

            item.Advice = _entity.Advice;
            //item.AlarmEventHoldType = _entity.AnalyzeConclusion;
            //item.AlarmTime = _entity.DiagnosisTime;
            item.AnalyzeConclusion = _entity.AnalyzeConclusion;
            item.DiagnosisTime = _entity.DiagnosisTime;
            item.DiagnosisUser = _entity.DiagnosisUser;
            item.FileNameExtension = _entity.ExtensionFileName;

            if (_entity.WordReport != null)
            {
                item.Hasreport = true;
            }
            else
            {
                item.Hasreport = false;
            }
            //item.IsUpload = 
            //item.ReportID =
            //item.ReportLocPath = 
            if (!string.IsNullOrEmpty(_entity.WindTurbineID.ToString()))
            {
                item.WindTurbineID = _entity.WindTurbineID.ToString();
            }
            item.WordReport = _entity.WordReport;

            return item;
        }

        /// <summary>
        /// 转换诊断报告列表
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static List<TurbineDiagnosisReport> ConvertTurReportDiagnosiList(List<WTCMSLive.Entity.Models.TurbineReportDiagnosi> _list)
        {
            List<TurbineDiagnosisReport> itemList = new List<TurbineDiagnosisReport>();

            foreach (WTCMSLive.Entity.Models.TurbineReportDiagnosi item in _list)
            {
                itemList.Add(ConvertTurReportDiagnosi(item));
            }

            return itemList;
        }

        #region 风场下的诊断信息
        public static TurbineDiagnosisReport ConvertLastDiagnosisReport(WTCMSLive.Entity.Models.TurbineReportDiagnosi item)
        {
            return ConvertTurReportDiagnosi(item);
        }
        #endregion

        /// <summary>
        /// 转换维修报告
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static TurbineMaintainReport ConvertTurReportMaintain(WTCMSLive.Entity.Models.TurbineReportMaintain _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            TurbineMaintainReport item = new TurbineMaintainReport();

            item.Advice = _entity.Advice;
            //item.AlarmEventHoldType = _entity.AnalyzeConclusion;
            //item.AlarmTime = _entity.DiagnosisTime;
            item.HandleTime = _entity.HandleTime;
            item.HandleUser = _entity.HandleUser;
            item.FileNameExtension = _entity.ExtensionFileName;

            if (_entity.WordReport != null)
            {
                item.Hasreport = true;
            }
            else
            {
                item.Hasreport = false;
            }
            //item.Hasreport = 
            //item.IsUpload = 
            //item.ReportID =
            //item.ReportLocPath = 
            if (!string.IsNullOrEmpty(_entity.WindTurbineID.ToString()))
            {
                item.WindTurbineID = _entity.WindTurbineID.ToString();
            }
            item.WordReport = _entity.WordReport;

            return item;
        }

        /// <summary>
        /// 转换维修报告列表
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static List<TurbineMaintainReport> ConvertTurReportMaintainList(List<WTCMSLive.Entity.Models.TurbineReportMaintain> _list)
        {
            List<TurbineMaintainReport> itemList = new List<TurbineMaintainReport>();

            foreach (WTCMSLive.Entity.Models.TurbineReportMaintain item in _list)
            {
                itemList.Add(ConvertTurReportMaintain(item));
            }

            return itemList;
        }

        #region 风场下的维修报告信息
        public static TurbineMaintainReport GetLastTurbineReportMaintain(WTCMSLive.Entity.Models.TurbineReportMaintain item)
        {
            return ConvertTurReportMaintain(item);
        }
        #endregion

        public static LabelGroup ConvertLabelGroup(OViewLabelGroup _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            LabelGroup item = new LabelGroup();
            item.Direction = _entity.Direction;
            List<LabelData> labelDataList = new List<LabelData>();
            if (_entity.OViewLabelEVs != null)
            {
                labelDataList.AddRange(ConvertLabelData_EigenValueList(_entity.OViewLabelEVs.ToList()));
            }
            if (_entity.OViewLabelRotSpds != null)
            {
                labelDataList.AddRange(ConvertLabelData_RotSpeedList(_entity.OViewLabelRotSpds.ToList()));
            }
            if (_entity.OViewLabelWorkConditions != null)
            {
                labelDataList.AddRange(ConvertLabelData_WorkCondList(_entity.OViewLabelWorkConditions.ToList()));
            }
            item.LabelDataList = labelDataList;
            if (_entity.OViewLabelSensors != null && _entity.OViewLabelSensors.Count > 0)
            {
                item.LableTransLoc = ConvertLable_TransLocation(_entity.OViewLabelSensors.First());
            }
            item.LabelGroupID = _entity.LabelGroupID.ToString();
            item.LabelGroupName = _entity.LabelGroupName;
            if (_entity.LabelHeight != null)
            {
                item.LabelHeight = (int)_entity.LabelHeight;
            }
            if (_entity.LabelWidth != null)
            {
                item.LabelWidth = (int)_entity.LabelWidth;
            }
            if (_entity.PositionLeft != null)
            {
                item.Left = (int)_entity.PositionLeft;
            }
            if (_entity.PositionTop != null)
            {
                item.Top = (int)_entity.PositionTop;
            }
            item.WindTurbineID = _entity.WindTurbineID.ToString();

            return item;
        }

        public static List<LabelGroup> ConvertLabelGroupList(List<OViewLabelGroup> _entityList)
        {
            List<LabelGroup> itemList = new List<LabelGroup>();
            foreach (OViewLabelGroup item in _entityList)
            {
                itemList.Add(ConvertLabelGroup(item));
            }
            return itemList;
        }

        public static LabelData_EigenValue ConvertLabelData_EigenValue(OViewLabelEV _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            LabelData_EigenValue item = new LabelData_EigenValue();
            item.BorderWidth = Convert.ToInt32(_entity.Border);
            item.EigenValueCode = _entity.DataName;
            item.DataName = _entity.DataName;
            //Modify by zhanggw 无用属性，为了运行不报错，赋值。
            item.EigenValueCategory = EnumEigenValueCategory.EigenValueCategory_FreqBand;// (EnumEigenValueCategory)_entity.EigenValueType;
            item.EigenValueID = _entity.EigenValueID.ToString();
            item.LabelDataType = EnumLabelDataType.EVData;
            item.LabelGroupID = _entity.LabelGroupID.ToString();

            EVDataRT evRT=evRTService.GetEVDataRTByEVID(_entity.WindTurbineID, _entity.EigenValueID);
            if (evRT != null)
            {
                item.MeasLocVibID = evRT.MeasLocationID.ToString();
                item.EVData = ConvertEntityDAToBusinessData.ConvertEVData(evRT);
            }

            if (item.MeasLocVibID != null)
            {
                DevMeasLocVibration measLocVib = devService.GetDevMeasLocVibByMeasLocID(item.MeasLocVibID);

                if (measLocVib != null)
                {
                    item.MeasLocVibName = measLocVib.MeasLocationName;
                }
            }
            if (!string.IsNullOrEmpty(item.EigenValueID))
            {
                item.MeasLocVibID = item.EigenValueID.Substring(0,item.EigenValueID.IndexOf("&"));
                DevMeasLocVibration measLocVib = devService.GetDevMeasLocVibByMeasLocID(item.MeasLocVibID);
                if (measLocVib != null)
                {
                    item.MeasLocVibName = measLocVib.MeasLocationName;
                }
            }

            item.Order = Convert.ToInt32(_entity.OrderSeq);
            return item;
        }

        public static List<LabelData_EigenValue> ConvertLabelData_EigenValueList(List<OViewLabelEV> _entityList)
        {
            List<LabelData_EigenValue> itemList = new List<LabelData_EigenValue>();
            foreach (OViewLabelEV item in _entityList)
            {
                itemList.Add(ConvertLabelData_EigenValue(item));
            }
            return itemList;
        }

        public static LabelData_RotSpeed ConvertLabelData_RotSpeed(OViewLabelRotSpd _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            LabelData_RotSpeed item = new LabelData_RotSpeed();
            item.BorderWidth = _entity.Border.Value;
            item.DataName = _entity.DataName;
            item.LabelDataType = EnumLabelDataType.RotSpeedData;
            item.LabelGroupID = _entity.LabelGroupID.ToString();
            item.MeasLocationID = _entity.MeasLocationID.ToString();

            List<RotSpeedWaveDataRT> listRotSpdValue = wfDataRTService.GetRotSpeedWaveDataRTListByMeasLocID(_entity.WindTurbineID,_entity.MeasLocationID);

            // 测量位置名称
            DevMeasLocRotSpd measSpd = devService.GetDevMeasLocRotSpdByMeasLocID(_entity.MeasLocationID);
            if (measSpd != null)
                item.MeasLocationName = measSpd.MeasLocationName;

            item.Order = _entity.OrderSeq.Value;
            return item;
        }

        public static List<LabelData_RotSpeed> ConvertLabelData_RotSpeedList(List<OViewLabelRotSpd> _entityList)
        {
            List<LabelData_RotSpeed> itemList = new List<LabelData_RotSpeed>();
            foreach (OViewLabelRotSpd item in _entityList)
            {
                itemList.Add(ConvertLabelData_RotSpeed(item));
            }
            return itemList;
        }

        public static LabelData_WorkCond ConvertLabelData_WorkCond(OViewLabelWorkCondition _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            LabelData_WorkCond item = new LabelData_WorkCond();
            item.BorderWidth = _entity.Border.Value;
            item.DataName = _entity.DataName;
            item.LabelDataType = EnumLabelDataType.WorkingCond;
            item.LabelGroupID = _entity.LabelGroupID.ToString();
            item.MeasLocationID = _entity.MeasLocationID.ToString();
            item.Order = _entity.OrderSeq.Value;
            // 获取测量位置下工况数据
            List<WorkConditionEVDataRT> listWorkingCond = evRTService.GetWCEVDataRT(_entity.WindTurbineID).FindAll(
                c=>c.MeasLocationID==_entity.MeasLocationID);

            // 有数据
            if (listWorkingCond.Count > 0)
            {
                item.WorkingCondData = ConvertEntityDAToBusinessData.ConvertWorkConData(listWorkingCond[0]);
            }
            // 测量位置为输出功率，或者没有数据
            else
            {
                // 获取测量位置下输出功率数据
                List<WorkConditionEVDataRT> listOutPower = listWorkingCond.Where(g => g.ParamTypeCode == short.Parse(WorkCondition_ParamType.OUTPOWER.Param_Type_Code)).ToList();

                if (listOutPower.Count > 0)
                {
                    // 工况类型转换
                    item.WorkingCondData = ConvertEntityDAToBusinessData.ConvertWorkConData(listOutPower[0]);
                }
            }
            DevMeasLocProcess proLoc = devService.GetDevMeasLocProcessByMeasLocID(_entity.MeasLocationID);

            if (proLoc != null)
                item.MeasLocationName = proLoc.MeasLocationName;
            return item;
        }

        public static List<LabelData_WorkCond> ConvertLabelData_WorkCondList(List<OViewLabelWorkCondition> _entityList)
        {
            List<LabelData_WorkCond> itemList = new List<LabelData_WorkCond>();
            foreach (OViewLabelWorkCondition item in _entityList)
            {
                itemList.Add(ConvertLabelData_WorkCond(item));
            }
            return itemList;
        }

        public static Lable_TransLocation ConvertLable_TransLocation(OViewLabelSensor _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            Lable_TransLocation item = new Lable_TransLocation();
            item.LabelGroupID = _entity.LabelGroupID.ToString();
            item.MeasLocationID = _entity.MeasLocationID.ToString();
            item.TranHeight = (float)_entity.SensorHeight;
            item.TransStyleType = (int)_entity.SensorLocType;
            item.TranWidth = (float)_entity.SensorWidth;
            item.TranX = (float)_entity.SensorPosX;
            item.TranY = (float)_entity.SensorPosY;

            return item;
        }

        public static List<Lable_TransLocation> ConvertLable_TransLocationList(List<OViewLabelSensor> _entity)
        {
            List<Lable_TransLocation> itemList = new List<Lable_TransLocation>();
            foreach (OViewLabelSensor item in _entity)
            {
                itemList.Add(ConvertLable_TransLocation(item));
            }
            return itemList;
        }

        /// <summary>
        /// 诊断任务
        /// </summary>
        /// <param name="_entity">数据实体</param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.DiagnosisAssignment ConvertDiagnosisAssignment(WTCMSLive.Entity.Models.DiagnosisAssignment _entity)
        {
            WTCMSLive.BusinessEntity.DiagnosisAssignment _DiagnosisAssignment = new WTCMSLive.BusinessEntity.DiagnosisAssignment();
            if (null == _entity)
            {
                return null;
            }
            _DiagnosisAssignment.AssignmentStatus = (EnumAssignmentStatus)_entity.AssignmentStatus;
            _DiagnosisAssignment.AssignmentType = (EnumAssignmentType)_entity.AssignmentType;
            _DiagnosisAssignment.CreateTime = _entity.CreateTime;
            _DiagnosisAssignment.Diagnosiser = _entity.Diagnosiser;
            _DiagnosisAssignment.DiagnosisTime = _entity.DiagnosisTime;
            _DiagnosisAssignment.DiagnosisHandleLogsList = ConvertDiagnosisHandleLogList(_entity.DiagnosisHandleLogs.ToList());
            _DiagnosisAssignment.HitchDegree = WTCMSLive.BusinessEntity.AlarmType.GetAlarmTypeByDegree((int)_entity.HitchDegree);
            _DiagnosisAssignment.WindParkID = _entity.WindParkID;
            _DiagnosisAssignment.WindTurbineID = _entity.WindTurbineID;
            return _DiagnosisAssignment;
        }
        public static List<WTCMSLive.BusinessEntity.DiagnosisAssignment> ConvertDiagnosisAssignmentList(List<WTCMSLive.Entity.Models.DiagnosisAssignment> _entity)
        {
            List<WTCMSLive.BusinessEntity.DiagnosisAssignment> itemList = new List<WTCMSLive.BusinessEntity.DiagnosisAssignment>();
            foreach (WTCMSLive.Entity.Models.DiagnosisAssignment item in _entity)
            {
                itemList.Add(ConvertDiagnosisAssignment(item));
            }
            return itemList;
        }
        /// <summary>
        /// 诊断任务处理日志
        /// </summary>
        /// <param name="_entity">数据实体</param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.DiagnosisHandleLog ConvertDiagnosisHandleLog(WTCMSLive.Entity.Models.DiagnosisHandleLog _entity)
        {
            WTCMSLive.BusinessEntity.DiagnosisHandleLog diagnosisHandleLog = new WTCMSLive.BusinessEntity.DiagnosisHandleLog();
            diagnosisHandleLog.WindTurbineID = _entity.WindTurbineID;
            diagnosisHandleLog.CreateTime = _entity.CreateTime;
            diagnosisHandleLog.HandleTime = _entity.HandleTime;
            diagnosisHandleLog.HandleUser = _entity.HandleUser;
            diagnosisHandleLog.WorkDescription = _entity.WorkDescription;
            return diagnosisHandleLog;
        }

        public static List<WTCMSLive.BusinessEntity.DiagnosisHandleLog> ConvertDiagnosisHandleLogList(List<WTCMSLive.Entity.Models.DiagnosisHandleLog> _entity)
        {
            List<WTCMSLive.BusinessEntity.DiagnosisHandleLog> itemList = new List<WTCMSLive.BusinessEntity.DiagnosisHandleLog>();
            foreach (WTCMSLive.Entity.Models.DiagnosisHandleLog item in _entity)
            {
                itemList.Add(ConvertDiagnosisHandleLog(item));
            }
            return itemList;
        }

        public static List<WTCMSLive.BusinessEntity.WindTurbineRunLog> ConvertWindTurbineRunLogList(List<WTCMSLive.Entity.Models.WindTurbineRunLog> _entity)
        {
            List<WTCMSLive.BusinessEntity.WindTurbineRunLog> itemList = new List<WTCMSLive.BusinessEntity.WindTurbineRunLog>();
            foreach (WTCMSLive.Entity.Models.WindTurbineRunLog item in _entity)
            {
                itemList.Add(ConvertWindTurbineRunLog(item));
            }
            return itemList;
        }

        public static WTCMSLive.BusinessEntity.WindTurbineRunLog ConvertWindTurbineRunLog(Entity.Models.WindTurbineRunLog item)
        {
            if (item == null)
            {
                return null;
            }
            WTCMSLive.BusinessEntity.WindTurbineRunLog windTurbineRunLog = new WTCMSLive.BusinessEntity.WindTurbineRunLog();
            windTurbineRunLog.EventTime = item.EventTime;
            windTurbineRunLog.AlarmDegree = item.AlarmDegree;
            windTurbineRunLog.LogTitle = item.LogTitle;
            windTurbineRunLog.WindTurbineID = item.WindTurbineID;
            return windTurbineRunLog;
        }

    }
}
