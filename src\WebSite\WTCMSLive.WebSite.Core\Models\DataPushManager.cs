﻿using Newtonsoft.Json.Linq;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Linq;

namespace WTCMSLive.WebSite.Core.Models
{
    public class DataPushManager
    {
        private readonly IConfiguration _configuration;

        public DataPushManager()
        {
            // 构造函数中直接读取 appsettings.json 文件内容
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();
        }

        public PushDataMapConfig? GetDevMap(string WindParkID)
        {
            //string SqliteMapConfig = Path.Combine(_configuration.GetSection("AppSettings:serverXMConfigPath").Value, "GoldWindSqliteMapConfig.xml");
            string SqliteMapConfig = Path.Combine(_configuration.GetValue<string>("serverXMConfigPath"), "GoldWindSqliteMapConfig.xml");
            if (System.IO.File.Exists(SqliteMapConfig))
            {
                try
                {
                    var xmldata = AppFramework.Utility.XmlFileHelper<PushDataMapConfig>.Load(SqliteMapConfig);

                    // 如果机组增删，同步配置文件
                    var parkdata = DevTreeManagement.GetWindPark(WindParkID);
                    if (parkdata != null && xmldata.WindParkMap.Where(t => t.WindParkID == WindParkID).Count() > 0 && parkdata.WindTurbineList.Count > 0)
                    {
                        var _parkxml = xmldata.WindParkMap.FirstOrDefault(t => t.WindParkID == WindParkID);
                        var _parlist = parkdata.WindTurbineList.Where(t => t.WindParkID == WindParkID).ToList();
                        if (_parlist.Count > 0)
                        {
                            // 删除多余机组
                            xmldata.WindTurbineMap.RemoveAll(t => !_parlist.Select(k => k.WindTurbineID).Contains(t.WindTurbineID));
                            _parlist.ForEach(t =>
                            {
                                if (xmldata.WindTurbineMap.FirstOrDefault(k => k.WindTurbineID == t.WindTurbineID) == null)
                                {
                                    xmldata.WindTurbineMap.Add(new WindTurbineMapConfig()
                                    {
                                        WindTurbineID = t.WindTurbineID,
                                        //TargetWindTurbineID = _parkxml.TargetWindTurbineID + t.WindTurbineCode.PadLeft(3, '0'),
                                        TargetWindTurbineID = _parkxml.TargetWindTurbineID + t.WindTurbineCode.Substring(t.WindTurbineCode.Length - 3),
                                        TargetWindTurbineName = t.WindTurbineName
                                    });
                                }
                            });
                            AppFramework.Utility.XmlFileHelper<PushDataMapConfig>.Save(xmldata, SqliteMapConfig);
                        }
                    }

                    xmldata.WindParkMap = xmldata.WindParkMap.Where(t => t.WindParkID == WindParkID).ToList();
                    xmldata.WindTurbineMap = xmldata.WindTurbineMap.Where(t => t.WindTurbineID.StartsWith(WindParkID)).ToList();

                    return xmldata;
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
    }
}