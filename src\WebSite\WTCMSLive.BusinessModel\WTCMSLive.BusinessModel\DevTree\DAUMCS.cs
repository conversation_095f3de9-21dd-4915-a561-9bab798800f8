﻿using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层 主控系统, 系统组态版本
    /// </summary>
    public static class DAUMCS
    {
        /// <summary>
        /// 通过机组Id 获取该机组绑定的主控
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <returns></returns>
        public static MCS GetMCSByTurbineId(string _turbineId)
        {
            MCS mcs = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                mcs = ctx.MCSystems.Find(_turbineId);
            }
            return mcs;
        }

        public static List<MCS> GetMCSByWindParkId(string _windParkId)
        {
            List<MCS> mcsList = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                //mcsList = ctx.MCSystems.Where(item => item.WindTurbineID.Contains(_windParkId)).ToList();
                mcsList = ctx.MCSystems.ToList();
                mcsList = mcsList.Where(t => t.WindTurbineID.Contains(_windParkId)).ToList();
            }
            return mcsList;
        }

        /// <summary>
        /// 添加主控信息
        /// </summary>
        /// <param name="_DAUMCS"></param>
        public static void AddMCS(MCS _DAUMCS)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSystems.Add(_DAUMCS);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 编辑机组主控
        /// </summary>
        /// <param name="_DAUMCS"></param>
        public static void EditMCS(MCS _DAUMCS)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSystems.Attach(_DAUMCS);
                ctx.Entry(_DAUMCS).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 添加寄存器地址, 数据
        /// </summary>
        /// <param name="_MCSChanValue"></param>
        public static void AddMCSChannelValue(MCSChannelValueParam _MCSChanValue)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.Add(_MCSChanValue);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 添加寄存器地址, 状态
        /// </summary>
        /// <param name="_MCSChanValue"></param>
        public static void AddMCSChannelState(MCSChannelStateParam _MCSChanSate)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.Add(_MCSChanSate);
                ctx.SaveChanges();
            }
        }


        public static void AddMCSChannelValue(List<MCSChannelValueParam> _MCSChanValueList)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.AddRange(_MCSChanValueList);
                ctx.SaveChanges();
            }
        }

        public static void AddMCSChannelState(List<MCSChannelStateParam> _MCSChanSatelist)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.AddRange(_MCSChanSatelist);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 主控寄存器地址是否存在
        /// </summary>
        /// <param name="_MCSID"></param>
        /// <param name="_CHANNELNUMBER"></param>
        /// <returns></returns>
        public static bool IsExistMCSChannel(string _MCSID, string _CHANNELNUMBER) 
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                return ctx.MCSRegisters.Where(item => item.WindTurbineID == _MCSID && item.ChannelNumber == _CHANNELNUMBER).Count() > 0;
            }
        }

        /// <summary>
        /// 编辑主控寄存器, 数据
        /// </summary>
        /// <param name="OldMeasLocId"></param>
        /// <param name="_MCSChan"></param>
        public static void EditMCSChanValue(string OldMeasLocId, MCSChannelValueParam _MCSChanValue)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.Attach(_MCSChanValue);
                ctx.Entry(_MCSChanValue).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 编辑主控寄存器, 状态
        /// </summary>
        /// <param name="OldMeasLocId"></param>
        /// <param name="_MCSChan"></param>
        public static void EditMCSChanState(string OldMeasLocId, MCSChannelStateParam _MCSChanState)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.Attach(_MCSChanState);
                ctx.Entry(_MCSChanState).State = EntityState.Modified;
                ctx.SaveChanges();
            }
            /*
            MCSRegister mcsRegister = new MCSRegister();
            mcsRegister = ConvertEntityBusinessToDADEV.ConvertMCSRegisterState(_MCSChanState);
            //***必须先修改子表数据，不然EF会在执行主表后，把子表里面的值情况掉，变成null，子表就修改不了了
            //wangy 数据寄存器的和状态寄存器同理
            // 再修改子表
            if (mcsRegister.MCSRegisterState != null)
                MCSSvc.UpdateMCSRegisterState(mcsRegister.MCSRegisterState);
            // 先修改主表
            MCSSvc.UpdateMCSRegister(mcsRegister);*/
        }

        /// <summary>
        /// 删除主控寄存器
        /// </summary>
        /// <param name="_MCSID"></param>
        /// <param name="_chanNum"></param>
        public static void DeleteMCSChan(string _MCSID, string _paramMeaning)
        {
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSRegisters.Remove(ctx.MCSRegisters.Find(_MCSID, _paramMeaning));
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 根据机组ID删除对应主控
        /// </summary>
        /// <param name="_turbingID"></param>
        /// <returns></returns>
        public static bool DeleteMCSSystem(string _turbingID)
        {
            int count = 0;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ctx.MCSystems.Remove(ctx.MCSystems.Find(_turbingID));
                count = ctx.SaveChanges();
                ctx.MCSRegisters.RemoveRange(ctx.MCSRegisters.Where(t => t.WindTurbineID == _turbingID));
                ctx.SaveChanges();
            }
            return count > 0;
        }

        /// <summary>
        /// 获取状态寄存器对象
        /// </summary>
        /// <param name="_mcsId"></param>
        /// <param name="_paramMean"></param>
        /// <returns></returns>
        public static MCSChannelStateParam GetMCSChannelState(string _mcsId, string _paramMean)
        {
            MCSChannelStateParam stateParam = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                stateParam = ctx.MCSRegisters.OfType<MCSChannelStateParam>().FirstOrDefault(item => item.WindTurbineID == _mcsId && item.ParamMeaning == _paramMean);
            }
            return stateParam;
        }

        public static List<MCSChannelStateParam> GetMCSChannelStateListByTurID(string _mcsId)
        {
            List<MCSChannelStateParam> stateParamList = new List<MCSChannelStateParam>();
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                stateParamList = ctx.MCSRegisters.OfType<MCSChannelStateParam>().Where(item => item.WindTurbineID == _mcsId).ToList();
            }
            return stateParamList;
        }

        public static List<MCSChannelValueParam> GetMCSChannelValueListByTurID(string _mcsId)
        {
            List<MCSChannelValueParam> valueParamList = new List<MCSChannelValueParam>();
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                valueParamList = ctx.MCSRegisters.OfType<MCSChannelValueParam>().Where(item => item.WindTurbineID == _mcsId).ToList();
            }
            return valueParamList;
        }

        /// <summary>
        /// 获取数据寄存器对象
        /// </summary>
        /// <param name="_mcsId"></param>
        /// <param name="_paramMean"></param>
        /// <returns></returns>
        public static MCSChannelValueParam GetMCSChannelValue(string _mcsId, string _paramMean)
        {
            MCSChannelValueParam ValueParam = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ValueParam = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(item => item.WindTurbineID == _mcsId && item.ParamMeaning == _paramMean);
            }
            return ValueParam;
        }

        /// <summary>
        /// 获取寄存器列表
        /// </summary>
        /// <param name="_MCSID"></param>
        /// <returns></returns>
        public static List<MCSChannel> GetMCSChannelList(string _MCSID)
        {
            List<MCSChannel> mcsChannelList = new List<MCSChannel>();
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
               mcsChannelList = ctx.MCSRegisters.Where(item => item.WindTurbineID == _MCSID).ToList();
            }
            return mcsChannelList;
        }
    }
}
