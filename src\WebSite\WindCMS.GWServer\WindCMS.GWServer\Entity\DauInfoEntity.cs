﻿using CMSFramework.DAUFacadeBase;

namespace WindCMS.GWServer.Entity;

/// <summary>
/// 上报信息实体
/// </summary>
public class DauInfoEntity
{
    /// <summary>
    /// DAU Ip 信息
    /// </summary>
    public string DauIp { get; set; }
    
    /// <summary>
    /// Dau配置扩展信息
    /// </summary>
    public DauConfigExtension DauConfig { get; set; }
    
    /// <summary>
    /// 数据库脚本字节数组
    /// </summary>
    public byte[] WtLiveDbByteArray { get; set; }
}