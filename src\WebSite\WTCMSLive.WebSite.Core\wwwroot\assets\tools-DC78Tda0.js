const h=s=>{const a=[];for(const e in s){if(!s.hasOwnProperty(e))continue;const t=e.match(/^([^\[]+)\[(\d+)\]$/);if(!t)continue;const r=t[1],n=parseInt(t[2]);a[n]||(a[n]={}),a[n][r]=s[e]}return a.filter(e=>e)},f=(s,a={label:"value",value:"key"},e={nother:!1})=>{if(!s||!s.length)return[];const t=[];return a.loopSelf?s.forEach(r=>{t.push({label:r,value:r})}):s!=null&&s.length!==0&&s.forEach(r=>{let n=e.nother?{}:r;Object.keys(a).forEach(o=>{n[o]=r[a[o]]}),t.push(n)}),t},b=(s,a)=>{let e=[];return Object.keys(s).forEach(t=>{a=="key"?e.push({value:t,label:t}):a=="value"?e.push({value:s[t],label:s[t]}):a?e.push({value:s[t],label:t}):e.push({value:t,label:s[t]})}),e},g=(s,a={},e)=>{const t=[],r=new Set;return Object.keys(s).forEach(n=>{const o=n.match(/\[(\d+)\]/);o&&r.add(parseInt(o[1]))}),Array.from(r).sort().forEach(n=>{const o={...a};Object.keys(s).forEach(u=>{if(u.includes(`[${n}]`)){const c=u.replace(`[${n}]`,""),l=s[u];l&&typeof l=="object"&&l.value!==void 0?e&&e[c]?Object.keys(e[c]).forEach(p=>{if(Array.isArray(e[c][p])){let i=e[c][p];o[i[0]]=l[p][i[1]]}else o[e[c][p]]=l[p]}):o[c]=l.value:o[c]=l}}),t.push(o)}),t},d=(s={type:"",title:"",required:!1})=>{const{type:a,title:e,required:t}=s;let r=[];switch(t&&(r.push({required:!0,message:e+"是必填项！"}),r.push({pattern:/\S/,message:e+"不能为空字符串！"})),a){case"ip":r.push({pattern:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,message:"请输入正确的IP地址！"});break;case"email":r.push({pattern:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message:"请输入正确的邮箱地址！"});break;case"phone":r.push({pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码！"});break;case"number":r.push({pattern:/^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$/,message:`${e}为数字！`});break;case"postCode":r.push({pattern:/^\d{6}$/,message:"邮编格式不正确!"});break;case"integer":r.push({pattern:/^[0-9]+$/,message:"请输入正整数！"});break;case"port":r.push({pattern:/^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,message:"请输入正确的端口号！"});break}return r};export{b as a,f as b,g as f,d as g,h as t};
