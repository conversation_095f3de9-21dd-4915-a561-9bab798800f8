﻿using System.Net;
using CMSFramework.BusinessEntity;
using CMSFramework.Logger;
using CMSFramework.Repository;
using CMSFramework.TypeDef;
using WindCMS.GWServer.Agent;
using WindCMS.GWServer.Entity;
using WTCMSLive.DAUFacade;

namespace WindCMS.GWServer;

/// <summary>
/// 构建DAU上下文信息
/// </summary>
public static class BuildDauContext
{
    /// <summary>
    /// DAU 信息
    /// </summary>
    private static readonly IDAUTreeRepository DauRepository = DAUTreeRepository.Instance;
    
    /// <summary>
    /// DAU 上下文信息
    /// </summary>
    private static readonly DauContextDataProvider DauContextDataProvider = DauContextDataProvider.Single;
    
    /// <summary>
    /// 构建DAU上下文信息
    /// </summary>
    /// <param name="agent"></param>
    /// <param name="endPoint"></param>
    /// <param name="dataByteArray"></param>
    /// <returns></returns>
    public static DauWorkContext BuildContext(TcpListenerAgent agent, IPEndPoint endPoint, byte[] dataByteArray)
    {
        var ctx = new DauWorkContext();
        // 解析上报数据
        var dauStations = DauStationsEntity.ByteArrayToDauBaseParameter(dataByteArray, 6);
        agent.DauConfigExtension.DauDeviceType = Enum.Parse<EnumDAUAcqType>(dauStations.DauType.ToString());
        agent.DauConfigExtension.DauStatus = dauStations.DauStatus.GetHashCode();
        agent.DauConfigExtension.Version = dauStations.Version;
        agent.DauConfigExtension.WindParkId = dauStations.WindParkId;
        agent.DauConfigExtension.WindTurbineId = dauStations.WindTurbineId;
        agent.DauConfigExtension.IsGatewayDevice = dauStations.IsGatewayDevice;
        // 设置DAU基础信息
        ctx.Dau = new WindDAU
        {
            IP = endPoint.Address.ToString(),
            WindParkID = dauStations.WindParkId,
            WindTurbineID = dauStations.WindTurbineId,
            DAUSoftwareVersion = dauStations.Version
        };
        // 设置是否网关设备
        agent.IsGatewayDevice = agent.DauConfigExtension.IsGatewayDevice;
        // 构建 Facade
        ctx.DauFacade = Factory.Instance.GetDAUFacade(ctx.Dau, agent.TcpClient, GetVersion(dataByteArray));
        return ctx;
    }

    /// <summary>
    /// 获取DAU版本号
    /// </summary>
    /// <param name="dataByteArray"></param>
    /// <returns></returns>
    private static string GetVersion(byte[] dataByteArray)
    {
        var offset = 6;
        return $"{dataByteArray[offset++]}.{dataByteArray[offset++]}.{dataByteArray[offset++]}.{dataByteArray[offset]}";
    }

    // /// <summary>
    // /// 构建 DataPump
    // /// </summary>
    // /// <param name="agent"></param>
    // public static void BuildDataPump(TcpListenerAgent agent)
    // {
    //     var dauWorkContext = agent.DauWorkContext;
    //     if (dauWorkContext.DataPump != null)
    //     {
    //         return;
    //     }
    //     
    //     if (dauWorkContext.Dau != null)
    //     {
    //         dauWorkContext.DataPump = WTCMSLive.DAUFacade.Factory.Instance.GetDauMeasDataPump(dauWorkContext);
    //     }
    // }

    /// <summary>
    /// 获取DAU信息
    /// </summary>
    /// <param name="windTurbineId"></param>
    /// <param name="dauId"></param>
    /// <returns></returns>
    public static WindDAU? GetDau(string windTurbineId, string dauId)
    {
        List<WindDAU> dauList = DauRepository.GetAllEnabledDAUNoCache(EnumDAUType.Vibration);
        return dauList.FirstOrDefault(dau => dau.WindTurbineID == windTurbineId && dau.DauID == dauId);
    }

    /// <summary>
    /// 从数据库读取相关信息
    /// </summary>
    /// <param name="dau"></param>
    /// <param name="ctx"></param>
    public static void ReloadDauContext(WindDAU? dau, DauWorkContext ctx)
    {
        // 获取机组
        var windTurbine = DauContextDataProvider.AllWindTurbineList.Find(wt => wt.WindTurbineID == dau.WindTurbineID);
        ctx.DauTurbine = windTurbine ?? new WindTurbine();

        // 测量定义 by steel 考虑测量定义的禁用状态
        ctx.AllEnableMdf = DauContextDataProvider.AllMDFList.FindAll(t =>
            t.IsAvailable && t.WindTurbineID == dau.WindTurbineID && t.Mdf_Ex.DauID == dau.DauID);
        ctx.MDFList = DauContextDataProvider.AllMDFList.FindAll(mdf => mdf.WindTurbineID == dau.WindTurbineID);
        ctx.MDFList = ctx.MDFList.FindAll(mdf => IsMdfOfCurrentDau(mdf, dau));
        ctx.MDFList = ctx.MDFList.FindAll(m => m.IsAvailable);

        Logger.LogDebugMessage($"dau {ctx.Dau.IP} has {ctx.MDFList.Count} mdfs");

        // 主控
        var ctxMainCtrlSys = DauContextDataProvider.AllMcsList.Find(cs => cs.WindTurbineID == dau.WindTurbineID);
        ctx.MainCtrlSys = ctxMainCtrlSys ?? new MCS();

        // SVM 
        ctx.SvmUnitList = DauContextDataProvider.AllSVMUnitList.FindAll(svm => IsSvMofCurrentDau(dau, svm));

        // 油液
        ctx.OilUnitList = DauContextDataProvider.AllOilUnitList.FindAll(oil => oil.WindTurbineID == dau.WindTurbineID);

        CheckIfSvmConfigOk(ctx);
    }

    /// <summary>
    /// 判断测量定义是否为当前DAU的测量定义
    /// </summary>
    /// <param name="mdf"></param>
    /// <param name="dau"></param>
    /// <returns></returns>
    private static bool IsMdfOfCurrentDau(MeasDefinition mdf, WindDAU dau)
    {
        Logger.LogDebugMessage(
            $"Map mdf ID={mdf.MeasDefinitionID}  mdf.ex is null={mdf.Mdf_Ex == null} vib cnt= {mdf.WaveDefList_Time.Count} current dauID {dau.DauID}");
        if (mdf.WaveDefList_Time.Count > 0)
        {
            string measLocationID = mdf.WaveDefList_Time.FirstOrDefault().MeasLocationID;
            //当前DAU通道列表中包含测量定义中的振动测量位置，则返回true
            if (dau.DAUChannelList.FirstOrDefault(c => c.MeasLocVibID == measLocationID) != null
                || dau.VoltageCurrentList.FirstOrDefault(c => c.MeasLoc_ProcessId == measLocationID) != null)
            {
                return true;
            }
        }
        else if (mdf.Mdf_Ex != null)
        {
            var result = mdf.Mdf_Ex.DauID == dau.DauID;
            if (result)
            {
                Logger.LogDebugMessage(
                    $"MDF Mapping OK --> mdfID {mdf.MeasDefinitionID} mdfName{mdf.MeasDefinitionName} dauid {dau.DauID}");
            }
            else
            {
                Logger.LogDebugMessage(
                    $"MDF Mapping not ok --> mdfID {mdf.MeasDefinitionID} mdfName{mdf.MeasDefinitionName} dauid {dau.DauID}");
            }

            return result;
        }
        else
        {
            Logger.LogDebugMessage(string.Format(
                "[ReloadDauContext] WT({0}) mdfID={1} WaveDefList_Time.Count <= 0 And mdf.Mdf_Ex==null",
                mdf.WindTurbineID, mdf.MeasDefinitionID));
        }

        return false;
    }

    /// <summary>
    /// 检查当前晃动仪是否是当前DAU
    /// </summary>
    /// <param name="dau"></param>
    /// <param name="svm"></param>
    /// <returns></returns>
    private static bool IsSvMofCurrentDau(WindDAU dau, SVMUnit svm)
    {
        return svm.AssocWindTurbineID == dau.WindTurbineID
               && string.IsNullOrEmpty(svm.ModbusUnit.DauID) == false
               && svm.ModbusUnit.DauID == dau.DauID;
    }

    /// <summary>
    /// 目前一个dau只支持一个晃度仪
    /// </summary>
    /// <param name="ctx"></param>
    private static void CheckIfSvmConfigOk(DauWorkContext ctx)
    {
        var wrdSvmLs = ctx.SvmUnitList.FindAll(s => s.ModbusUnit.ModbusDevType == EnumModbusDevType.StaticSVM
                                                    || s.ModbusUnit.ModbusDevType == EnumModbusDevType.DynamicSVM);

        if (wrdSvmLs.Count > 1)
        {
            throw new Exception(
                $"DAU only suppport one SVM dev at present, but it's {wrdSvmLs.Count} now. Please check if svm conig is OK.");
        }
    }
}