using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 特征值响应DTO
    /// </summary>
    public class EigenValueResponseDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string TurbineID { get; set; }

        /// <summary>
        /// 振动特征值数据
        /// </summary>
        public List<VibrationEigenValueDTO> VibrationData { get; set; } = new List<VibrationEigenValueDTO>();

        /// <summary>
        /// SVM特征值数据
        /// </summary>
        public List<SVMEigenValueDTO> SVMData { get; set; } = new List<SVMEigenValueDTO>();

        /// <summary>
        /// 数据获取时间
        /// </summary>
        public DateTime DataTime { get; set; }
    }

    /// <summary>
    /// 振动特征值DTO
    /// </summary>
    public class VibrationEigenValueDTO
    {
        /// <summary>
        /// 测量位置ID
        /// </summary>
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 测量位置名称
        /// </summary>
        public string MeasLocationName { get; set; }

        /// <summary>
        /// 特征值代码
        /// </summary>
        public string EigenValueCode { get; set; }

        /// <summary>
        /// 特征值名称
        /// </summary>
        public string EigenValueName { get; set; }

        /// <summary>
        /// 特征值
        /// </summary>
        public double EigenValue { get; set; }

        /// <summary>
        /// 工程单位
        /// </summary>
        public string EngUnit { get; set; }

        /// <summary>
        /// 工程单位名称
        /// </summary>
        public string EngUnitName { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>
        public DateTime AcquisitionTime { get; set; }

        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 测量定义ID
        /// </summary>
        public string MeasDefinitionID { get; set; }

        /// <summary>
        /// 报警等级
        /// </summary>
        public int AlarmDegree { get; set; }

        /// <summary>
        /// 报警等级描述
        /// </summary>
        public string AlarmDegreeDescription { get; set; }

        /// <summary>
        /// 报警颜色
        /// </summary>
        public string AlarmColor { get; set; }

        /// <summary>
        /// 数据类型（用于分类）
        /// </summary>
        public string DataType { get; set; } = "Vibration";
    }

    /// <summary>
    /// SVM特征值DTO
    /// </summary>
    public class SVMEigenValueDTO
    {
        /// <summary>
        /// 测量位置ID
        /// </summary>
        public string MeasLocationID { get; set; }

        /// <summary>
        /// 测量位置名称
        /// </summary>
        public string MeasLocationName { get; set; }

        /// <summary>
        /// 特征值代码
        /// </summary>
        public string EigenValueCode { get; set; }

        /// <summary>
        /// 特征值名称
        /// </summary>
        public string EigenValueName { get; set; }

        /// <summary>
        /// 特征值
        /// </summary>
        public double EigenValue { get; set; }

        /// <summary>
        /// 工程单位
        /// </summary>
        public string EngUnit { get; set; }

        /// <summary>
        /// 工程单位名称
        /// </summary>
        public string EngUnitName { get; set; }

        /// <summary>
        /// 采集时间
        /// </summary>
        public DateTime AcquisitionTime { get; set; }

        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 测量定义ID
        /// </summary>
        public string MeasDefinitionID { get; set; }

        /// <summary>
        /// 报警等级
        /// </summary>
        public int AlarmDegree { get; set; }

        /// <summary>
        /// 报警等级描述
        /// </summary>
        public string AlarmDegreeDescription { get; set; }

        /// <summary>
        /// 报警颜色
        /// </summary>
        public string AlarmColor { get; set; }

        /// <summary>
        /// 数据类型（用于分类）
        /// </summary>
        public string DataType { get; set; } = "SVM";

        /// <summary>
        /// SVM参数类型
        /// </summary>
        public string SVMParamType { get; set; }
    }
}
