﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel.MeasData
{
    // author:<PERSON><PERSON><PERSON><PERSON>
    // create:2011-06-08
    /// <summary>
    /// 工况类型实体
    /// </summary>
    [DataContract(Namespace = "http://CMSFramework.BusinessEntity/")]
    public class WorkCondition_ParamType
    {
        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// Param_Type_Code 编号
        /// </summary>
        [DataMember]
        public string Param_Type_Code
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// name 名称
        /// </summary>
        [DataMember]
        public string Name
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// Unit 单位
        /// </summary>
        [DataMember]
        public string Unit
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// IsDefault 是否默认
        /// </summary>
        [DataMember]
        public bool IsDefault
        {
            get;
            set;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        // author: GuoKaile
        // time: 2012-08-16
        /// <summary>
        /// IsStateParam 是否状态参数
        /// </summary>
        [DataMember]
        public bool IsStateParam
        {
            get;
            set;
        }
        public EnumWorkCondition_ParamType ParmaType
        {
            get;
            set;
        }

        // modified by whr @2013-11-19 “修改工况参数编号（对应CMS工况编号）”
        private static WorkCondition_ParamType NOWORKCONDTION = new WorkCondition_ParamType() { Param_Type_Code = "99", Name = "无工况", ParmaType = EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION };
        private static WorkCondition_ParamType WINDSPEED = new WorkCondition_ParamType() { Param_Type_Code = "0", Name = "风速", Unit = "m/s", IsDefault = true, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_WindSpeed };
        private static WorkCondition_ParamType OUTPOWER = new WorkCondition_ParamType() { Param_Type_Code = "1", Name = "输出功率", Unit = "KW", IsDefault = true, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_Power };
        private static WorkCondition_ParamType ROTSPEED = new WorkCondition_ParamType() { Param_Type_Code = "3", Name = "发电机转速", Unit = "RPM", IsDefault = true, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_RotSpeed };
        private static WorkCondition_ParamType YAWADDRESS = new WorkCondition_ParamType() { Param_Type_Code = "4", Name = "偏航状态", Unit = null, IsDefault = true, IsStateParam = true, ParmaType = EnumWorkCondition_ParamType.WCPT_YAWState };
        private static WorkCondition_ParamType T_MAINBEARING = new WorkCondition_ParamType() { Param_Type_Code = "2", Name = "主轴承温度", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_MBRTemp };
        private static WorkCondition_ParamType T_GEARBOXHIGHTAXIS = new WorkCondition_ParamType() { Param_Type_Code = "5", Name = "齿轮箱高速轴温度", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GB_HSSTemp };
        private static WorkCondition_ParamType T_GENERATOROIL = new WorkCondition_ParamType() { Param_Type_Code = "6", Name = "齿轮箱油温", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GB_OilTemp };
        private static WorkCondition_ParamType T_GENERATORDRIVER = new WorkCondition_ParamType() { Param_Type_Code = "7", Name = "发电机驱动端轴承温度", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GEN_DETemp };
        private static WorkCondition_ParamType T_GENERATORNONDRIVER = new WorkCondition_ParamType() { Param_Type_Code = "8", Name = "发电机非驱动端轴承温度", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GEN_NDETemp };
        private static WorkCondition_ParamType T_STATOR1 = new WorkCondition_ParamType() { Param_Type_Code = "9", Name = "发电机定子温度1", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GEN_STR1Temp };
        private static WorkCondition_ParamType T_STATOR2 = new WorkCondition_ParamType() { Param_Type_Code = "10", Name = "发电机定子温度2", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GEN_STR2Temp };
        private static WorkCondition_ParamType T_STATOR3 = new WorkCondition_ParamType() { Param_Type_Code = "11", Name = "发电机定子温度3", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_GEN_STR3Temp };
        private static WorkCondition_ParamType T_ENGINEROOM = new WorkCondition_ParamType() { Param_Type_Code = "12", Name = "机舱温度", Unit = "℃", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_NAC_Temp };
        private static WorkCondition_ParamType T_PITCHBLADEANGLE1 = new WorkCondition_ParamType() { Param_Type_Code = "13", Name = "桨距角1", Unit = "°", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_BLD_Angle1 };
        private static WorkCondition_ParamType V_NacelleDirection = new WorkCondition_ParamType() { Param_Type_Code = "14", Name = "航向角", Unit = "°", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_NAC_Direction };
        private static WorkCondition_ParamType BladeTemp01 = new WorkCondition_ParamType() { Param_Type_Code = "15", Name = "叶片1温度", Unit = "Ω", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_Blade_01Temp };
        private static WorkCondition_ParamType BladeTemp02 = new WorkCondition_ParamType() { Param_Type_Code = "16", Name = "叶片2温度", Unit = "Ω", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_Blade_02Temp };
        private static WorkCondition_ParamType BladeTemp03 = new WorkCondition_ParamType() { Param_Type_Code = "17", Name = "叶片3温度", Unit = "Ω", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_Blade_03Temp };
        private static WorkCondition_ParamType HubTemp = new WorkCondition_ParamType() { Param_Type_Code = "18", Name = "轮毂温度", Unit = "Ω", IsDefault = false, IsStateParam = false, ParmaType = EnumWorkCondition_ParamType.WCPT_HubTemp };
        public static List<WorkCondition_ParamType> GetWorkConParmeTypeList()
        {
            List<WorkCondition_ParamType> workConParmeTypeList = new List<WorkCondition_ParamType>();

            workConParmeTypeList.Add(WINDSPEED);
            workConParmeTypeList.Add(OUTPOWER);
            workConParmeTypeList.Add(ROTSPEED);
            workConParmeTypeList.Add(YAWADDRESS);
            workConParmeTypeList.Add(T_MAINBEARING);
            workConParmeTypeList.Add(T_GEARBOXHIGHTAXIS);
            workConParmeTypeList.Add(T_GENERATOROIL);
            workConParmeTypeList.Add(T_GENERATORDRIVER);
            workConParmeTypeList.Add(T_GENERATORNONDRIVER);
            workConParmeTypeList.Add(T_STATOR1);
            workConParmeTypeList.Add(T_STATOR2);
            workConParmeTypeList.Add(T_STATOR3);
            workConParmeTypeList.Add(T_ENGINEROOM);
            workConParmeTypeList.Add(T_PITCHBLADEANGLE1);
            workConParmeTypeList.Add(V_NacelleDirection);
            workConParmeTypeList.Add(NOWORKCONDTION);
            workConParmeTypeList.Add(BladeTemp01);
            workConParmeTypeList.Add(BladeTemp02);
            workConParmeTypeList.Add(BladeTemp03);
            workConParmeTypeList.Add(HubTemp);
            return workConParmeTypeList;
        }
    }
}
