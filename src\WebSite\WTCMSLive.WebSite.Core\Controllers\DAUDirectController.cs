using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.DTOs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Services;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using System.Data.SQLite;
using WindCMS.GWServer.ListenerServer;
using System.Collections.Generic;
using WTCMSLive.BusinessModel;
using CMSFramework.DAUEntities;
using CMSFramework.MeasDefEntities;
using CMSFramework.BusinessEntity;
using static System.Net.WebRequestMethods;
using System;
using System.Reflection;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DAUDirectController : ControllerBase
    {
        private readonly ILogger<DAUDirectController> _logger;
        private readonly IRecordedDataProgressService _progressService;
        private readonly TurbineConfigDistributionService _distributionService;
        private readonly TurbineConfigSyncService _syncService;

        public DAUDirectController(
            ILogger<DAUDirectController> logger,
            IRecordedDataProgressService progressService,
            TurbineConfigDistributionService distributionService,
            TurbineConfigSyncService syncService)
        {
            _logger = logger;
            _progressService = progressService;
            _distributionService = distributionService;
            _syncService = syncService;
        }


        #region 设备管理

        /// <summary>
        /// 启动采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StartAcquisition")]
        public IActionResult StartAcquisition([FromBody] List<DAUDirectDTO> daus)
        {
            Dictionary<string,bool> result = new Dictionary<string,bool>();
            foreach (DAUDirectDTO dau in daus)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(dau.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                result[key] = agent?.ListenerWriteHandler.StartDaq() ?? false;
            }
            return Ok(ApiResponse<Dictionary<string,bool>>.Success(result));

        }


        /// <summary>
        /// 停止采集
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("StopAcquisition")]
        public IActionResult StopAcquisition([FromBody] List<DAUDirectDTO> daus)
        {
            Dictionary<string, bool> result = new Dictionary<string, bool>();
            foreach (DAUDirectDTO dau in daus)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(dau.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                result[key] = agent?.ListenerWriteHandler.StopDaq() ?? false;
            }
            return Ok(ApiResponse<Dictionary<string, bool>>.Success(result));

        }

        /// <summary>
        /// 获取录波数据
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("GetWaveFormData")]
        public IActionResult GetWaveFormData([FromBody] List<DAUDirectDTO> daus)
        {
            Dictionary<string, bool> result = new Dictionary<string, bool>();
            foreach (DAUDirectDTO dau in daus)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(dau.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                result[key] = agent?.ListenerWriteHandler.StartGetRecorded() ?? false;
            }

            return Ok(ApiResponse<Dictionary<string, bool>>.Success(result));
        }


        /// <summary>
        /// 测量定义下发
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>
        [HttpPost("SetMeasureDefinition")]
        public async Task<IActionResult> SetMeasureDefinitionAsync([FromBody] List<DAUDirectDTO> daus)
        {

            Dictionary<string, bool> result = new Dictionary<string, bool>();
            var sqltieLists = await _distributionService.GenerateConfigDatabases(daus.Select(t => t.WindTurbineID).Distinct().ToList());
            foreach (DAUDirectDTO dau in daus)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(dau.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                

                if (sqltieLists.ContainsKey(dau.WindTurbineID))
                {
                    // 发送数据库
                    result[key] = agent?.ListenerWriteHandler.SendMDF(sqltieLists[dau.WindTurbineID]) ?? false;
                }
            }


            
            return Ok(ApiResponse<Dictionary<string, bool>>.Success(result));

        }

        /// <summary>
        /// 推送配置
        /// </summary>
        /// <param name="daus"></param>
        /// <returns></returns>

        [HttpPost("SetSFTPConfig")]
        public IActionResult SetSFTPConfig([FromBody] List<DAUSFTPConfigDTO> sftpConfigs)
        {

            Dictionary<string, bool> result = new Dictionary<string, bool>();
            foreach (DAUSFTPConfigDTO _sftp in sftpConfigs)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(_sftp.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                if(agent != null)
                {
                    agent.DauConfigExtension.SftpUserName = _sftp.SftpUsername??"";
                    agent.DauConfigExtension.SftpPassword = _sftp.SftpPassword;
                    agent.DauConfigExtension.SftPort = int.Parse(_sftp.SftpPort ?? "22");
                   
                    agent.DauConfigExtension.SftpServiceIpAddress = _sftp.SftpAddress;

                    agent.DauConfigExtension.BvmSftpPushPath = _sftp.BvmPushPath;
                    agent.DauConfigExtension.CvmSftpPushPath = _sftp.CvmPushPath;
                    agent.DauConfigExtension.TvmSftpPushPath = _sftp.TvmPushPath;

                    var agentRes = agent.ListenerWriteHandler.SetDauPushParameter();
                    result[key] = agentRes;

                    // 存储到数据库
                    if (agentRes)
                    {
                        DauManagement.AddOrUpdateDAUSFTPConfig(new CMSFramework.DAUEntities.DAUSftpConfig()
                        {
                            SftpAddress = _sftp.SftpAddress ?? "",
                            DauID = _sftp.DauID,
                            SftpPassword = _sftp.SftpPassword,
                            SftpPort = _sftp.SftpPort ?? "22",
                            SftpUsername = _sftp.SftpUsername,
                            WindTurbineID = _sftp.WindTurbineID,

                            CvmPushPath = _sftp.CvmPushPath,
                            TvmPushPath = _sftp.TvmPushPath,
                            BvmPushPath = _sftp.BvmPushPath,
                        }) ;
                    }
                }
                else
                {
                    result[key] = false; 
                }
                
            }
            return Ok(ApiResponse<Dictionary<string, bool>>.Success(result));

        }


        /// <summary>
        /// 高级配置
        /// </summary>
        /// <returns></returns>
        [HttpPost("SetAdvancedParameters")]
        public IActionResult SetAdvancedParameters([FromBody] List<AdvancedParameterDTO> advanceds)
        {

            Dictionary<string, bool> result = new Dictionary<string, bool>();
            foreach (AdvancedParameterDTO _adconfig in advanceds)
            {
                var key = TcpListenerServer.GetTcpListenerContextKey(_adconfig.IP);
                var agent = TcpListenerServer.GetTcpListenerAgent(key);
                if (agent != null)
                {
                    agent.DauConfigExtension.TimeSynchronizationServiceIp = _adconfig.TimeServerIP;
                    agent.DauConfigExtension.TimeSynchronizationServicePort = _adconfig.TimeServerPort;
                    agent.DauConfigExtension.RecordedDays = (ushort)_adconfig.RecordedDays;
                    agent.DauConfigExtension.EnableRecordedDays = (bool)_adconfig.EnableRecordedDays;

                    var agentRes = agent.ListenerWriteHandler.SetDauCollectionStrategyRequest();
                    result[key] = agentRes;

                    // 存储到数据库
                    if (agentRes)
                    {
                        List<DauExtendConfig> extendConfigs = new List<DauExtendConfig>();
                        DauManagement.DelteDauExtendConfig(_adconfig.WindTurbineID,_adconfig.DauID);
                        if (!string.IsNullOrEmpty(_adconfig.TimeServerIP))
                        {
                            extendConfigs.Add(new DauExtendConfig()
                            {
                                DauID = _adconfig.DauID,
                                WindTurbineID = _adconfig.WindTurbineID,
                                Key = EnumDauExtendConfig.TimeSynchronizationServiceIp.GetHashCode().ToString(),
                                Value = _adconfig.TimeServerIP,
                            });
                        }
                        if (_adconfig.TimeServerPort != null)
                        {
                            extendConfigs.Add(new DauExtendConfig()
                            {
                                DauID = _adconfig.DauID,
                                WindTurbineID = _adconfig.WindTurbineID,
                                Key = EnumDauExtendConfig.TimeSynchronizationServicePort.GetHashCode().ToString(),
                                Value = _adconfig.TimeServerPort.ToString(),
                            });
                        }

                        if (_adconfig.RecordedDays != null)
                        {
                            extendConfigs.Add(new DauExtendConfig()
                            {
                                DauID = _adconfig.DauID,
                                WindTurbineID = _adconfig.WindTurbineID,
                                Key = EnumDauExtendConfig.RecordedDays.GetHashCode().ToString(),
                                Value = _adconfig.RecordedDays.ToString(),
                            });
                        }

                        if (_adconfig.EnableRecordedDays != null)
                        {
                            extendConfigs.Add(new DauExtendConfig()
                            {
                                DauID = _adconfig.DauID,
                                WindTurbineID = _adconfig.WindTurbineID,
                                Key = EnumDauExtendConfig.EnableRecordedDays.GetHashCode().ToString(),
                                Value = _adconfig.EnableRecordedDays.ToString(),
                            });
                        }
                        DauManagement.AddDauExtendConfig(extendConfigs);
                    }
                }
                else
                {
                    result[key] = false;
                }

            }
            return Ok(ApiResponse<Dictionary<string, bool>>.Success(result));

        }


        #endregion

        #region 录波数据进度监控

        /// <summary>
        /// 获取录波数据接收进度
        /// </summary>
        /// <param name="parkID">风场ID</param>
        /// <returns></returns>
        [HttpGet("GetRecordedDataProgress")]
        public async Task<IActionResult> GetRecordedDataProgress(string parkID)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(parkID))
                {
                    return BadRequest(ApiResponse<object>.Error("风场ID不能为空"));
                }

                var progressList = await _progressService.GetCurrentProgressAsync(parkID);
                return Ok(ApiResponse<List<RecordedDataProgressDTO>>.Success(progressList));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取录波数据进度失败: {ParkID}", parkID);
                return BadRequest(ApiResponse<object>.Error($"获取进度失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 触发录波数据进度更新
        /// </summary>
        /// <param name="parkID">风场ID</param>
        /// <returns></returns>
        [HttpPost("TriggerProgressUpdate")]
        public async Task<IActionResult> TriggerProgressUpdate([FromBody] string parkID)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(parkID))
                {
                    return BadRequest(ApiResponse<object>.Error("风场ID不能为空"));
                }

                await _progressService.TriggerProgressUpdateAsync(parkID);
                return Ok(ApiResponse<object>.Success("进度更新已触发"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发录波数据进度更新失败: {ParkID}", parkID);
                return BadRequest(ApiResponse<object>.Error($"触发更新失败: {ex.Message}"));
            }
        }

        #endregion

        #region 数据验证

        /// <summary>
        /// 获取dau sftp推送路径
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="dauID"></param>
        /// <returns></returns>
        [HttpGet("GetDAUSFTPDATAPath")]
        public Dictionary<string,string> GetDAUSFTPDATAPath(string turbineID,string dauID)
        {
            Dictionary<string, string> res = new();
            var sftp = DauManagement.GetDAUSftpConfigByTurbineID(turbineID, dauID);
            if (sftp == null)
            {
                return res;
            }
            foreach(PropertyInfo prop in sftp.GetType().GetProperties())
            {
                if (prop.Name.Contains("PushPath") && !res.ContainsKey(prop.Name) && prop.GetValue(sftp)!=null)
                {
                    res.Add(prop.Name, prop.GetValue(sftp)?.ToString()??"");
                }
            }
            return res;
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="sTime">开始时间</param>
        /// <param name="eTime">结束时间</param>
        /// <returns></returns>
        [HttpGet("GetWaveDBFileList")]
        public async Task<IActionResult> GetWaveDBFileList(
            string parkID,
            DateTime sTime,
            DateTime eTime,
            string sfttyePath,
            [FromQuery] string? turbineID,
            [FromQuery] string? dauID)
        {
            try
            {
                DAUSftpConfig? sftp = null;
                // 如果不查询机组，则查询推送服务器的sftp配置（dau推送）
                if (string.IsNullOrEmpty(turbineID))
                {
                    sftp = DauManagement.GetDAUSftpConfigByParkID(parkID);
                }
                else
                {
                    sftp = DauManagement.GetDAUSftpConfigByTurbineID(turbineID,dauID);
                    // 查询dau的ip ，连接到网关
                    WindDAU windDAU = DauManagement.GetDAUNameById(turbineID, dauID);
                    if (windDAU != null)
                    {
                        sftp.SftpAddress = windDAU.IP;
                    }
                    else
                    {
                        return Ok(ApiResponse<object>.Error($"未查询到采集器配置"));
                    }
                }
                // SFTP服务器配置
                string sftpHost = sftp.SftpAddress; 
                int sftpPort = int.Parse(sftp.SftpPort);
                string sftpPath = sfttyePath;
                string sftpUser = sftp.SftpUsername;
                string sftpPwd = sftp.SftpPassword;

                _logger.LogInformation("开始连接SFTP服务器: {Host}:{Port}, 用户: {User}, 路径: {Path}",
                    sftpHost, sftpPort, sftpUser, sftpPath);

                // 创建SFTP连接
                using var client = new SftpClient(sftpHost, sftpPort, sftpUser, sftpPwd);

                // 设置连接超时
                client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(30);

                try
                {
                    await Task.Run(() => client.Connect());
                }
                catch (Renci.SshNet.Common.SshConnectionException ex)
                {
                    _logger.LogError(ex, "SFTP连接失败: {Host}:{Port}", sftpHost, sftpPort);
                    return Ok(ApiResponse<object>.Error("SFTP服务器连接失败"));
                }
                catch (Renci.SshNet.Common.SshAuthenticationException ex)
                {
                    _logger.LogError(ex, "SFTP认证失败: 用户 {User}", sftpUser);
                    return Ok(ApiResponse<object>.Error("SFTP认证失败"));
                }

                if (!client.IsConnected)
                {
                    _logger.LogError("SFTP连接失败，连接状态为false");
                    return Ok(ApiResponse<object>.Error("SFTP连接失败"));
                }

                _logger.LogInformation("SFTP连接成功，开始扫描目录: {Path}", sftpPath);

                // 检查根目录是否存在
                try
                {
                    if (!client.Exists(sftpPath))
                    {
                        _logger.LogWarning("指定的SFTP路径不存在: {Path}", sftpPath);
                        return Ok(ApiResponse<object>.Error($"路径不存在: {sftpPath}"));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "检查SFTP路径时发生错误: {Path}", sftpPath);
                    return Ok(ApiResponse<object>.Error("检查路径失败"));
                }

                // 递归扫描目录并构建文件树
                var rootNode = await ScanDirectoryRecursiveSimple(client, sftpPath, sTime, eTime);

                try
                {
                    client.Disconnect();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "断开SFTP连接时发生警告");
                }

                _logger.LogInformation("SFTP文件扫描完成，共找到 {TotalFileCount} 个文件", rootNode.TotalFileCount);

                // 直接返回层级数据，不使用ApiResponse包装
                return Ok(rootNode);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "SFTP操作超时");
                return Ok(ApiResponse<object>.Error("操作超时"));
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "SFTP连接参数错误");
                return Ok(ApiResponse<object>.Error("连接参数错误"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取SFTP文件列表时发生未知错误");
                return Ok(ApiResponse<object>.Error($"获取文件列表失败: {ex.Message}"));
            }
        }

        #endregion
      
        #region 私有辅助方法

        /// <summary>
        /// 递归扫描目录并构建简化的文件树
        /// </summary>
        /// <param name="client">SFTP客户端</param>
        /// <param name="currentPath">当前扫描路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        private async Task<SftpDirectoryNodeDTO> ScanDirectoryRecursiveSimple(SftpClient client, string currentPath, DateTime startTime, DateTime endTime)
        {
            var currentDirName = Path.GetFileName(currentPath);
            if (string.IsNullOrEmpty(currentDirName))
                currentDirName = "CMSDATA";

            var result = new SftpDirectoryNodeDTO
            {
                Name = currentDirName,
                Path = currentPath,
                IsDirectory = true,
                LastModified = null,
                TotalFileCount = 0
            };

            try
            {
                _logger.LogDebug("扫描目录: {Path}", currentPath);

                // 获取当前目录下的所有项目
                var items = await Task.Run(() => client.ListDirectory(currentPath));

                foreach (var item in items.Where(i => i.Name != "." && i.Name != ".."))
                {
                    try
                    {
                        if (item.IsDirectory)
                        {
                            // 递归扫描子目录
                            var subDirNode = await ScanDirectoryRecursiveSimple(client, item.FullName, startTime, endTime);
                            result.Children.Add(subDirNode);
                            // 累加子目录的文件数量
                            result.TotalFileCount += subDirNode.TotalFileCount;
                        }
                        else if (item.IsRegularFile)
                        {
                            // 检查文件时间是否在范围内
                            if (item.LastWriteTime >= startTime && item.LastWriteTime <= endTime)
                            {
                                var fileNode = new SftpDirectoryNodeDTO
                                {
                                    Name = item.Name,
                                    Path = item.FullName,
                                    IsDirectory = false,
                                    Size = item.Length,
                                    LastModified = item.LastWriteTime,
                                    TotalFileCount = 1 // 文件节点的文件数量为1
                                };
                                result.Children.Add(fileNode);
                                // 累加当前目录的文件数量
                                result.TotalFileCount += 1;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "处理项目失败: {Path}", item.FullName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描目录时发生错误: {Path}", currentPath);
            }

            return result;
        }



        /// <summary>
        /// 检查SFTP文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="sftpConfig">SFTP配置信息</param>
        /// <returns></returns>
        private async Task<bool> CheckSftpFileExists(string filePath, CMSFramework.DAUEntities.DAUSftpConfig sftpConfig)
        {
            try
            {
                // 使用查询出来的SFTP服务器配置
                string sftpHost = sftpConfig.SftpAddress;
                int sftpPort = int.Parse(sftpConfig.SftpPort);
                string sftpUser = sftpConfig.SftpUsername;
                string sftpPwd = sftpConfig.SftpPassword;

                using var client = new SftpClient(sftpHost, sftpPort, sftpUser, sftpPwd);
                client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(30);

                await Task.Run(() => client.Connect());

                if (!client.IsConnected)
                {
                    return false;
                }

                var exists = client.Exists(filePath);
                client.Disconnect();

                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查SFTP文件是否存在时发生错误: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 从SFTP下载文件到临时目录
        /// </summary>
        /// <param name="sftpFilePath">SFTP文件路径</param>
        /// <param name="sftpConfig">SFTP配置信息</param>
        /// <returns>本地临时文件路径</returns>
        private async Task<string?> DownloadSftpFile(string sftpFilePath, CMSFramework.DAUEntities.DAUSftpConfig sftpConfig)
        {
            try
            {
                // 使用查询出来的SFTP服务器配置
                string sftpHost = sftpConfig.SftpAddress;
                int sftpPort = int.Parse(sftpConfig.SftpPort);
                string sftpUser = sftpConfig.SftpUsername;
                string sftpPwd = sftpConfig.SftpPassword;

                using var client = new SftpClient(sftpHost, sftpPort, sftpUser, sftpPwd);
                client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(30);

                await Task.Run(() => client.Connect());

                if (!client.IsConnected)
                {
                    _logger.LogError("无法连接到SFTP服务器");
                    return null;
                }

                // 创建临时文件路径
                var tempDir = Path.GetTempPath();
                var fileName = Path.GetFileName(sftpFilePath);
                var tempFilePath = Path.Combine(tempDir, $"vibdata_{Guid.NewGuid()}_{fileName}");

                _logger.LogDebug("开始下载文件: {SftpPath} -> {TempPath}", sftpFilePath, tempFilePath);

                // 下载文件
                using var fileStream = System.IO.File.Create(tempFilePath);
                await Task.Run(() => client.DownloadFile(sftpFilePath, fileStream));

                client.Disconnect();

                _logger.LogDebug("文件下载完成: {TempPath}", tempFilePath);
                return tempFilePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载SFTP文件时发生错误: {SftpPath}", sftpFilePath);
                return null;
            }
        }

        #endregion

        /// <summary>
        /// 读取SQLite文件中的vibdata表数据
        /// </summary>
        /// <param name="path">SQLite文件路径</param>
        /// <returns>vibdata表的结构和数据</returns>
        [HttpGet("GetVibData")]
        public async Task<IActionResult> GetVibData(
            string parkID, 
            string path,
            [FromQuery] string? turbineID,
            [FromQuery] string? dauID)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                {
                    return BadRequest(ApiResponse<object>.Error("文件路径不能为空"));
                }

                _logger.LogInformation("开始读取SQLite文件: {Path}", path);

                // SFTP服务器配置
                //var sftp = DauManagement.GetDAUSftpConfigByParkID(parkID);
                DAUSftpConfig? sftp = null;
                // 如果不查询机组，则查询推送服务器的sftp配置（dau推送）
                if (string.IsNullOrEmpty(turbineID))
                {
                    sftp = DauManagement.GetDAUSftpConfigByParkID(parkID);
                }
                else
                {
                    sftp = DauManagement.GetDAUSftpConfigByTurbineID(turbineID, dauID);

                    // 查询dau的ip ，连接到网关
                    WindDAU windDAU = DauManagement.GetDAUNameById(turbineID,dauID);
                    if(windDAU != null)
                    {
                        sftp.SftpAddress = windDAU.IP;
                    }
                    else
                    {
                        return BadRequest(ApiResponse<object>.Error($"未查询到采集器配置"));
                    }

                }

                if (sftp == null)
                {
                    return BadRequest(ApiResponse<object>.Error($"未查询到sftp配置"));
                }

                // 检查文件是否存在（通过SFTP）
                if (!await CheckSftpFileExists(path, sftp))
                {
                    return BadRequest(ApiResponse<object>.Error($"文件不存在: {path}"));
                }

                // 下载SQLite文件到临时目录
                var tempFilePath = await DownloadSftpFile(path, sftp);
                if (string.IsNullOrEmpty(tempFilePath))
                {
                    return BadRequest(ApiResponse<object>.Error("下载文件失败"));
                }

                try
                {
                    // 读取SQLite文件中的vibdata表
                    var vibDataResponse = await ReadVibDataFromSqlite(tempFilePath);

                    _logger.LogInformation("成功读取vibdata表，共 {Count} 条记录", vibDataResponse.TotalCount);

                    // 直接返回数据，不使用ApiResponse包装
                    return Ok(vibDataResponse);
                }
                finally
                {
                    // 清理临时文件
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        try
                        {
                            System.IO.File.Delete(tempFilePath);
                            _logger.LogDebug("已删除临时文件: {TempPath}", tempFilePath);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "删除临时文件失败: {TempPath}", tempFilePath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取SQLite文件时发生错误: {Path}", path);
                return BadRequest(ApiResponse<object>.Error($"读取文件失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 从SQLite文件中读取vibdata表的结构和数据
        /// </summary>
        /// <param name="sqliteFilePath">SQLite文件路径</param>
        /// <returns></returns>
        private async Task<VibDataResponseDTO> ReadVibDataFromSqlite(string sqliteFilePath)
        {
            var response = new VibDataResponseDTO();

            var connectionString = $"Data Source={sqliteFilePath};Read Only=True;";

            using var connection = new SQLiteConnection(connectionString);
            await connection.OpenAsync();

            try
            {
                // 检查vibdata表是否存在
                var tableExistsQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='vibdata';";
                using var tableExistsCmd = new SQLiteCommand(tableExistsQuery, connection);
                var tableExists = await tableExistsCmd.ExecuteScalarAsync();

                if (tableExists == null)
                {
                    _logger.LogWarning("vibdata表不存在于SQLite文件中: {FilePath}", sqliteFilePath);
                    return response;
                }

                // 获取表结构信息
                var schemaQuery = "PRAGMA table_info(vibdata);";
                using var schemaCmd = new SQLiteCommand(schemaQuery, connection);
                using var schemaReader = await schemaCmd.ExecuteReaderAsync();

                while (await schemaReader.ReadAsync())
                {
                    var column = new VibDataColumnDTO
                    {
                        Name = schemaReader["name"].ToString() ?? "",
                        Type = schemaReader["type"].ToString() ?? "",
                        IsNullable = Convert.ToInt32(schemaReader["notnull"]) == 0,
                        IsPrimaryKey = Convert.ToInt32(schemaReader["pk"]) == 1
                    };
                    response.Columns.Add(column);
                }

                // 获取数据总数
                var countQuery = "SELECT COUNT(*) FROM vibdata;";
                using var countCmd = new SQLiteCommand(countQuery, connection);
                var totalCount = await countCmd.ExecuteScalarAsync();
                response.TotalCount = Convert.ToInt32(totalCount);

                // 获取表数据（限制返回前1000条记录以避免内存问题）
                var dataQuery = "SELECT * FROM vibdata LIMIT 1000;";
                using var dataCmd = new SQLiteCommand(dataQuery, connection);
                using var dataReader = await dataCmd.ExecuteReaderAsync();

                while (await dataReader.ReadAsync())
                {
                    var row = new Dictionary<string, object?>();

                    for (int i = 0; i < dataReader.FieldCount; i++)
                    {
                        var columnName = dataReader.GetName(i);
                        var value = dataReader.IsDBNull(i) ? null : dataReader.GetValue(i);

                        // 如果是wave列且数据为byte[]，则转换为float[]
                        if (columnName.Equals("wave", StringComparison.OrdinalIgnoreCase) && value is byte[] byteArray)
                        {
                            try
                            {
                                // 将byte[]转换为float[]
                                // 每个float占4个字节
                                if (byteArray.Length % 4 == 0)
                                {
                                    var floatArray = new float[byteArray.Length / 4];
                                    Buffer.BlockCopy(byteArray, 0, floatArray, 0, byteArray.Length);
                                    value = floatArray;
                                }
                                else
                                {
                                    _logger.LogWarning("wave列的字节数组长度不是4的倍数，无法转换为float数组。长度: {Length}", byteArray.Length);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "转换wave列的byte[]到float[]时发生错误");
                            }
                        }

                        row[columnName] = value;
                    }

                    response.Data.Add(row);
                }

                _logger.LogInformation("成功读取vibdata表: {ColumnCount} 列, {TotalCount} 条记录, 返回 {ReturnCount} 条",
                    response.Columns.Count, response.TotalCount, response.Data.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取SQLite数据时发生错误: {FilePath}", sqliteFilePath);
                throw;
            }

            return response;
        }

        /// <summary>
        /// 测试单个机组配置同步功能
        /// </summary>
        /// <param name="turbineId">机组ID</param>
        /// <param name="configFile">SQLite配置文件</param>
        /// <returns>同步结果</returns>
        [HttpPost("TestSyncSingleTurbineConfig")]
        public async Task<IActionResult> TestSyncSingleTurbineConfig([FromForm] string turbineId, [FromForm]IFormFile configFile)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(turbineId))
                {
                    return BadRequest("机组ID不能为空");
                }

                if (configFile == null || configFile.Length == 0)
                {
                    return BadRequest("配置文件不能为空");
                }

                // 验证文件扩展名
                var fileExtension = Path.GetExtension(configFile.FileName).ToLowerInvariant();
                if (fileExtension != ".db")
                {
                    return BadRequest("只支持.db格式的SQLite数据库文件");
                }

                _logger.LogInformation($"开始测试同步机组 {turbineId} 的配置，文件名: {configFile.FileName}，文件大小: {configFile.Length} 字节");

                // 将文件转换为字节数组
                byte[] configData;
                using (var memoryStream = new MemoryStream())
                {
                    await configFile.CopyToAsync(memoryStream);
                    configData = memoryStream.ToArray();
                }

                // 构造测试数据
                var testConfigs = new Dictionary<string, byte[]>
                {
                    { turbineId, configData }
                };

                var result = await _syncService.BatchSyncConfigs(testConfigs);

                _logger.LogInformation($"机组 {turbineId} 配置同步测试完成");

                return Ok(new
                {
                    Success = true,
                    Message = $"机组 {turbineId} 配置同步测试成功",
                    TurbineId = turbineId,
                    FileName = configFile.FileName,
                    FileSize = configFile.Length,
                    SyncResult = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"测试同步机组 {turbineId} 配置时发生错误");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = $"测试同步机组配置失败: {ex.Message}",
                    TurbineId = turbineId,
                    Error = ex.Message
                });
            }
        }

    }
}

