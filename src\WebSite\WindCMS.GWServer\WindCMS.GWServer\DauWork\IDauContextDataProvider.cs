﻿using CMSFramework.BusinessEntity;

namespace WindCMS.GWServer.DauWork;

public interface IDauContextDataProvider
{
    List<WindTurbine> AllWindTurbineList
    {
        get;
    }

    List<MeasDefinition> AllMDFList
    {
        get;
    }

    List<SVMUnit> AllSVMUnitList
    {
        get;
    }
    List<OilUnit> AllOilUnitList
    {
        get;
    }
    List<MCS> AllMcsList
    {
        get;
    }
}