﻿using CMSFramework.BusinessEntity;
using Newtonsoft.Json.Linq;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using Microsoft.AspNetCore.Mvc;

namespace WTCMSLive.WebSite.Controllers
{
    public class WindParkController : Controller
    {
        //
        // GET: /WindPark/
        public ActionResult Index(string windParkID)
        {
            //如果ID为null，取第一个风场处理
            WindPark windPark = null;
            TreeManager treeManager = new TreeManager();
            if (null == windParkID)
            {
                List<WindPark> parkList = DevTreeManagement.GetWindParkList();
                if (parkList != null && parkList.Count > 0)
                {
                    windPark = parkList.First();
                    windParkID = windPark.WindParkID;
                }
                else
                {
                    ViewData["leftTree"] = treeManager.GetTreeModel().ToJson();
                    return View("WindPark");
                }
            }
            else
            {
                windPark = DevTreeManagement.GetWindPark(windParkID);
            }
            //导航

            ViewData["NodePath"] = Utility.ToJson(new object[]
            {
                new []{Resources.Message.CompanyName,"/OverViewPage/Index"},
                new []{windPark.WindParkName,"/WindPark/Index/"+windParkID},
            });
            //机组列表做成
            DataManager manager = new DataManager();
            ViewBag.id = windParkID;
            string UserId = Request.Cookies["WindCMSUserName"];
            ViewData["turbineTable"] = manager.GetTurbineListTable(windParkID, UserId);
            ViewData["leftTree"] = treeManager.GetTreeModel().ToJson();
            if (windPark == null)
            {
                ViewBag.WindParkName = "请添加风场!";
            }
            else
            {
                //风场基本信息
                ViewBag.WindParkName = windPark.WindParkName;
                ViewBag.WTTotalNumber = windPark.WindTurbineList.Count;
                ViewBag.WindParkID = windPark.WindParkID;
                ViewBag.OperationalDate = windPark.OperationalDate.ToString("yyyy-MM-dd");
                ViewBag.ContactMan = windPark.ContactMan;
                ViewBag.Address = windPark.Address;
                ViewBag.ContactTel = windPark.ContactTel;
                ViewBag.PostCode = windPark.PostCode;
                ViewBag.Description = windPark.Description;
            }

            if (Request.IsAjaxRequest())
            {
                return PartialView("WindPark", ViewBag);
            }
            return View("WindPark");
        }

        public JsonResult GetAlarmRecord(string windparkId, DateTime begintime, DateTime endtime)
        {
            // 读取指定风场的报警事件

            List<AlarmRecord> recordList = new List<AlarmRecord>();
            List<AlarmEvent> eventList = DevRTStateManagement.GetAlarmEventList(windparkId);
            List<WindTurbine> turList = DevTreeManagement.GetTurbinesList();
            eventList = eventList.Where(item => item.AlarmTime >= begintime && item.AlarmTime <= endtime.AddDays(1))
                .OrderByDescending(item => item.AlarmTime).ToList();
            for (int i = 0; i < eventList.Count; i++)
            {
                if (eventList[i].AlarmDegree == 3)
                    continue;
                AlarmRecord record = new AlarmRecord
                {
                    WindTurbineID = turList.Find(item => item.WindTurbineID == eventList[i].WindTurbineID).WindTurbineName,
                    AlarmDescription = AppFramework.Utility.EnumHelper.GetDescription((EnumAlarmDegree)eventList[i].AlarmDegree),
                    AlarmTime = eventList[i].AlarmTime
                };
                recordList.Add(record);
            }
            //填充假数据
            //for (int i = 0; i < 10; i++)
            //{
            //    AlarmRecord record = new AlarmRecord
            //    {
            //        WindTurbineID = "机组" + i,
            //        AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
            //        AlarmTime = DateTime.Now.AddHours(i)
            //    };
            //    recordList.Add(record);
            //}

            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "WindParkAlarm";
            List<Rows> rows = new List<Rows>();
            for (int j = 0; j < recordList.Count; j++)
            {
                Rows cells = new Rows();
                cells.cells = GetWindParkAlarmListTableCell(recordList[j]);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return Json(tableModel);
        }

        private Cell[] GetWindParkAlarmListTableCell(AlarmRecord alarmRecord)
        {
            List<Cell> cellList = new List<Cell>();
            // Cell cell0 = new Cell();
            //cell0.displayValue = systemRunningLog.
            //时间
            Cell cell1 = new Cell();
            cell1.displayValue = alarmRecord.WindTurbineID;
            cellList.Add(cell1);
            //操作者
            Cell cell2 = new Cell();
            cell2.displayValue = alarmRecord.AlarmDescription.ToString();
            cellList.Add(cell2);
            //日志内容
            Cell cell3 = new Cell();
            cell3.displayValue = alarmRecord.AlarmTime.ToString();
            cellList.Add(cell3);
            return cellList.ToArray();
        }

        #region 风场分组
        /// <summary>
        /// 设置风场分组
        /// </summary>
        /// <param name="parkid"></param>
        /// <param name="num"></param>
        /// <returns></returns>
        public string SetParkGroupNum(string parkid,int groupNum)
        {
            string message = "state:{0},msg:'{1}'";
            DevWindParkGroupManager dg = new DevWindParkGroupManager(ConfigInfo.DBConnName);
            dg.EditWindParkGroup(parkid, groupNum);
            message = string.Format(message, 1, "OK");
            return "{" + message + "}";
        }

        public string GetParkGroupData(string parkid)
        {
            DevWindParkGroupManager dg = new DevWindParkGroupManager(ConfigInfo.DBConnName);
            var groupdata = dg.GetWindParkGroup(parkid);
            if (groupdata != null && groupdata.GroupNum != 0)
            {
                JObject res = new JObject
                {
                    new JProperty("groupNum", groupdata.GroupNum)
                };

                // 机组分组
                var park = DevTreeManagement.GetWindPark(parkid);
                List<List<string>> ArrayList = park.WindTurbineList.Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / groupdata.GroupNum)
                .Select(x => x.Select(v => v.Value.WindTurbineName).ToList())
                .ToList();

                JArray jarr = new JArray();
                for(int i = 0; i < ArrayList.Count; i++)
                {
                    jarr.Add(new JObject()
                    {
                        { "key",$"组合{i+1}"},
                        { "value",string.Join(",",ArrayList[i])},
                    });
                }

                res.Add(new JProperty("data", jarr));

                return res.ToString();

            }

            return "{" + string.Format("state:{0},msg:'{1}'", 0, "未配置分组") + "}";

        }
        #endregion
    }
}
