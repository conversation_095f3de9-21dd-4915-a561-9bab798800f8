namespace WTCMSLive.WebSite.Core.DTOs
{
    /// <summary>
    /// 风场设备状态统计DTO
    /// </summary>
    public class DevStatusCountDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string WindParkName { get; set; }

        /// <summary>
        /// 机组状态统计
        /// </summary>
        public TurbineStatusSummaryDTO TurbineStatusSummary { get; set; }

        /// <summary>
        /// 机组详细状态列表（包含部件信息）
        /// </summary>
        public List<TurbineWithComponentsDTO> TurbineDetailList { get; set; } = new List<TurbineWithComponentsDTO>();

        /// <summary>
        /// 统计时间
        /// </summary>
        public DateTime StatisticsTime { get; set; }
    }

    /// <summary>
    /// 机组及其部件状态DTO
    /// </summary>
    public class TurbineWithComponentsDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 机组名称
        /// </summary>
        public string WindTurbineName { get; set; }

        /// <summary>
        /// 机组状态（3-正常，5-注意，6-危险）
        /// </summary>
        public int TurbineStatus { get; set; }

        /// <summary>
        /// 机组状态描述
        /// </summary>
        public string TurbineStatusDescription { get; set; }

        /// <summary>
        /// 机组状态更新时间
        /// </summary>
        public DateTime? TurbineStatusUpdateTime { get; set; }

        /// <summary>
        /// 该机组下的部件状态列表
        /// </summary>
        public List<ComponentStatusDTO> ComponentList { get; set; } = new List<ComponentStatusDTO>();
    }

    /// <summary>
    /// 部件状态DTO
    /// </summary>
    public class ComponentStatusDTO
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public string ComponentID { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; }

        /// <summary>
        /// 部件状态（3-正常，5-注意，6-危险）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 部件状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 部件状态更新时间
        /// </summary>
        public DateTime? StatusUpdateTime { get; set; }

        /// <summary>
        /// 机组ID（所属机组）
        /// </summary>
        public string WindTurbineID { get; set; }
    }
}
