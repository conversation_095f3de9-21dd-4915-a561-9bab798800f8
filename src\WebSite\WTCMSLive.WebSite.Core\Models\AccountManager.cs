﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class AccountManager
    {
        //取得系统用户列表
        public BaseTableModel GetUserList(bool edit)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<User> userList = UserManagement.GetUserList();
            tableModel.tableName = "UserList";
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < userList.Count; i++)
            {
                Rows cells = new Rows();
                cells.cells = CreateUserListTableCell(userList[i],edit);
                rows.Add(cells);
            }
            tableModel.rows = rows.ToArray();
            return tableModel;
        }

        private Cell[] CreateUserListTableCell(User user,bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //用户名
            Cell cell01 = new Cell();
            cell01.displayValue = user.UserID;
            //真实姓名
            Cell cell02 = new Cell();
            cell02.displayValue = user.UserName;
            //角色
            Cell cell03 = new Cell();
            cell03.displayValue = user.UserRole.RoleName;
            //邮箱
            Cell cell04 = new Cell();
            cell04.displayValue = user.Email;
            //电话
            Cell cell05 = new Cell();
            cell05.displayValue = user.Phone;
            //状态
            Cell cell06 = new Cell();
            cell06.displayValue = user.UserState;
            //编辑
            Cell cell07 = new Cell();
            if (edit)
            {
                cell07.type = "btn";
                cell07.displayValue = "编辑";
                cell07.onclick = "editUser('" + user.UserID + "')";
                cell07.style = "btn btn-default  btn-sm";
            }
            else { cell07.displayValue = "-"; }
            //删除
            Cell cell08 = new Cell();
            if (edit)
            {
                cell08.type = "btn";
                cell08.displayValue = "删除";
                cell08.onclick = "deleteUser('" + user.UserID + "')";
                cell08.style = "btn btn-default  btn-sm";
            }
            else {
                cell08.displayValue = "-";
            }
            //重置密码
            Cell cell09 = new Cell();
            if (edit)
            {
                cell09.type = "btn";
                cell09.displayValue = "重置";
                cell09.onclick = "resetUser('" + user.UserID + "')";
                cell09.style = "btn btn-default  btn-sm";
            }
            else {
                cell09.displayValue = "-";
            }
            cellList.Add(cell01);
            cellList.Add(cell02);
            cellList.Add(cell03);
            cellList.Add(cell04);
            cellList.Add(cell05);
            cellList.Add(cell06);
            cellList.Add(cell07);
            cellList.Add(cell08);
            cellList.Add(cell09);
            return cellList.ToArray();
        }
    }
}